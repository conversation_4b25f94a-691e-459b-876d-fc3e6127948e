<?php

if (!function_exists('translateText')) {
    function translateText($text)
    {
        return trans_static($text);
    }
}

if (!function_exists('trans_static')) {
    function trans_static($text, $locale = null)
    {
        $locale = $locale ?: session('locale', 'ps');

        // If locale is Pashto, return original text
        if ($locale === 'ps') {
            return $text;
        }

        // Try to get translation from language files
        try {
            // First try with the exact key
            $translation = trans('messages.' . $text, [], $locale);
            if ($translation !== 'messages.' . $text) {
                return $translation;
            }

            // If not found, try to get all messages and search
            $allMessages = trans('messages', [], $locale);
            if (is_array($allMessages) && isset($allMessages[$text])) {
                return $allMessages[$text];
            }

            return $text;
        } catch (Exception $e) {
            return $text;
        }
    }
}

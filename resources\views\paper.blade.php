
@extends('layouts/Main')

@section('title','Books')

@section('contents')
<div id="ssd">
 <h1>{{translateText('هغه څیړنې او ریسرچونه چی دروانی ناروغیو په اړه شوی دلته کتلی سی')}}</h1>

   <form action="{{ route('paper.search') }}" method="GET">
      <div id="search_sec">
         <button type="submit" id="serch_btn">
            <i class="fas fa-search"></i>
         </button>
         <input type="search" name="search" placeholder="{{trans_static('پلټنه وکړئ....')}}" value="{{ request('search') }}">
      </div>
   </form>

   <!-- ======== Cards Section ======== -->
   <section class="team-container">
      @if($papers->count() > 0)
         @foreach($papers as $index => $paper)
            <!-- Card {{ $index + 1 }} -->
            <div class="team-card {{ $index >= 4 ? 'hidden' : '' }}">
               @if($paper->A_Image && file_exists(public_path('imagese/' . $paper->A_Image)))
                  <img src="{{ asset('imagese/' . $paper->A_Image) }}" alt="{{ $paper->A_Title }}" class="top-img">
               @else
                  <img src="{{ asset('imagese/book1.jpg') }}" alt="{{ trans_static('مقاله') }}" class="top-img">
               @endif

               <div class="card-body">
                  <p>{{ $paper->A_catagory }}</p>
                  <h3>{{ $paper->A_Title }}</h3>
                  <p>{{ $paper->A_Description ?? 'دا مقاله د ذهني روغتیا اړوند مفید معلومات لري او د ټولنې د ذهني روغتیا د ښه کولو لپاره ګټوره ده.' }}</p>
               </div>

               <div class="card-footer">
                  <div class="profile">
                     @if($paper->A_Author_Image && file_exists(public_path('imagese/' . $paper->A_Author_Image)))
                        <img src="{{ asset('imagese/' . $paper->A_Author_Image) }}" alt="{{ $paper->A_Author }}" class="profile-img">
                     @else
                        <img src="{{ asset('imagese/author-default.jpg') }}" alt="{{ $paper->A_Author }}" class="profile-img">
                     @endif
                     <div>
                        <span>{{ $paper->A_Author }}</span>
                        <p>{{ \Carbon\Carbon::parse($paper->A_Publication_date)->format('Y,m,d') }}</p>
                     </div>
                  </div>
                  @if($paper->A_File && file_exists(public_path('imagese/' . $paper->A_File)))
                     <a href="{{ asset('imagese/' . $paper->A_File) }}" target="_blank">{{ trans_static('نور ولولئ') }}</a>
                  @else
                     <a href="{{ asset('book/دعاګاني.pdf') }}" target="_blank">{{ trans_static('نور ولولئ') }}</a>
                  @endif
               </div>
            </div>
         @endforeach
      @else
         <div class="no-books">
            <p>{{ trans_static('اوس مهال مقالې شتون نلري.') }}</p>
         </div>
      @endif
   </section>

   @if($papers->count() > 4)
      <!-- Show More Button -->
      <div style="text-align: center; margin-top: 20px;">
         <button id="showMoreBtn" class="show-more-btn">{{ trans_static('نور مقالې ښکاره کړئ') }}</button>
      </div>
   @endif
</div>
<div class="pagination">
    @php
        $current = $papers->currentPage();
        $last = $papers->lastPage();
    @endphp


    @if ($current > 1)
        <button onclick="goToPage({{ $current - 1 }})">مخکنی</button>
    @endif

  
    @for ($i = 1; $i <= $last; $i++)
        <button onclick="goToPage({{ $i }})" class="{{ $i == $current ? 'active' : '' }}">
            {{ $i }}
        </button>
    @endfor

 
    @if ($current < $last)
        <button onclick="goToPage({{ $current + 1 }})">ورستنی</button>
    @endif
</div>


</div>
<script>
function goToPage(page) {
    const url = new URL(window.location.href);
    url.searchParams.set('page', page);

    // Preserve search parameter when navigating between pages
    const search = document.querySelector('input[name="search"]')?.value;
    if (search) {
        url.searchParams.set('search', search);
    }

    window.location.href = url.toString();
}

document.addEventListener("DOMContentLoaded", function() {
    const cards = document.querySelectorAll('.team-card');
    const showMoreBtn = document.getElementById('showMoreBtn');

    if (showMoreBtn) {
        showMoreBtn.addEventListener('click', function() {
            cards.forEach(card => {
                card.classList.remove('hidden');
            });
            showMoreBtn.style.display = 'none';
        });
    }
});
</script>
@endsection

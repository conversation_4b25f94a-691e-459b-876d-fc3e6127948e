<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail Setup Guide - KU Mental Health</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-8">
            <h1 class="text-3xl font-bold mb-6 text-center text-gray-800">Gmail Email Setup Guide</h1>
            
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                <h3 class="font-bold">Current Issue:</h3>
                <p>Gmail is rejecting authentication because you need to set up an App Password.</p>
            </div>
            
            <div class="space-y-6">
                <div class="bg-blue-50 p-6 rounded-lg">
                    <h2 class="text-xl font-bold mb-4 text-blue-800">Step 1: Enable 2-Factor Authentication</h2>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>Go to <a href="https://myaccount.google.com/security" target="_blank" class="text-blue-600 underline">Google Account Security</a></li>
                        <li>Sign in with your Gmail account: <strong><EMAIL></strong></li>
                        <li>Find "2-Step Verification" section</li>
                        <li>Click "Get Started" and follow the setup process</li>
                        <li>Use your phone number to receive verification codes</li>
                    </ol>
                </div>
                
                <div class="bg-green-50 p-6 rounded-lg">
                    <h2 class="text-xl font-bold mb-4 text-green-800">Step 2: Generate App Password</h2>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>After enabling 2FA, go back to <a href="https://myaccount.google.com/security" target="_blank" class="text-blue-600 underline">Google Account Security</a></li>
                        <li>Look for "App passwords" section (it only appears after 2FA is enabled)</li>
                        <li>Click on "App passwords"</li>
                        <li>Select "Mail" from the dropdown</li>
                        <li>Select "Other (Custom name)" and type "KU Mental Health"</li>
                        <li>Click "Generate"</li>
                        <li><strong>Copy the 16-character password</strong> (it looks like: abcd efgh ijkl mnop)</li>
                    </ol>
                </div>
                
                <div class="bg-yellow-50 p-6 rounded-lg">
                    <h2 class="text-xl font-bold mb-4 text-yellow-800">Step 3: Update .env File</h2>
                    <p class="mb-4">Replace <code class="bg-gray-200 px-2 py-1 rounded">REPLACE_WITH_YOUR_APP_PASSWORD</code> in your .env file with the 16-character password from Step 2.</p>
                    
                    <div class="bg-gray-800 text-green-400 p-4 rounded font-mono text-sm">
                        <div class="mb-2"># Your .env file should look like this:</div>
                        <div>MAIL_MAILER=smtp</div>
                        <div>MAIL_HOST=smtp.gmail.com</div>
                        <div>MAIL_PORT=587</div>
                        <div>MAIL_USERNAME=<EMAIL></div>
                        <div class="text-yellow-400">MAIL_PASSWORD=abcd efgh ijkl mnop  # ← Your 16-character app password</div>
                        <div>MAIL_ENCRYPTION=tls</div>
                        <div>MAIL_FROM_ADDRESS=<EMAIL></div>
                        <div>MAIL_FROM_NAME="KU Mental Health"</div>
                    </div>
                </div>
                
                <div class="bg-purple-50 p-6 rounded-lg">
                    <h2 class="text-xl font-bold mb-4 text-purple-800">Step 4: Clear Cache and Test</h2>
                    <ol class="list-decimal list-inside space-y-2 text-gray-700">
                        <li>Open command prompt in your project folder</li>
                        <li>Run: <code class="bg-gray-200 px-2 py-1 rounded">php artisan config:clear</code></li>
                        <li>Go to <a href="/test-email" class="text-blue-600 underline">Email Test Page</a></li>
                        <li>Send a test email to yourself</li>
                        <li>Check your inbox for the test email</li>
                    </ol>
                </div>
                
                <div class="bg-gray-50 p-6 rounded-lg">
                    <h2 class="text-xl font-bold mb-4 text-gray-800">Important Notes:</h2>
                    <ul class="list-disc list-inside space-y-2 text-gray-700">
                        <li><strong>Never use your regular Gmail password</strong> - only use the App Password</li>
                        <li>The App Password is 16 characters with spaces (keep the spaces)</li>
                        <li>If you can't find "App passwords", make sure 2FA is properly enabled</li>
                        <li>Each app password can only be viewed once, so copy it immediately</li>
                        <li>You can generate multiple app passwords if needed</li>
                    </ul>
                </div>
                
                <div class="text-center space-x-4">
                    <a href="/test-email" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors inline-block">
                        Test Email Configuration
                    </a>
                    <a href="/feedbackd" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors inline-block">
                        Go to Feedback System
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

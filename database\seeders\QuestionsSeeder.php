<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class QuestionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if questions table is empty
        if (DB::table('questions')->count() === 0) {
            $questions = [
                [
                    'question_text' => 'آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟',
                    'question_type' => 'mental',
                    'A' => 'هیڅ نه',
                    'B' => 'لږه اندازه',
                    'C' => 'لږه ډېره اندازه',
                    'D' => 'ډېره اندازه'
                ],
                [
                    'question_text' => 'آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي د تقویت درمل ته اړتیا لري؟',
                    'question_type' => 'physical',
                    'A' => 'هیڅ نه',
                    'B' => 'لږه اندازه',
                    'C' => 'لږه ډېره اندازه',
                    'D' => 'ډېره اندازه'
                ],
                [
                    'question_text' => 'آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دي د سستي او کمزوري احساس کړی؟',
                    'question_type' => 'physical',
                    'A' => 'هیڅ نه',
                    'B' => 'لږه اندازه',
                    'C' => 'لږه ډېره اندازه',
                    'D' => 'ډېره اندازه'
                ],
                [
                    'question_text' => 'آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې د ناروغۍ احساس کړی؟',
                    'question_type' => 'physical',
                    'A' => 'هیڅ نه',
                    'B' => 'لږه اندازه',
                    'C' => 'لږه ډېره اندازه',
                    'D' => 'ډېره اندازه'
                ],
                [
                    'question_text' => 'آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه د سر درد لري؟',
                    'question_type' => 'physical',
                    'A' => 'هیڅ نه',
                    'B' => 'لږه اندازه',
                    'C' => 'لږه ډېره اندازه',
                    'D' => 'ډېره اندازه'
                ],
                [
                    'question_text' => 'آیا له دیرشو(۳۰) ورځو راهیسي د تشویش له امله بې خوبه شوی یې؟',
                    'question_type' => 'mental',
                    'A' => 'هیڅ نه',
                    'B' => 'لږه اندازه',
                    'C' => 'لږه ډېره اندازه',
                    'D' => 'ډېره اندازه'
                ],
                [
                    'question_text' => 'آیا د خوب په منځ کې راویښیږې او خوب دې ګډوډیږي؟',
                    'question_type' => 'mental',
                    'A' => 'هیڅ نه',
                    'B' => 'لږه اندازه',
                    'C' => 'لږه ډېره اندازه',
                    'D' => 'ډېره اندازه'
                ],
                [
                    'question_text' => 'آیا همېشه د فشار لاندې یې؟',
                    'question_type' => 'mental',
                    'A' => 'هیڅ نه',
                    'B' => 'لږه اندازه',
                    'C' => 'لږه ډېره اندازه',
                    'D' => 'ډېره اندازه'
                ],
                [
                    'question_text' => 'آیا د کورنۍ غړو سره ښه اړیکه لرئ؟',
                    'question_type' => 'social',
                    'A' => 'ډېره ښه',
                    'B' => 'ښه',
                    'C' => 'معمولي',
                    'D' => 'بده'
                ],
                [
                    'question_text' => 'آیا د ټولنې سره ښه اړیکه لرئ؟',
                    'question_type' => 'social',
                    'A' => 'ډېره ښه',
                    'B' => 'ښه',
                    'C' => 'معمولي',
                    'D' => 'بده'
                ]
            ];
            
            foreach ($questions as $question) {
                DB::table('questions')->insert([
                    'question_text' => $question['question_text'],
                    'question_type' => $question['question_type'],
                    'A' => $question['A'],
                    'B' => $question['B'],
                    'C' => $question['C'],
                    'D' => $question['D'],
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }
    }
}



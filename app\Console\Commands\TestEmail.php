<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test {email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test email configuration by sending a test email';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $email = $this->argument('email');
        
        $this->info('Testing email configuration...');
        $this->info('Sending test email to: ' . $email);
        
        try {
            Mail::raw('This is a test email from KU Mental Health System. If you receive this, your email configuration is working correctly!', function ($message) use ($email) {
                $message->to($email)
                        ->subject('Test Email - KU Mental Health System');
            });
            
            $this->info('✅ Email sent successfully!');
            $this->info('Check your inbox at: ' . $email);
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('❌ Email failed to send!');
            $this->error('Error: ' . $e->getMessage());
            
            $this->info('');
            $this->info('Troubleshooting tips:');
            $this->info('1. Check your .env file configuration');
            $this->info('2. Make sure you have internet connection');
            $this->info('3. Verify your email credentials');
            $this->info('4. Check storage/logs/laravel.log for detailed errors');
            
            return Command::FAILURE;
        }
    }
}

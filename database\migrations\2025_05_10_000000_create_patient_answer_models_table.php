<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('patient_answer_models', function (Blueprint $table) {
            $table->bigIncrements('Answer_Id');
            $table->unsignedBigInteger('Patient_Id');
            $table->unsignedBigInteger('Question_Id');
            $table->string('Selected_Answer'); // A, B, C, or D
            // $table->integer('Score')->default(0);
            
            $table->foreign('Patient_Id')->references('Patient_id')->on('patient_models')->onDelete('cascade');
            $table->foreign('Question_Id')->references('Q_Id')->on('questioner_models')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('patient_answer_models');
    }
};
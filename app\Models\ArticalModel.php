<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ArticalModel extends Model
{


    protected $table = 'artical_models';
    protected $primaryKey = 'A_Id';

    protected $fillable = [
        'A_Title', 'A_Author', 'A_Publication_date', 'A_Language', 'A_Type', 'A_catagory', 'A_Image', 'A_Description', 'A_Author_Image', 'user_id','A_File',
    ];


       public function scopeSearchableFields($query, $term)
    {
        return $query->where('A_Title', 'LIKE', "%$term%")
            ->orWhere('A_Author', 'LIKE', "%$term%")
            ->orWhere('A_catagory', 'LIKE', "%$term%")
            ->orWhere('A_Type', 'LIKE', "%$term%");
    }








}







/* 🌟 BEAUTIFUL QUESTION BANK STYLES 🌟 */

/* Main Container */
#fgfgfgfg {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 2rem;
  direction: rtl;
  margin-top: 5rem;
}

/* Beautiful Form Card */
.beautiful-form-card {
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Form Header */
.form-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  text-align: center;
  color: white;
}

.header-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.form-header h2 {
  font-size: 2rem;
  margin: 0 0 0.5rem 0;
  font-weight: 700;
}

.form-header p {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

/* Form Container */
#mentalForm {
  padding: 2rem;
  background: transparent;
}

/* Form Steps */
.form-step {
  display: none;
}

.form-step.active {
  display: block;
}

/* Beautiful Labels */
#mentalForm label {
  display: block;
  margin-bottom: 1.5rem;
  font-weight: 600;
  color: #4a5568;
  font-size: 1rem;
}

#mentalForm label i {
  color: #667eea;
  margin-left: 0.5rem;
  font-size: 1.1rem;
}

/* Beautiful Inputs */
#mentalForm input[type="text"],
#mentalForm input[type="number"],
#mentalForm input[type="number"],
#mentalForm input[type="tel"],
#mentalForm input[type="email"],
#mentalForm select {
  width: 100%;
  padding: 1rem;
  margin-top: 0.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  color: black;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

#mentalForm input:focus,
#mentalForm select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-2px);
  background: white;
}

/* Gender Selection */
.gender-label {
  margin-bottom: 1rem !important;
}

.gender-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.gender-option {
  margin: 0 !important;
}

.gender-option input[type="radio"] {
  display: none;
}

.gender-card {
  padding: 1.5rem;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.gender-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.gender-card.male {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}

.gender-card.female {
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
}

.gender-option input:checked + .gender-card {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.05);
}

.gender-card i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.gender-card span {
  font-weight: 600;
  font-size: 1.1rem;
}

/* Location Group */
.location-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* Beautiful Button */
.beautiful-button {
  width: 100%;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  margin-top: 2rem;
}

.beautiful-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.beautiful-button i {
  margin-left: 0.5rem;
}

/* Error Messages */
.error-msg {
  color: #e53e3e;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  padding: 0.5rem;
  background: rgba(229, 62, 62, 0.1);
  border-radius: 8px;
  border-left: 4px solid #e53e3e;
}

/* Responsive Design */
@media (max-width: 768px) {
  #fgfgfgfg {
    padding: 1rem;
  }

  .beautiful-form-card {
    margin: 0;
    border-radius: 15px;
  }

  .form-header {
    padding: 1.5rem;
  }

  .header-icon {
    font-size: 2.5rem;
  }

  .form-header h2 {
    font-size: 1.5rem;
  }

  #mentalForm {
    padding: 1.5rem;
  }

  .gender-options {
    grid-template-columns: 1fr;
  }

  .location-group {
    grid-template-columns: 1fr;
  }
}
    *{
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
 body{
  /* background-color: #1d2a3a; */
  font-family: '<PERSON><PERSON>', serif;
 }

.lang-menu {
  position: absolute;
  margin-top: 8px;
  width: 112px;
  /* background-color: #212d40; */
background-color: royalblue;
  border: 2px solid #e5e7eb; 
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 50;
}
.lang-btn {
  /* background-color: #212d40; */
  color: white;
  border-radius: 6px;
  cursor: pointer;
  border: none;
  font-size: 18px ;
  font-weight: bold;
}
.lang-menu a {
  display: block;
  padding: 8px 16px;
  font-size: 14px;
  color: #374151;
  text-decoration: none;
}
.lang-menu a:hover {
  background-color: #f3f4f6; 
}
.hidden {
  display: none;
}
/* ============== */

.login-menu {
  position: absolute;
  margin-top: 8px;
  width: 112px;
  /* background-color: #212d40; */
  background-color: royalblue;
  border: 2px solid #e5e7eb; 
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 50;
}
.login-btn {
  /* background-color: #212d40; */
  color: white;
  border-radius: 6px;
  cursor: pointer;
  border: none;
  font-size: 18px ;
  font-weight: bold;
}
.login-menu a {
  display: block;
  padding: 8px 16px;
  font-size: 14px;
  color: #374151;
  text-decoration: none;
}
.login-menu a:hover {
  background-color: #f3f4f6; 
}
.hidden {
  display: none;
}
  /* ======Navbar=============  */
  .navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* background-color: #212d40; */
    padding: 0 10px;
    width: 100%;
    position: fixed;
    top: 0;
    height: 4.5em;
    z-index: 100;
  }
  .logo img {
  width: 60px;
  height: 60px;
  margin-bottom: 10px;
  margin-top: 10px;
  margin-left: 7.5em;
  border-radius: 50%;

  }
  
  .menu-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    display: none;

  }
  ul{
    margin-right: 3em;
    /* top: 2px; */
    /* background-color: rgba(20, 0, 0, 0.5); */
    background-color: rgb(99, 7, 99);
    padding: 10px ;
    margin-bottom: 1.3em;
    /* border: 2px solid rgb(151, 13, 151); */
    border-radius: 15px;
 
  }
  .menu-toggle:hover{
    border: 1px solid  white;
  }
  .nav-links {
    list-style: none;
    display: flex;
    padding-right: 20px;
  }
  
  .nav-links li {
    margin-left: 20px;
  }
  
  .nav-links a {
    color: white;
    text-decoration: none;
    font-size: 18px;
    font-weight: bold;
    padding-right: 10px;
    position: relative;
    transition: all 0.4s ease;
    direction: rtl;
  }
  .nav-links a::before{
    content: '';
    position: absolute;
    width: 0;
    left: 0;
    bottom: 0;
    height: 2px;
    background-color: rgb(198, 83, 83);
    transition: all 0.4s ease-in-out;
  }
  .nav-links a:hover::before{
    width: 100%; 
  }
  .nav-links a:hover,a.activ{
    color:rgb(198, 83, 83);
  }
  
  /* Responsive Settings */
  @media (max-width: 768px) {
    .navbar{
      width: 100%;
    }
    .menu-toggle {
      display: block;
    }
  
    .nav-links {
      display: none;
      flex-direction: column;
      width: 60%;
      height: auto;
      background-color: #212d40;
      position: absolute;
      top: 70px;
      right: 0;
    }
  
    .nav-links.active {
      display: flex;
    }
    .nav-links a{
      padding-top: 2em;
      box-shadow: 0 0 5px rgb(65, 47, 47);
      transition: all 0.4s ease-in-out;
    }
    .nav-links li {

  text-align: center;
      margin: 10px 0;
    }
  }
  
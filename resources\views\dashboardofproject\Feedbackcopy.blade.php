@extends('layouts/Admin')

@section('title','Feedback Copy')

@section('contents')
<div id="ssd" class="p-4 md:p-8 min-h-screen transition-all w-full bg-gradient-to-br from-blue-50 to-indigo-50">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div class="flex items-center mb-4 md:mb-0">
            <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300">
                <i class="fas fa-comment-dots text-white text-xl"></i>
            </div>
            <h1 class="text-2xl md:text-3xl font-bold text-gray-800 ml-4">{{ translateText('د نظرونو مدیریت') }}</h1>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('feedback.index') }}" class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-300">
                <i class="fas fa-sync-alt mr-2"></i> {{ translateText('تازه کول') }}
            </a>
        </div>
    </div>

    <!-- Professional Filter Section -->
    <div class="bg-white rounded-2xl shadow-xl mb-8 border-0 overflow-hidden transform transition-all duration-300 hover:shadow-2xl">
        <div class="bg-gray-200 px-6 py-4">
            <h2 class="text-xl md:text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-filter mr-2"></i> {{ translateText('د نظرونو فلټر کول') }}
            </h2>
        </div>

        <div class="p-6">
            <form action="/feedbackd" method="GET" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د ایمیل له مخې پلټنه') }}</label>
                        <input type="text" name="search" value="{{ request('search') }}" placeholder="{{ translateText('د ایمیل ادرس ولیکئ...') }}"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                    </div>
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د نیټې له مخې پلټنه') }}</label>
                        <input type="date" name="date" value="{{ request('date') }}"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                    </div>
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د محتوا له مخې پلټنه') }}</label>
                        <input type="text" name="content" value="{{ request('content') }}" placeholder="{{ translateText('د نظر محتوا ولیکئ...') }}"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                    </div>
                </div>
                <div class="flex flex-col md:flex-row gap-4 pt-4">
                    <button type="submit"
                        class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                        <i class="fas fa-filter mr-2"></i> {{ translateText('فلټر کول') }}
                    </button>
                    <a href="/feedbackd"
                        class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                        <i class="fas fa-undo mr-2"></i> {{ translateText('بیا تنظیمول') }}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Alerts Section -->
    @if(session('success'))
    <div class="bg-green-50 border-r-4 border-green-500 text-green-700 p-4 mb-6 rounded-lg shadow-md flex items-center justify-between" role="alert">
        <div class="flex items-center">
            <div class="bg-green-500 rounded-full p-1 mr-3">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <p class="font-medium">{{ session('success') }}</p>
        </div>
        <button onclick="this.parentElement.remove()" class="text-green-700 hover:text-green-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-50 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-lg shadow-md flex items-center justify-between" role="alert">
        <div class="flex items-center">
            <div class="bg-red-500 rounded-full p-1 mr-3">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="font-medium">{{ session('error') }}</p>
        </div>
        <button onclick="this.parentElement.remove()" class="text-red-700 hover:text-red-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>
    @endif

    <!-- Feedback Form -->
    <div class="bg-white rounded-2xl shadow-xl mb-8 border border-blue-100 overflow-hidden transform transition-all duration-300 hover:shadow-2xl">
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h2 class="text-xl md:text-2xl font-bold text-white flex items-center">
                <i class="fas fa-edit mr-2"></i> {{ translateText('نوی نظرونه دلته ثبت کړئ') }}
            </h2>
        </div>

        <div class="p-6">
            <div class="bg-blue-50 border-r-4 border-blue-500 text-blue-700 p-4 mb-6 rounded-lg shadow-md" role="alert">
                <div class="flex items-center">
                    <div class="bg-blue-500 rounded-full p-1 mr-3">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <p class="font-medium">{{ translateText('نوټ: د نظر ثبتولو سره به ایمیل هم ولیږل شي.') }}</p>
                </div>
            </div>

            <form method="POST" action="{{ isset($editFeedback) ? route('feedback.update', $editFeedback->Feedback_Id) : route('feedback.store') }}" class="space-y-6">
                @csrf
                @if(isset($editFeedback))
                    @method('PUT')
                @endif

                <div>
                    <label for="recipient_email" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-envelope mr-2 text-blue-600"></i>
                        {{ translateText('د لېږلو ایمیل') }}
                    </label>

                    <!-- Patient Email Dropdown -->
                    <div class="mb-3">
                        <select name="recipient_email" id="recipient_email" class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 bg-white">
                            <option value="">{{ translateText('د ناروغ ایمیل وټاکئ') }}</option>

                            @if(isset($patients) && count($patients) > 0)
                                @foreach($patients as $patient)
                                    <option value="{{ $patient->Patient_email }}"
                                        {{ isset($editFeedback) && $editFeedback->Recever_email == $patient->Patient_email ? 'selected' : '' }}
                                        data-patient-name="{{ $patient->Patiet_Name ?? 'ناروغ' }}">
                                        {{ $patient->Patiet_Name ?? 'ناروغ' }} - {{ $patient->Patient_email }}
                                    </option>
                                @endforeach
                            @else
                                <option value="" disabled class="text-gray-500">{{ translateText('هیڅ ناروغ د ایمیل سره موجود نه دی') }}</option>
                            @endif
                        </select>
                        <p class="text-xs text-gray-500 mt-1">
                            <i class="fas fa-info-circle mr-1"></i>
                            {{ translateText('د ثبت شوي ناروغانو ایمیلونه') }}
                        </p>
                    </div>

                    <!-- Custom Email Input -->
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-at text-gray-400"></i>
                        </div>
                        <input type="email" name="custom_email" id="custom_email"
                            placeholder="{{ translateText('یا نوی ایمیل دلته ولیکئ...') }}"
                            class="w-full pl-10 p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
                            {{ isset($editFeedback) && !in_array($editFeedback->Recever_email, $patients->pluck('Patient_email')->toArray()) ? 'value=' . $editFeedback->Recever_email : '' }}>
                        <p class="text-xs text-gray-500 mt-1">
                            <i class="fas fa-plus-circle mr-1"></i>
                            {{ translateText('د نوي ایمیل ادرس لپاره') }}
                        </p>
                    </div>
                </div>

                <div>
                    <label for="Contents" class="block text-sm font-medium text-gray-700 mb-2">{{ translateText('د نظر محتوا') }}</label>
                    <textarea name="Contents" id="Contents" rows="5" class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300">{{ isset($editFeedback) ? $editFeedback->Contents : '' }}</textarea>
                </div>

                <div>
                    <button type="submit" class="w-full py-3 px-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-bold rounded-lg shadow-md hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:-translate-y-1">
                        <i class="fas fa-paper-plane mr-2"></i> {{ isset($editFeedback) ? translateText('تازه کول او ایمیل کول') : translateText('ثبت او ایمیل کول') }}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Feedback List -->
    <div class="bg-white rounded-2xl shadow-xl p-6 border border-blue-100 overflow-hidden">
        <h3 class="text-xl font-bold mb-6 text-gray-800 border-b-2 border-blue-500 pb-2 flex items-center">
            <i class="fas fa-list-ul mr-2 text-blue-600"></i>
            {{ translateText('ثبت شوي نظرونه') }}
        </h3>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
                        <th class="p-3 text-right">{{ translateText('محتوا') }}</th>
                        <th class="p-3 text-right">{{ translateText('د ترلاسه کوونکي ایمیل') }}</th>
                        <th class="p-3 text-right">{{ translateText('نیټه') }}</th>
                        <th class="p-3 text-right">{{ translateText('عملیې') }}</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    @forelse($feedbacks as $feedback)
                        <tr class="hover:bg-blue-50 transition-colors">
                            <td class="p-3 text-gray-700 content-cell">{{ $feedback->Contents }}</td>
                            <td class="p-3">{{ $feedback->Recever_email ?? 'N/A' }}</td>
                            <td class="p-3">{{ $feedback->created_at->format('Y-m-d') }}</td>
                            <td class="p-3">
                                <div class="flex gap-2 justify-end">
                                    <a href="{{ route('feedback.edit', $feedback->Feedback_Id) }}" class="px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors shadow-sm hover:shadow flex items-center">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('feedback.destroy', $feedback->Feedback_Id) }}" method="POST" class="inline" onsubmit="return confirm('{{ translateText('آیا ډاډه یاست چې دا نظر له منځه یوسي؟') }}');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors shadow-sm hover:shadow flex items-center">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="4" class="p-4 text-center text-gray-500">{{ translateText('هیڅ نظر شتون نه لري') }}</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="mt-6">
            {{ $feedbacks->links() }}
        </div>
    </div>
</div>

<style>
    /* Fixed height for content cells with ellipsis for overflow */
    .content-cell {
        max-width: 250px;
        max-height: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Show full content on hover */
    .content-cell:hover {
        white-space: normal;
        overflow: visible;
        position: relative;
        z-index: 10;
        background-color: #f9fafb;
        border-radius: 4px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        padding: 8px;
        transition: all 0.3s ease;
        min-width: 300px;
        max-height: none;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const patientEmailSelect = document.getElementById('recipient_email');
    const customEmailInput = document.getElementById('custom_email');

    // Clear custom email when patient email is selected
    patientEmailSelect.addEventListener('change', function() {
        if (this.value) {
            customEmailInput.value = '';
            customEmailInput.style.backgroundColor = '#f9fafb';
        } else {
            customEmailInput.style.backgroundColor = '#ffffff';
        }
    });

    // Clear patient email selection when custom email is typed
    customEmailInput.addEventListener('input', function() {
        if (this.value) {
            patientEmailSelect.value = '';
            patientEmailSelect.style.backgroundColor = '#f9fafb';
        } else {
            patientEmailSelect.style.backgroundColor = '#ffffff';
        }
    });

    // Form validation
    const form = document.querySelector('form[action*="feedback"]');
    if (form) {
        form.addEventListener('submit', function(e) {
            const patientEmail = patientEmailSelect.value;
            const customEmail = customEmailInput.value;
            const content = document.getElementById('Contents').value;

            if (!patientEmail && !customEmail) {
                e.preventDefault();
                alert('{{ translateText("لطفۍ د ترلاسه کوونکي ایمیل ادرس وټاکئ یا ولیکئ.") }}');
                return false;
            }

            if (!content.trim()) {
                e.preventDefault();
                alert('{{ translateText("لطفۍ د نظر محتوا ولیکئ.") }}');
                return false;
            }

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>{{ translateText("د لېږلو په حال کې...") }}';
            submitBtn.disabled = true;

            // Re-enable button after 5 seconds (in case of slow response)
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 5000);
        });
    }
});
</script>

@endsection



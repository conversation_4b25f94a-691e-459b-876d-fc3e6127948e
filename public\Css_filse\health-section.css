
  body{
    background-color: #1d2a3a;
  }
#content_section{
  width: 70%;
  height: auto;
  margin: 8.5em auto ;
  background-color:white;
  line-height: 33px;
  color: black;
  border-radius: 20px;
}  
  body {
  margin: 0;
  padding: 0;
}

h1 {
  text-align: center;
  color: #004d80;
  /* padding: 20px auto; */
  margin: 20px auto;
}

.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.section {
  background-color: #ffffff;
  padding: 20px;
  text-align: center;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  max-height: 260px;
  overflow: hidden;
  transition: all 0.5s ease;
  position: relative;
}

.section.active {
  max-height: 2000px;
  overflow: visible;
}

.section img {
  width: 100%;
  height: auto;
  border-radius: 10px;
  margin-bottom: 15px;
  transition: transform 0.3s ease;
}

.section:hover img {
  transform: scale(1.05);
}

.description, .sub-description {
  display: none;
  background-color: #f9f9f9;
  padding: 12px;
  border-radius: 10px;
  margin-top: 15px;
  font-size: 14px;
  color: #333;
  text-align: right;
}

.sub-description {
  background-color: #eef7ff;
}

.sub-description img {
  width: 100%;
  max-width: 300px;
  height: auto;
  margin: 10px 0;
  border-radius: 8px;
}

.icon {
  font-size: 45px;
  color: #004d80;
  margin-bottom: 10px;
}
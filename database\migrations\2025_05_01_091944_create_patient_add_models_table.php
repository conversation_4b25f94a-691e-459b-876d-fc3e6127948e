<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('patient_add_models', function (Blueprint $table) {
            $table->bigIncrements('P_Add_Id');
            $table->unsignedBigInteger('Patient_Id'); // اضافه کول د Patient_Id ستون
          
            $table->string('P_Province');
            $table->string('P_Distract');
            $table->string('P_Village');
            $table->foreign('Patient_Id')->references('Patient_id')->on('patient_models')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('patient_add_models');
    }
};



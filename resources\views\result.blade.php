@extends('layouts/Main')

@section('title', 'Result')

@section('contents')
<div class="container mt-5">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h2 class="text-center">{{ translateText('د ازموینې پایله') }}</h2>
        </div>
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-md-6">
                    <h4>{{ translateText('شخصي معلومات') }}</h4>
                    <p><strong>{{ translateText('نوم:') }}</strong> {{ $patient->Patiet_Name }}</p>
                    <p><strong>{{ translateText('عمر:') }}</strong> {{ $patient->Patient_Age }}</p>
                    <p><strong>{{ translateText('جنسیت:') }}</strong> {{ $patient->Patient_Gender }}</p>
                    <p><strong>{{ translateText('تلیفون:') }}</strong> {{ $patient->Patient_phone }}</p>
                </div>
                <div class="col-md-6">
                    <h4>{{ translateText('د ازموینې معلومات') }}</h4>
                    <p><strong>{{ translateText('ډاکټر:') }}</strong> {{ $patient->doctor->Dr_Name }}</p>
                    <p><strong>{{ translateText('ټولټال نمبر:') }}</strong> {{ $patient->Total_Score }}</p>
                    <p><strong>{{ translateText('نېټه:') }}</strong> {{ $patient->created_at->format('Y-m-d') }}</p>
                </div>
            </div>
            
            <div class="result-interpretation mt-4">
                <h3 class="text-center mb-3">{{ translateText('د پایلې تفسیر') }}</h3>
                
                @php
                    $score = $patient->Total_Score;
                    $interpretation = '';
                    $color = '';
                    
                    if ($score >= 0 && $score <= 7) {
                        $interpretation = translateText('تاسو روغ یاست او کومه رواني ستونزه نلرئ.');
                        $color = 'success';
                    } elseif ($score >= 8 && $score <= 15) {
                        $interpretation = translateText('تاسو خفیفه رواني ستونزه لرئ. د ډاکټر سره مشوره وکړئ.');
                        $color = 'warning';
                    } elseif ($score >= 16 && $score <= 23) {
                        $interpretation = translateText('تاسو متوسطه رواني ستونزه لرئ. د ډاکټر سره مشوره وکړئ.');
                        $color = 'warning';
                    } else {
                        $interpretation = translateText('تاسو شدیده رواني ستونزه لرئ. ژر تر ژره د ډاکټر سره مشوره وکړئ.');
                        $color = 'danger';
                    }
                @endphp
                
                <div class="alert alert-{{ $color }} text-center">
                    <h4>{{ $interpretation }}</h4>
                </div>
            </div>
            
            <div class="answers mt-4">
                <h3 class="text-center mb-3">{{ translateText('د ازموینې پاسخونه') }}</h3>
                
                @forelse($patient->questions as $answer)
                    <div class="question-answer">
                        <p><strong>
                            @if(isset($answer->question) && $answer->question)
                                {{ $answer->question->Question_Text ?? $answer->Q_Discription ?? 'پوښتنه ' . $answer->Question_No }}
                            @else
                                {{ $answer->Q_Discription ?? 'پوښتنه ' . $answer->Question_No }}
                            @endif
                        </strong></p>
                        
                        <p>
                            @if($answer->A)
                                <span class="selected-answer">A: {{ $answer->A }}</span>
                            @endif
                            
                            @if($answer->B)
                                <span class="selected-answer">B: {{ $answer->B }}</span>
                            @endif
                            
                            @if($answer->C)
                                <span class="selected-answer">C: {{ $answer->C }}</span>
                            @endif
                            
                            @if($answer->D)
                                <span class="selected-answer">D: {{ $answer->D }}</span>
                            @endif
                        </p>
                    </div>
                @empty
                    <div class="alert alert-info text-center">
                        {{ translateText('د دې ناروغ لپاره پوښتنې نشته') }}
                    </div>
                @endforelse
            </div>
        </div>
    </div>
</div>
@endsection


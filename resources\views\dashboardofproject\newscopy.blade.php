@extends('layouts/Admin')

@section('title', 'News Copy')

@section('contents')
<!-- Main Content -->
<div class="p-4 md:p-8 min-h-screen transition-all w-full bg-white">
    <div class="container mx-auto max-w-6xl">
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-8">
            <div class="flex items-center mb-4 md:mb-0">
                <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-newspaper text-gray-700 text-xl"></i>
                </div>
                <h1 class="text-2xl md:text-3xl font-bold text-gray-800 mr-4">{{ translateText('د خبرونو مدیریت') }}</h1>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('news.index') }}" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                    <i class="fas fa-sync-alt mr-2"></i> {{ translateText('تازه کول') }}
                </a>
                <button id="filterToggleBtn" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                    <i class="fas fa-filter mr-2"></i> {{ translateText('فلټر') }}
                </button>
            </div>
        </div>

        <!-- Alert Messages -->
        @if(session('success'))
        <div class="bg-green-100 border-r-4 border-green-500 text-green-700 p-4 mb-6 rounded-lg shadow-md alert-dismissible fade show" role="alert" id="successAlert">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-500 text-xl"></i>
                </div>
                <div class="mr-3">
                    <p class="font-bold">{{ translateText('بریالیتوب!') }}</p>
                    <p>{{ session('success') }}</p>
                </div>
                <button type="button" class="mr-auto text-green-700 hover:text-green-900 focus:outline-none" onclick="document.getElementById('successAlert').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        @endif

        @if(session('error'))
        <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-lg shadow-md alert-dismissible fade show" role="alert" id="errorAlert">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
                </div>
                <div class="mr-3">
                    <p class="font-bold">{{ translateText('تېروتنه!') }}</p>
                    <p>{{ session('error') }}</p>
                </div>
                <button type="button" class="mr-auto text-red-700 hover:text-red-900 focus:outline-none" onclick="document.getElementById('errorAlert').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        @endif

        @if($errors->any())
        <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-4 rounded-md">
            <p class="font-bold">{{ translateText('تېروتنې') }}</p>
            <ul class="list-disc list-inside pl-6">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <!-- Filter Card - Hidden by default -->
        <div id="filterCard" class="bg-white rounded-2xl shadow-xl mb-8 border-0 overflow-hidden transform transition-all duration-300 hover:shadow-2xl hidden">
            <div class="bg-gray-200 px-6 py-4">
                <h2 class="text-xl md:text-2xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-filter mr-2"></i> {{ translateText('د خبرونو فلټر کول') }}
                </h2>
            </div>

            <div class="p-6">
                <form action="/newsd" method="GET" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د عنوان له مخې پلټنه') }}</label>
                            <input type="text" name="search" value="{{ request('search') }}" placeholder="{{ translateText('د خبر عنوان ولیکئ...') }}"
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                        </div>

                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د نیټې له مخې پلټنه') }}</label>
                            <input type="date" name="date" value="{{ request('date') }}"
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                        </div>
                    </div>
                    <div class="flex space-x-2 pt-4">
                        <button type="submit"
                            class="px-6 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black shadow-md transition-all duration-300 border-0 flex items-center justify-center">
                            <i class="fas fa-filter mr-2"></i> {{ translateText('فلټر کول') }}
                        </button>
                        <a href="/newsd"
                            class="px-6 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black shadow-md transition-all duration-300 border-0 flex items-center justify-center">
                            <i class="fas fa-undo mr-2"></i> {{ translateText('بیا تنظیمول') }}
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- News Form -->
        <div class="bg-white rounded-2xl shadow-xl mb-8 border-0 overflow-hidden transform transition-all duration-300 hover:shadow-2xl">
            <div class="bg-white px-6 py-4 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-newspaper mr-2 text-gray-600"></i>
                    {{ isset($editNews) ? translateText('د خبر تازه کول') : translateText('نوی خبر خپرول') }}
                </h2>
            </div>

            <div class="p-6">
                <!-- IMPORTANT: Fixed form with explicit method and action -->
                @if(isset($editNews))
                    <form action="{{ route('news.update', ['id' => $editNews->News_Id]) }}" method="POST" enctype="multipart/form-data">
                    @method('PUT')
                @else
                    <form action="{{ route('news.store') }}" method="POST" enctype="multipart/form-data">
                @endif
                    @csrf

                    <!-- Form fields -->
                    <div class="mb-4">
                        <label for="title" class="block text-sm font-medium text-gray-700 mb-2">{{ translateText('سرلیک') }} <span class="text-red-500">*</span></label>
                        <input type="text" name="title" id="title" value="{{ $editNews->News_Title ?? old('title') }}"
                               class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all"
                               placeholder="{{ translateText('د خبر سرلیک دلته ولیکئ...') }}" required>
                        @error('title')
                            <p class="text-red-500 text-xs mt-1">{{ translateText('سرلیک اړین دی') }}</p>
                        @enderror
                    </div>

                    <div class="mb-4">
                        <label for="contents" class="block text-sm font-medium text-gray-700">{{ translateText('منځپانګه') }}</label>
                        <textarea name="contents" id="contents" rows="5" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">{{ $editNews->News_Discription ?? old('contents') }}</textarea>
                        @error('contents')
                            <p class="text-red-500 text-xs mt-1">{{ translateText('منځپانګه اړینه ده') }}</p>
                        @enderror
                    </div>

                    <div class="mb-4">
                        <label for="image" class="block text-sm font-medium text-gray-700">{{ translateText('انځور') }}</label>
                        @if(isset($editNews) && $editNews->New_Images)
                            <div class="mt-2 mb-4">
                                <img src="{{ asset('imagese/' . $editNews->New_Images) }}" alt="Current Image" class="w-32 h-32 object-cover">
                                <p class="text-sm text-gray-500 mt-1">{{ translateText('اوسنی انځور') }}</p>
                            </div>
                        @endif
                        <input type="file" name="image" id="image" class="mt-1 block w-full">
                        @if(isset($editNews))
                            <p class="text-sm text-gray-500 mt-1">{{ translateText('که غواړئ انځور بدل کړئ، نوی انځور وټاکئ. که نه، اوسنی انځور به وساتل شي.') }}</p>
                        @endif
                        @error('image')
                            <p class="text-red-500 text-xs mt-1">{{ translateText('انځور باید د JPEG، PNG، JPG یا GIF په بڼه وي او له 2MB څخه لوی نه وي') }}</p>
                        @enderror
                    </div>

                    <div class="flex justify-start mt-6">
                        <button type="submit" class="px-6 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black shadow-md transition-all duration-300 border-0 flex items-center justify-center">
                            <i class="fas fa-{{ isset($editNews) ? 'save' : 'plus' }} ml-2"></i>
                            {{ isset($editNews) ? translateText('تازه کول') : translateText('خپرول') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- د خبرونو جدول -->
        <div class="bg-white rounded-2xl shadow-xl p-4 border-0">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-800 pb-2 flex items-center border-0">
                    <i class="fas fa-list-ul mr-2 text-gray-600"></i>
                    وروستي خبرونه
                </h3>
            </div>
            <div class="overflow-x-auto">
                <table class="table-fixed w-full text-xs">
                    <thead>
                        <tr class="bg-white text-gray-800 border border-gray-800">
                            <th class="py-2 px-1 text-center w-12 border-r border-gray-800">تصویر</th>
                            <th class="py-2 px-1 text-center w-20 border-r border-gray-800">عنوان</th>
                            <th class="py-2 px-1 text-center w-32 border-r border-gray-800">محتوا</th>
                            <th class="py-2 px-1 text-center w-16 border-r border-gray-800">نیټه</th>
                            <th class="py-2 px-1 text-center w-12">عملیې</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if(isset($news) && count($news) > 0)
                            @foreach($news as $news_item)
                                <tr class="border-b hover:bg-gray-100">
                                    <td class="py-2 px-1 text-center border-r border-gray-200">
                                        @if($news_item->New_Images)
                                            <img src="{{ asset('imagese/' . $news_item->New_Images) }}" alt="News Image" class="h-8 w-8 rounded-full object-cover mx-auto">
                                        @else
                                            <span class="text-gray-400"><i class="fas fa-image text-lg"></i></span>
                                        @endif
                                    </td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $news_item->News_Title }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ Str::limit($news_item->News_Discription, 50) }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $news_item->created_at->format('Y-m-d') }}</td>
                                    <td class="py-2 px-1 text-center">
                                        <div class="flex justify-center">
                                            <a href="{{ route('news.edit', ['id' => $news_item->News_Id]) }}" class="p-1 bg-white text-gray-700 rounded shadow-sm hover:bg-gray-200 hover:text-black transition-all duration-300 mx-0.5 border-0">
                                                <i class="fas fa-edit text-xs"></i>
                                            </a>
                                            <form action="{{ route('news.destroy', ['id' => $news_item->News_Id]) }}" method="POST" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="p-1 bg-white text-gray-700 rounded shadow-sm hover:bg-gray-200 hover:text-black transition-all duration-300 mx-0.5 border-0" onclick="return confirm('{{ translateText('آیا ډاډه یاست چې دا خبر له منځه یوسئ؟') }}')">
                                                    <i class="fas fa-trash text-xs"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="5" class="py-6 text-center text-gray-500">{{ translateText('هیڅ خبر شتون نه لري') }}</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4 flex justify-center">
                @if(isset($news))
                    {{ $news->links() }}
                @endif
            </div>
        </div>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure all elements are loaded
    setTimeout(function() {
        // Auto-hide alerts after 5 seconds
        const alerts = document.querySelectorAll('.alert-dismissible');
        alerts.forEach(function(alert) {
            setTimeout(function() {
                if (alert) {
                    alert.classList.add('opacity-0', 'transition-opacity', 'duration-500');
                    setTimeout(function() {
                        if (alert.parentNode) {
                            alert.parentNode.removeChild(alert);
                        }
                    }, 500);
                }
            }, 5000);
        });

        // Filter toggle functionality
        const filterToggleBtn = document.getElementById('filterToggleBtn');
        const filterCard = document.getElementById('filterCard');

    if (filterToggleBtn && filterCard) {
        filterToggleBtn.addEventListener('click', function(e) {
            e.preventDefault();

            if (filterCard.classList.contains('hidden')) {
                filterCard.classList.remove('hidden');
                filterToggleBtn.innerHTML = '<i class="fas fa-times mr-2"></i> فلټر بندول';
                filterToggleBtn.classList.add('bg-red-100', 'text-red-700');
                filterToggleBtn.classList.remove('bg-white', 'text-gray-700');
                // Smooth scroll to the filter card
                filterCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
            } else {
                filterCard.classList.add('hidden');
                filterToggleBtn.innerHTML = '<i class="fas fa-filter mr-2"></i> فلټر';
                filterToggleBtn.classList.remove('bg-red-100', 'text-red-700');
                filterToggleBtn.classList.add('bg-white', 'text-gray-700');
            }
        });
    }

    // Add news form toggle functionality
    const addNewsBtn = document.getElementById('addNewsBtn');
    const newsFormCard = document.getElementById('newsFormCard');

    if (addNewsBtn && newsFormCard) {
        addNewsBtn.addEventListener('click', function() {
            newsFormCard.classList.remove('hidden');
            // Smooth scroll to the form
            newsFormCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
        });
    }

    // Show filter card if there are active filters
    @if(isset($showFilter) && $showFilter)
        if (filterCard) {
            filterCard.classList.remove('hidden');
        }
    @endif
    }, 100); // End of setTimeout
});
</script>

@endsection


<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DoctorModel extends Model
{
    use HasFactory;

    protected $table = 'doctor_models';
    protected $primaryKey = 'Dr_Id';

    protected $fillable = [
        'Dr_Name',
        'Dr_office_phone',
        'Dr_Personal_phone',
        'Dr_image',
        'Dr_specialty',
        'Dr_description',
        'Dr_facebook',
        'Dr_youtube',
        'Dr_telegram',
        'user_id',
    ];

    /**
     * Get the address associated with the doctor.
     */
    public function address()
    {
        return $this->hasOne(DoctorAddModel::class, 'Dr_Id', 'Dr_Id');
    }

    /**
     * Get the image URL attribute.
     */
    public function getImageUrlAttribute()
    {
        if ($this->Dr_image && file_exists(public_path($this->Dr_image))) {
            return asset($this->Dr_image);
        }

        return asset('imagese/doctor.jpg');
    }
}










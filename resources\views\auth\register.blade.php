
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ trans_static('ثبت نوم') }}</title>
    <link rel="stylesheet" href="{{ url('public/../Css_filse/sign_up.css') }}">
    <style>
        /* General Form Styling */
form {
    max-width: 600px;
    margin: 40px auto;
    padding: 30px;
    border-radius: 15px;
    background: #f9fafb;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

form h1 {
    text-align: center;
    color: #059669;
    font-size: 28px;
    margin-bottom: 20px;
}

/* Label styling */
label {
    font-weight: 600;
    color: #374151;
    margin-top: 10px;
    display: block;
}

/* Text and password inputs */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="date"],
select {
    width: 100%;
    padding: 12px;
    margin-top: 6px;
    margin-bottom: 14px;
    border: 1px solid #d1d5db;
    border-radius: 10px;
    background-color: #fff;
    font-size: 16px;
    transition: 0.3s border-color;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="date"]:focus,
select:focus {
    border-color: #10b981;
    outline: none;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

/* Buttons */
input[type="submit"],
input[type="reset"] {
    width: 48%;
    padding: 12px;
    font-weight: 600;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: background 0.3s ease;
    font-size: 16px;
}

#submit_b {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    margin-left: 2%;
}

#submit_b:hover {
    background: linear-gradient(135deg, #059669, #047857);
}

#reset_b {
    background: #f87171;
    color: white;
    margin-right: 2%;
}

#reset_b:hover {
    background: #ef4444;
}

/* Alert Box */
.alert {
    padding: 10px 15px;
    background-color: #fee2e2;
    color: #991b1b;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #fca5a5;
}

/* Responsive Design */
@media (max-width: 600px) {
    form {
        padding: 20px;
    }

    input[type="submit"],
    input[type="reset"] {
        width: 100%;
        margin-bottom: 10px;
    }

    .form-group.row {
        display: block;
    }

    .form-group .col-md-4,
    .form-group .col-md-6 {
        width: 100%;
    }
}

    </style>
    
</head>
<body>
<script defer>
    function checkTheForm(){
        let sd=document.getElementById("user").value;
        if(sd[0]=""){
            alert("{{trans_static('په مهربانی سره تاسی نشی کولی چی لمړی فاصله داخله کړی')}}");
            return false;
        }
        else if(sd.length==""){
            alert("{{trans_static('مهربانی وکړی نوموړی فیلډ پوره کړی')}}");
            return false;
        }
        else if(sd.length < 2 || sd.length > 25 ){
            alert(" 2 <charaacters > 25");
            return false;
        }

        // Check if passwords match
        let password = document.getElementById("password").value;
        let confirmPassword = document.getElementById("confirm-password").value;
        if(password !== confirmPassword) {
            alert("{{trans_static('امنیتی کودونه سره برابر نه دي')}}");
            return false;
        }
        
        // Form is valid, it will be submitted
        // You can add a success message here if needed
        return true;
    }
    
    function ChekTheClearance(){
        if(confirm("{{trans_static('آیا تاسی اطمنانی یی')}}")){
            return true;
        }
        else{
            return false;
        }
    }
    
    function ChangeTheColor() {
        event.target.style.borderColor = "#1a2a6c";
        event.target.style.boxShadow = "0 0 0 3px rgba(26, 42, 108, 0.2)";
        event.target.style.backgroundColor = "#fff";
    }
    
    function backToTheColor() {
        event.target.style.borderColor = "#ddd";
        event.target.style.boxShadow = "none";
        event.target.style.backgroundColor = "#f8f9fa";
    }
</script>

<form action="{{ route('register') }}" onsubmit="return checkTheForm()" onreset="return ChekTheClearance()" method="POST">
    @csrf
    <!-- Remove this line as we're handling redirects in the backend -->
    <!-- <input type="hidden" name="redirect_to" value="{{ route('dashboard') }}"> -->
    <h1 dir="rtl"> {{trans_static('ثبت نام ته ښه راغلاست!')}}</h1>
    
    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    
    <label class="ssdss" for="user">{{trans_static('پوره نوم')}}</label>
    <br>
    <input dir="rtl" type="text" id="user" name="name" value="{{ old('name') }}"
           onfocus="ChangeTheColor()" onblur="backToTheColor()"
           placeholder="{{trans_static('خپل نوم مو داخل کړی')}}" class="awz" required>
    <br>

    <label id="dds" for="date_of_birth">{{trans_static('د زیږیدو نیټه')}}</label>
    <br>
    <div class="form-group row">
        <label for="date_of_birth" class="col-md-4 col-form-label text-md-right">{{ trans_static('Date of Birth') }}</label>

        <div class="col-md-6">
            <input id="date_of_birth" type="date" class="form-control @error('date_of_birth') is-invalid @enderror" name="date_of_birth" value="{{ old('date_of_birth') }}" required>

            @error('date_of_birth')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>
    </div>
    <br>
    
    <label id="dsds" for="email">{{trans_static('ایمیل')}}</label>
    <br>
    <input dir="rtl" type="email" required id="email" name="email" value="{{ old('email') }}"
           onfocus="ChangeTheColor()" onblur="backToTheColor()"
           placeholder="{{trans_static('خپل ایمیل ادرس داخل کړی')}}" class="awz">
    <br>

    <label class="ssdss" for="password">{{trans_static('امنیتی کود')}}</label>
    <br>
    <input dir="rtl" type="password" required id="password" name="password"
           onfocus="ChangeTheColor()" onblur="backToTheColor()"
           placeholder="{{trans_static('امنیتی کود موداخل کړی')}}" class="awz">
    <br>

    <label for="confirm-password">{{trans_static('تصدیق شوی کود')}}</label>
    <br>
    <input dir="rtl" type="password" required id="confirm-password" name="password_confirmation"
           onfocus="ChangeTheColor()" onblur="backToTheColor()"
           placeholder="{{trans_static('تصدیق شوی کود')}}" class="awz">
    <br>
    
    <div class="form-group row">
        <label for="role" class="col-md-4 col-form-label text-md-right">{{ trans_static('Role') }}</label>

        <div class="col-md-6">
            <select id="role" class="form-control @error('role') is-invalid @enderror" name="role" required>
             
                <option value="admin">{{ trans_static('Admin') }}</option>
                
            </select>

            @error('role')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
            @enderror
        </div>
    </div>
    
    <input type="submit" id="submit_b" class="btn" value="{{trans_static('استول')}}">
</form>
</body>
</html>







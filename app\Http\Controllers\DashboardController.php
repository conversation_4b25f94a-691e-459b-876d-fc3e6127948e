<?php

namespace App\Http\Controllers;

use App\Models\FeedbackModel;
use Illuminate\Http\Request;
use App\Models\PatientModel;
use App\Models\DoctorModel;
use App\Models\CommentModel;
use App\Models\NewsModel;
use App\Models\ArticalModel; // Note the spelling: Artical not Article
use App\Models\VideoModel;

class DashboardController extends Controller
{
    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        // Add register route to view data
        return view('dashboard', [
            'registerRoute' => route('register')
        ]);
    }
}


 @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap');

 * {
     margin: 0;
     padding: 0;
     box-sizing: border-box;
     font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
 }

 :root {
     --primary: #4361ee;
     --primary-dark: #3a36e0;
     --secondary: #f72585;
     --success: #4cc9f0;
     --warning: #f8961e;
     --light: #f8f9fa;
     --dark: #212529;
     --gray: #6c757d;
     --light-gray: #e9ecef;
     --sidebar-width: 280px;
     --purple: #7209b7;
     --teal: #06d6a0;
     --orange: #ff9e00;
     --sidebar-bg: linear-gradient(to bottom, #3a36e0, #4361ee);
     --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
     --header-bg: linear-gradient(135deg, #4361ee, #3f57d0);
 }

 body {
     background-color: #f5f7fb;
     min-height: 100vh;
     overflow-x: hidden;
     direction: rtl;
     display: flex;
     transition: all 0.3s ease;
 }

 /* Sidebar Styles */
 .sidebar {
     width: var(--sidebar-width);
     background: #3b82f6;
     /* bg-blue-500 */
     color: white;
     height: 100vh;
     position: fixed;
     top: 0;
     right: 0;
     overflow-y: auto;
     z-index: 1000;
     box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
     transition: all 0.3s ease;
     transform: translateX(0);
 }

 .sidebar-header {
     padding: 25px;
     display: flex;
     align-items: center;
     border-bottom: 1px solid rgba(255, 255, 255, 0.1);
 }

 .sidebar-header img {
     width: 50px;
     height: 50px;
     border-radius: 50%;
     margin-left: 15px;
     border: 2px solid rgba(255, 255, 255, 0.3);
     box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
 }

 .user-info h3 {
     font-size: 20px;
     font-weight: 700;
     margin-bottom: 5px;
 }

 .user-info p {
     font-size: 14px;
     color: rgba(255, 255, 255, 0.8);
 }

 .search-container {
     padding: 20px;
     position: relative;
 }

 .search-container input {
     width: 100%;
     padding: 12px 45px 12px 15px;
     border-radius: 30px;
     border: none;
     background: rgba(255, 255, 255, 0.15);
     color: white;
     font-size: 15px;
     transition: all 0.3s;
 }

 .search-container input:focus {
     background: rgba(255, 255, 255, 0.25);
     outline: none;
 }

 .search-container input::placeholder {
     color: rgba(255, 255, 255, 0.6);
 }

 .search-container i {
     position: absolute;
     right: 35px;
     top: 50%;
     transform: translateY(-50%);
     color: rgba(255, 255, 255, 0.6);
     font-size: 18px;
 }

 .menu-items {
     padding: 20px 0;
 }

 .menu-item {
     padding: 15px 25px;
     display: flex;
     align-items: center;
     cursor: pointer;
     transition: all 0.2s;
     margin: 5px 10px;
     border-radius: 10px;
 }

 .menu-item:hover {
     background: rgba(255, 255, 255, 0.2);
     transform: translateX(-5px);
 }

 .menu-item.active {
     background: rgba(255, 255, 255, 0.2);
     border-right: 4px solid white;
 }

 .menu-item i {
     width: 30px;
     font-size: 20px;
     margin-left: 15px;
     text-align: center;
 }

 .menu-item span {
     font-size: 16px;
     font-weight: 500;
 }

 .divider {
     height: 1px;
     background: rgba(255, 255, 255, 0.1);
     margin: 20px;
 }

 .subsection {
     padding: 20px;
     background: rgba(255, 255, 255, 0.1);
     margin: 20px;
     border-radius: 15px;
     text-align: center;
 }

 .subsection h4 {
     font-size: 16px;
     margin-bottom: 8px;
     color: rgba(255, 255, 255, 0.7);
 }

 .subsection p {
     font-size: 18px;
     font-weight: 500;
 }

 /* Close button for mobile */
 .close-menu {
     display: none;
     position: absolute;
     top: 20px;
     left: 20px;
     background: rgba(255, 255, 255, 0.2);
     border: none;
     color: white;
     width: 40px;
     height: 40px;
     border-radius: 50%;
     font-size: 20px;
     cursor: pointer;
     z-index: 1100;
     align-items: center;
     justify-content: center;
     transition: all 0.3s;
 }

 .close-menu:hover {
     background: rgba(255, 255, 255, 0.3);
     transform: rotate(90deg);
 }

 /* Main Content Styles */
 .main-content {
     flex: 1;
     margin-right: var(--sidebar-width);
     padding: 30px;
     transition: all 0.3s ease;
 }

 /* Header Styles */
 .header {
     display: flex;
     justify-content: space-between;
     align-items: center;
     padding: 1rem 0;
     border-bottom: 2px solid #3b82f6;
     /* bg-blue-500 */
     margin-bottom: 2rem;
 }

 .dashboard-title {
     display: flex;
     align-items: center;
     gap: 0.75rem;
     font-size: 1.5rem;
     font-weight: 700;
 }

 .dashboard-title i {
     font-size: 1.75rem;
 }

 .header-actions {
     display: flex;
     gap: 1rem;
 }

 .logout-header-btn,
 .register-header-btn {
     display: flex;
     align-items: center;
     gap: 0.5rem;
     padding: 0.5rem 1rem;
     border-radius: 0.5rem;
     color: white;
     font-weight: 500;
     transition: all 0.3s ease;
 }

 .logout-header-btn:hover,
 .register-header-btn:hover {
     transform: translateY(-2px);
     box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
 }

 .date-info {
     display: flex;
     flex-direction: column;
     align-items: flex-end;
 }

 .current-date {
     font-size: 20px;
     font-weight: 600;
     color: white;
 }

 .calendar-info {
     font-size: 16px;
     color: rgba(255, 255, 255, 0.9);
 }

 /* Mobile Menu Button */
 .mobile-menu-btn {
     display: none;
     background: var(--primary);
     color: white;
     border: none;
     width: 50px;
     height: 50px;
     border-radius: 12px;
     font-size: 22px;
     cursor: pointer;
     justify-content: center;
     align-items: center;
     position: fixed;
     top: 20px;
     right: 20px;
     z-index: 900;
     box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
     transition: all 0.3s;
 }

 .mobile-menu-btn:hover {
     background: var(--primary-dark);
     transform: scale(1.05);
 }

 /* Summary Cards - New Design */
 .summary-cards {
     display: grid;
     grid-template-columns: repeat(3, 1fr);
     gap: 25px;
     margin-bottom: 40px;
 }

 .summary-card {
     background: white;
     border-radius: 16px;
     box-shadow: var(--card-shadow);
     padding: 0;
     display: flex;
     flex-direction: column;
     overflow: hidden;
     transition: all 0.4s ease;
     position: relative;
     border: none;
 }

 .summary-card:hover {
     transform: translateY(-10px);
     box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
 }

 .summary-card::before {
     content: '';
     position: absolute;
     top: 0;
     right: 0;
     width: 100%;
     height: 5px;
     background: var(--primary);
     z-index: 2;
     transform: scaleX(0);
     transform-origin: left;
     transition: transform 0.5s ease;
 }

 .summary-card:hover::before {
     transform: scaleX(1);
 }

 .card-header {
     display: flex;
     justify-content: space-between;
     align-items: center;
     padding: 25px 25px 15px;
     background: rgba(255, 255, 255, 0.9);
     position: relative;
     z-index: 1;
 }

 .card-title {
     font-size: 1.2rem;
     font-weight: 700;
     color: var(--dark);
 }

 .card-icon {
     width: 60px;
     height: 60px;
     border-radius: 16px;
     display: flex;
     align-items: center;
     justify-content: center;
     font-size: 28px;
     box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
 }

 .patients .card-icon {
     background: linear-gradient(135deg, #4361ee 0%, #3a36e0 100%);
     color: white;
 }

 .doctors .card-icon {
     background: linear-gradient(135deg, #f72585 0%, #d3166d 100%);
     color: white;
 }

 .comments .card-icon {
     background: linear-gradient(135deg, #4cc9f0 0%, #2aa8d0 100%);
     color: white;
 }

 .appointments .card-icon {
     background: linear-gradient(135deg, #7209b7 0%, #5a078f 100%);
     color: white;
 }

 .articles .card-icon {
     background: linear-gradient(135deg, #06d6a0 0%, #05b388 100%);
     color: white;
 }

 .surveys .card-icon {
     background: linear-gradient(135deg, #ff9e00 0%, #e68a00 100%);
     color: white;
 }

 .card-content {
     padding: 20px 25px 30px;
     position: relative;
     z-index: 1;
 }

 .card-value {
     font-size: 2.8rem;
     font-weight: 800;
     margin-bottom: 5px;
     background: linear-gradient(135deg, var(--dark) 0%, var(--gray) 100%);
     -webkit-background-clip: text;
     -webkit-text-fill-color: transparent;
     line-height: 1.2;
 }

 .card-description {
     color: var(--gray);
     font-size: 16px;
     margin-bottom: 15px;
     line-height: 1.5;
     min-height: 48px;
 }

 .card-decoration {
     position: absolute;
     bottom: 0;
     left: 0;
     width: 100%;
     height: 40%;
     background: linear-gradient(to top, rgba(67, 97, 238, 0.05) 0%, transparent 100%);
     border-radius: 0 0 16px 16px;
     opacity: 0.6;
     z-index: 0;
 }

 /* Specific card color accents */
 .patients::before {
     background: var(--primary);
 }

 .doctors::before {
     background: var(--secondary);
 }

 .comments::before {
     background: var(--success);
 }

 .appointments::before {
     background: var(--purple);
 }

 .articles::before {
     background: var(--teal);
 }

 .surveys::before {
     background: var(--orange);
 }

 /* Charts Section */
 .charts-container {
     display: grid;
     grid-template-columns: 1fr 1fr;
     gap: 25px;
     margin-bottom: 40px;
 }

 .chart-card {
     background: white;
     border-radius: 18px;
     box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
     padding: 25px;
     transition: all 0.3s ease;
     position: relative;
     overflow: hidden;
 }

 .chart-card:hover {
     box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
     transform: translateY(-5px);
 }

 .chart-header {
     display: flex;
     justify-content: space-between;
     align-items: center;
     margin-bottom: 25px;
 }

 .chart-title {
     font-size: 22px;
     font-weight: 700;
     color: var(--dark);
 }

 .chart-actions {
     display: flex;
     gap: 12px;
 }

 .chart-btn {
     background: var(--light-gray);
     border: none;
     padding: 8px 16px;
     border-radius: 10px;
     font-size: 15px;
     cursor: pointer;
     transition: all 0.2s;
 }

 .chart-btn:hover {
     background: var(--primary);
     color: white;
 }

 .chart-btn.active {
     background: var(--primary);
     color: white;
 }

 .chart-container {
     height: 320px;
     position: relative;
 }

 /* Recent Activity */
 .activity-container {
     background: white;
     border-radius: 18px;
     box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
     padding: 25px;
     margin-bottom: 40px;
 }

 .section-header {
     display: flex;
     justify-content: space-between;
     align-items: center;
     margin-bottom: 25px;
 }

 .section-title {
     font-size: 22px;
     font-weight: 700;
     color: var(--dark);
 }

 .view-all {
     color: var(--primary);
     text-decoration: none;
     font-weight: 500;
     display: flex;
     align-items: center;
     gap: 8px;
     transition: all 0.2s;
 }

 .view-all:hover {
     color: var(--primary-dark);
     transform: translateX(-5px);
 }

 .activity-list {
     display: flex;
     flex-direction: column;
     gap: 20px;
 }

 .activity-item {
     display: flex;
     align-items: center;
     padding: 15px;
     border-radius: 12px;
     background: var(--light);
     transition: all 0.2s;
     border-left: 3px solid transparent;
 }

 .activity-item:hover {
     background: rgba(67, 97, 238, 0.05);
     transform: translateX(-5px);
     border-left: 3px solid var(--primary);
 }

 .activity-icon {
     width: 50px;
     height: 50px;
     border-radius: 12px;
     display: flex;
     align-items: center;
     justify-content: center;
     font-size: 22px;
     margin-left: 15px;
     background: rgba(67, 97, 238, 0.15);
     color: var(--primary);
     transition: all 0.3s;
 }

 .activity-item:hover .activity-icon {
     transform: rotate(10deg);
 }

 .activity-content {
     flex: 1;
 }

 .activity-title {
     font-weight: 600;
     margin-bottom: 5px;
 }

 .activity-time {
     color: var(--gray);
     font-size: 14px;
 }

 /* Footer */
 .footer {
     text-align: center;
     padding: 20px;
     color: var(--gray);
     border-top: 1px solid var(--light-gray);
     font-size: 14px;
 }

 /* Overlay for mobile menu */
 .overlay {
     display: none;
     position: fixed;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background: rgba(0, 0, 0, 0.5);
     z-index: 900;
     backdrop-filter: blur(3px);
 }

 /* Responsive Design */
 @media (max-width: 1200px) {
     .summary-cards {
         grid-template-columns: repeat(2, 1fr);
     }

     .charts-container {
         grid-template-columns: 1fr;
     }
 }

 @media (max-width: 992px) {
     .sidebar {
         width: 280px;
         transform: translateX(100%);
     }

     .sidebar.active {
         transform: translateX(0);
     }

     .close-menu {
         display: flex;
     }

     .mobile-menu-btn {
         display: flex;
     }

     .main-content {
         margin-right: 0;
         width: 100%;
     }
 }

 @media (max-width: 768px) {
     .header {
         flex-direction: column;
         align-items: flex-start;
         gap: 15px;
         padding: 20px;
         padding-top: 70px;
     }

     .date-info {
         align-items: flex-start;
     }

     .summary-cards {
         grid-template-columns: 1fr;
     }

     .dashboard-title {
         font-size: 28px;
     }

     .dashboard-title i {
         width: 45px;
         height: 45px;
         font-size: 20px;
     }

     .chart-card {
         padding: 20px;
     }

     .activity-container {
         padding: 20px;
     }
 }

 @media (max-width: 576px) {
     .sidebar {
         width: 100%;
     }

     .main-content {
         padding: 20px 15px;
     }

     .dashboard-title {
         font-size: 24px;
     }

     .card-value {
         font-size: 2.5rem;
     }

     .card-icon {
         width: 50px;
         height: 50px;
         font-size: 24px;
     }

     .chart-container {
         height: 250px;
     }

     .activity-item {
         padding: 12px;
     }

     .activity-icon {
         width: 45px;
         height: 45px;
         font-size: 20px;
         margin-left: 10px;
     }

     .mobile-menu-btn {
         width: 45px;
         height: 45px;
         font-size: 20px;
         top: 15px;
         right: 15px;
     }
 }

 @media (max-width: 480px) {
     .chart-actions {
         flex-wrap: wrap;
     }

     .chart-btn {
         padding: 6px 12px;
         font-size: 14px;
     }

     .section-title {
         font-size: 20px;
     }

     .view-all {
         font-size: 14px;
     }

     .footer {
         font-size: 12px;
     }
 }

 /* Admin Actions Card */
 .admin-actions-card {
     background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
     border-radius: 12px;
     box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
     margin: 20px 0;
     overflow: hidden;
     transition: transform 0.3s ease, box-shadow 0.3s ease;
 }

 .admin-actions-card:hover {
     transform: translateY(-5px);
     box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
 }

 .admin-actions-card .card-header {
     background: linear-gradient(135deg, #4a6baf 0%, #5d9cec 100%);
     color: white;
     padding: 15px 20px;
     border-bottom: 1px solid rgba(0, 0, 0, 0.05);
 }

 .admin-actions-card .card-header h3 {
     margin: 0;
     font-size: 18px;
     font-weight: 600;
 }

 /* Header buttons styling */
 .header-actions {
     display: flex;
     align-items: center;
     gap: 10px;
     white-space: nowrap;
 }

 .logout-header-btn,
 .register-header-btn,
 .notification-header-btn {
     display: inline-flex;
     align-items: center;
     gap: 5px;
     padding: 8px 12px;
     border-radius: 4px;
     transition: all 0.2s;
 }

 .logout-header-btn {
     background-color: #f44336;
     color: white;
     border: 1px solid #d32f2f;
 }

 .logout-header-btn:hover {
     background-color: #d32f2f;
     box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
 }

 .register-header-btn {
     background-color: #4caf50;
     color: white;
     border: 1px solid #388e3c;
 }

 .register-header-btn:hover {
     background-color: #388e3c;
     box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
 }

 .logout-header-btn i,
 .register-header-btn i,
 .notification-header-btn i {
     margin-right: 4px;
 }

 /* Alert styling */
 .alert {
     padding: 15px;
     margin-bottom: 20px;
     border: 1px solid transparent;
     border-radius: 4px;
 }

 .alert-success {
     color: #155724;
     background-color: #d4edda;
     border-color: #c3e6cb;
 }

 .alert-danger {
     color: #721c24;
     background-color: #f8d7da;
     border-color: #f5c6cb;
 }
@extends('layouts/Admin')
@section('title','Doctor <PERSON>')
@section('contents')

<div class="p-4 md:p-8 min-h-screen transition-all w-full bg-white">
    <div class="container mx-auto max-w-6xl">
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-8">
            <div class="flex items-center mb-4 md:mb-0">
                <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-user-md text-gray-700 text-xl"></i>
                </div>
                <h1 class="text-2xl md:text-3xl font-bold text-gray-800 mr-4">{{ translateText('د ډاکټرانو مدیریت') }}</h1>
            </div>
            <div class="flex space-x-3">
                <button id="refreshBtn" onclick="window.location.href='{{ route('doctor.index') }}'" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                    <i class="fas fa-sync-alt mr-2"></i>
                    تازه کول
                </button>
               <div class="flex space-x-3">
                    <button type="button" id="filterToggleBtn" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                        <i class="fas fa-filter mr-2"></i> {{ translateText('فلټر') }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        @if(session('success'))
            <div class="bg-green-100 border-r-4 border-green-500 text-green-700 p-4 mb-6 rounded-lg shadow-md alert-dismissible fade show" role="alert" id="successAlert">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-500 text-xl"></i>
                    </div>
                    <div class="mr-3">
                        <p class="font-bold">{{ translateText('بریالیتوب!') }}</p>
                        <p>{{ session('success') }}</p>
                    </div>
                    <button type="button" class="mr-auto text-green-700 hover:text-green-900 focus:outline-none" onclick="document.getElementById('successAlert').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-lg shadow-md alert-dismissible fade show" role="alert" id="errorAlert">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
                    </div>
                    <div class="mr-3">
                        <p class="font-bold">{{ translateText('تېروتنه!') }}</p>
                        <p>{{ session('error') }}</p>
                    </div>
                    <button type="button" class="mr-auto text-red-700 hover:text-red-900 focus:outline-none" onclick="document.getElementById('errorAlert').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        @endif

        @if($errors->any())
            <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-lg shadow-md alert-dismissible fade show" role="alert" id="validationAlert">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-500 text-xl"></i>
                    </div>
                    <div class="mr-3">
                        <p class="font-bold">{{ translateText('د ډیټا تېروتنې!') }}</p>
                        <ul class="list-disc list-inside">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                    <button type="button" class="mr-auto text-red-700 hover:text-red-900 focus:outline-none" onclick="document.getElementById('validationAlert').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        @endif

        <!-- Doctor Form -->
        <div id="doctorFormContainer" class="bg-white rounded-lg shadow-md p-6 mb-8" style="display: none;">
            <h2 class="text-xl font-bold mb-4">{{ translateText('د ډاکټر معلومات') }}</h2>
            
            <form id="doctorForm" action="{{ route('doctor.store') }}" method="POST" enctype="multipart/form-data" class="space-y-4">
                @csrf
                <!-- Hidden doctor ID field for updates -->
                <input type="hidden" id="doctor_id" name="doctor_id" value="">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Doctor Name -->
                    <div class="group col-span-1 md:col-span-2">
                        <label for="dr_name" class="block text-sm font-medium mb-2 text-gray-700">{{ translateText('د ډاکټر نوم') }}</label>
                        <input type="text" id="dr_name" name="dr_name" class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300" required>
                    </div>

                    <!-- Doctor Specialty -->
                    <div class="group col-span-1 md:col-span-2">
                        <label for="dr_specialty" class="block text-sm font-medium mb-2 text-gray-700">{{ translateText('تخصص') }}</label>
                        <input type="text" id="dr_specialty" name="dr_specialty" class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300" placeholder="{{ translateText('د ذهني روغتیا متخصص') }}">
                    </div>

                    <!-- Facebook Link -->
                    <div class="group">
                        <label for="dr_facebook" class="block text-sm font-medium mb-2 text-gray-700">{{ translateText('د فیسبوک لینک') }}</label>
                        <input type="url" id="dr_facebook" name="dr_facebook" class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300" placeholder="https://facebook.com/username">
                    </div>

                    <!-- YouTube Link -->
                    <div class="group">
                        <label for="dr_youtube" class="block text-sm font-medium mb-2 text-gray-700">{{ translateText('د یوټیوب لینک') }}</label>
                        <input type="url" id="dr_youtube" name="dr_youtube" class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300" placeholder="https://youtube.com/channel/...">
                    </div>

                    <!-- Telegram Link -->
                    <div class="group col-span-1 md:col-span-2">
                        <label for="dr_telegram" class="block text-sm font-medium mb-2 text-gray-700">{{ translateText('د تلیګرام لینک') }}</label>
                        <input type="text" id="dr_telegram" name="dr_telegram" class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300" placeholder="@username یا https://t.me/username">
                    </div>
                </div>

                <!-- Doctor Description -->
                <div class="group">
                    <label for="dr_description" class="block text-sm font-medium mb-2 text-gray-700">{{ translateText('توضیحات') }}</label>
                    <textarea id="dr_description" name="dr_description" rows="4" class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300" placeholder="{{ translateText('د ډاکټر په اړه لنډ معلومات...') }}"></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Doctor Image -->
                    <div class="group col-span-1 md:col-span-2">
                        <label for="dr_image" class="block text-sm font-medium mb-2 text-gray-700">{{ translateText('د ډاکټر انځور') }}</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-500 transition-colors">
                            <input type="file" id="dr_image" name="dr_image" accept="image/*" class="hidden">
                            <label for="dr_image" class="cursor-pointer flex flex-col items-center justify-center">
                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                <span class="text-sm text-gray-500">{{ translateText('انځور انتخاب کړئ') }}</span>
                                <span class="text-xs text-gray-400 mt-1">JPG, PNG یا GIF</span>
                            </label>
                        </div>
                        
                        <!-- Image Preview -->
                        <div class="mt-2 text-center">
                            <img id="image_preview" src="" alt="{{ translateText('د ډاکټر انځور') }}" class="max-h-40 mx-auto hidden">
                        </div>
                    </div>
                </div>
                
                <!-- Submit and Cancel Buttons -->
                <div class="flex justify-center space-x-4 mt-6">
                    <button type="submit" class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0" onclick="console.log('Doctor form submitted');">
                        <i class="fas fa-save mr-2"></i> {{ translateText('ثبتول') }}
                    </button>
                    <button type="button" onclick="hideForm()" class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                        <i class="fas fa-times mr-2"></i> {{ translateText('لغوه کول') }}
                    </button>
                </div>
            </form>
        </div>

        <!-- Filter Card - Hidden by default -->
        <div id="filterCard" class="bg-white rounded-2xl shadow-xl mb-8 border-0 overflow-hidden transform transition-all duration-300 hover:shadow-2xl hidden">
            <div class="bg-gray-200 px-6 py-4">
                <h2 class="text-xl md:text-2xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-filter mr-2"></i> {{ translateText('د ډاکټرانو فلټر کول') }}
                </h2>
            </div>

            <div class="p-6">
                <form action="{{ route('doctors.filter') }}" method="GET" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د ثبت کال') }}</label>
                            <select name="year" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                                <option value="">{{ translateText('ټول کلونه') }}</option>
                                @if(isset($years) && count($years) > 0)
                                    @foreach($years as $year)
                                        <option value="{{ $year }}" {{ request('year') == $year ? 'selected' : '' }}>{{ $year }}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>

                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د نوم یا تلیفون له مخې پلټنه') }}</label>
                            <input type="text" name="search" value="{{ request('search') }}" placeholder="{{ translateText('د ډاکټر نوم یا تلیفون ولیکئ...') }}"
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                        </div>
                    </div>

                    <div class="flex flex-col md:flex-row gap-4 pt-4">
                        <button type="submit"
                            class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                            <i class="fas fa-filter mr-2"></i> {{ translateText('فلټر کول') }}
                        </button>
                        <a href="{{ route('doctord.index') }}"
                            class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                            <i class="fas fa-undo mr-2"></i> {{ translateText('بیا تنظیمول') }}
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- د ډاکټرانو لیست -->
        <div class="bg-white rounded-2xl shadow-xl p-4 border-0">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-800 pb-2 flex items-center border-0">
                    <i class="fas fa-user-md mr-2 text-gray-600"></i>
                    ثبت شوي ډاکټران
                </h3>
                <button onclick="showForm()" class="w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                    <i class="fas fa-plus-circle mr-2"></i> {{ translateText('نوی ډاکټر') }}
                </button>
            </div>

            <!-- Doctors Table Section -->
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white rounded-lg overflow-hidden">
                    <thead class="bg-gray-800 text-white">
                        <tr>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('انځور') }}</th>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('نوم') }}</th>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('توضیحات') }}</th>
                            <!-- <th class="p-3 text-right border-r border-gray-800">{{ translateText('ټیلیفون') }}</th> -->
                            <!-- <th class="p-3 text-right border-r border-gray-800">{{ translateText('ولایت') }}</th> -->
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('ټولنیز رسنۍ') }}</th>
                            <th class="p-3 text-right">{{ translateText('عملیې') }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-800">
                        @if(isset($doctors) && count($doctors) > 0)
                        @foreach($doctors as $doctor)
                        <tr class="border-b hover:bg-gray-100">
                            <!-- Doctor Image -->
                            <td class="p-3 border-r border-gray-200">
                                <div class="w-12 h-12 rounded-full overflow-hidden">
                                    @if($doctor->Dr_image && file_exists(public_path($doctor->Dr_image)))
                                        <img src="{{ asset($doctor->Dr_image) }}" alt="{{ $doctor->Dr_Name }}" class="w-full h-full object-cover">
                                    @else
                                        <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                            <i class="fas fa-user-md text-gray-400"></i>
                                        </div>
                                    @endif
                                </div>
                            </td>
                            <!-- Doctor Name -->
                            <td class="p-3 border-r border-gray-200">
                                <div>
                                    <div class="font-medium">{{ $doctor->Dr_Name }}</div>
                                    @if($doctor->Dr_specialty)
                                        <div class="text-sm text-blue-600">{{ $doctor->Dr_specialty }}</div>
                                    @endif
                                </div>
                            </td>
                            <!-- Description -->
                            <td class="p-3 border-r border-gray-200">
                                @if($doctor->Dr_description)
                                    <div class="text-sm">{{ Str::limit($doctor->Dr_description, 50) }}</div>
                                @else
                                    <span class="text-gray-400 text-sm">{{ translateText('توضیحات نشته') }}</span>
                                @endif
                            </td>
                            <!-- Phone -->
                            <!-- <td class="p-3 border-r border-gray-200">
                                @if($doctor->Dr_Personal_phone)
                                    <div class="text-sm">{{ $doctor->Dr_Personal_phone }}</div>
                                @endif
                                @if($doctor->Dr_office_phone)
                                    <div class="text-xs text-gray-500">{{ $doctor->Dr_office_phone }}</div>
                                @endif
                            </td> -->
                            <!-- Province -->
                            <!-- <td class="p-3 border-r border-gray-200">
                                {{ $doctor->address->Dr_Province ?? translateText('نامعلوم') }}
                            </td> -->
                            <!-- Social Media -->
                            <td class="p-3 border-r border-gray-200">
                                <div class="flex space-x-2">
                                    @if($doctor->Dr_facebook)
                                        <a href="{{ $doctor->Dr_facebook }}" target="_blank" class="text-blue-600 hover:text-blue-800">
                                            <i class="fab fa-facebook"></i>
                                        </a>
                                    @else
                                        <i class="fab fa-facebook text-gray-300"></i>
                                    @endif
                                    @if($doctor->Dr_youtube)
                                        <a href="{{ $doctor->Dr_youtube }}" target="_blank" class="text-red-600 hover:text-red-800">
                                            <i class="fab fa-youtube"></i>
                                        </a>
                                    @else
                                        <i class="fab fa-youtube text-gray-300"></i>
                                    @endif
                                    @if($doctor->Dr_telegram)
                                        <a href="{{ $doctor->Dr_telegram }}" target="_blank" class="text-blue-500 hover:text-blue-700">
                                            <i class="fab fa-telegram"></i>
                                        </a>
                                    @else
                                        <i class="fab fa-telegram text-gray-300"></i>
                                    @endif
                                </div>
                            </td>
                            <!-- Actions -->
                            <td class="p-3">
                                <div class="flex justify-end space-x-2">
                                    <button onclick="editDoctor(
                                        '{{ $doctor->Dr_Id }}',
                                        '{{ $doctor->Dr_Name }}',
                                        '{{ $doctor->Dr_image }}',
                                        '{{ $doctor->Dr_specialty ?? '' }}',
                                        '{{ $doctor->Dr_description ?? '' }}',
                                        '{{ $doctor->Dr_facebook ?? '' }}',
                                        '{{ $doctor->Dr_youtube ?? '' }}',
                                        '{{ $doctor->Dr_telegram ?? '' }}'
                                    )" class="p-1 bg-white text-gray-700 rounded shadow-sm hover:bg-gray-200 hover:text-black transition-all duration-300 mr-2 border-0">
                                        <i class="fas fa-edit"></i>
                                    </button>

                                    <form action="{{ route('doctor.destroy', $doctor->Dr_Id) }}" method="POST" class="inline" onsubmit="return confirm('{{ translateText('آیا تاسو ډاډه یاست چې غواړئ دا ډاکټر لرې کړئ؟') }}');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="p-1 bg-white text-gray-700 rounded shadow-sm hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                        @else
                        <tr>
                            <td colspan="7" class="p-8 text-center text-gray-500">
                                {{ translateText('هیڅ ډاکټر ونه موندل شو') }}
                            </td>
                        </tr>
                        @endif
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4 flex justify-center">
                @if(isset($doctors))
                    {{ $doctors->links() }}
                @endif
            </div>
        </div>
    </div>
</div>

<script>
function showForm() {
    document.getElementById('doctorFormContainer').style.display = 'block';
    document.getElementById('doctorFormContainer').scrollIntoView({ behavior: 'smooth' });
}

function hideForm() {
    document.getElementById('doctorFormContainer').style.display = 'none';
    document.getElementById('doctorForm').reset();
    document.getElementById('image_preview').style.display = 'none';
    
    // Reset form action to store
    document.getElementById('doctorForm').action = "{{ route('doctor.store') }}";
    
    // Remove method field if it exists
    const methodField = document.getElementById('method_field');
    if (methodField) {
        methodField.remove();
    }
    
    // Reset button text
    const submitButton = document.querySelector('#doctorForm button[type="submit"]');
    if (submitButton) {
        submitButton.textContent = "{{ translateText('ثبتول') }}";
    }
}

function editDoctor(id, name, image, specialty, description, facebook, youtube, telegram) {
    // Set form action for update
    document.getElementById('doctorForm').action = "{{ route('doctor.update', '') }}/" + id;
    
    // Add method field for PUT request
    let methodField = document.getElementById('method_field');
    if (!methodField) {
        let input = document.createElement('input');
        input.type = 'hidden';
        input.name = '_method';
        input.value = 'PUT';
        input.id = 'method_field';
        document.getElementById('doctorForm').appendChild(input);
    } else {
        methodField.value = 'PUT';
    }
    
    // Make sure CSRF token is present
    if (!document.querySelector('input[name="_token"]')) {
        let csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '_token';
        csrfInput.value = '{{ csrf_token() }}';
        document.getElementById('doctorForm').appendChild(csrfInput);
    }
    
    // Store doctor ID in hidden field
    document.getElementById('doctor_id').value = id;
    
    // Fill form fields with doctor data
    document.getElementById('dr_name').value = name || '';
    document.getElementById('dr_specialty').value = specialty || '';
    document.getElementById('dr_description').value = description || '';
    document.getElementById('dr_facebook').value = facebook || '';
    document.getElementById('dr_youtube').value = youtube || '';
    document.getElementById('dr_telegram').value = telegram || '';
    
    // Update image preview if available
    if (image) {
        const imagePreview = document.getElementById('image_preview');
        if (imagePreview) {
            imagePreview.src = "{{ asset('') }}" + image;
            imagePreview.style.display = 'block';
        }
    }
    
    // Change button text to indicate update
    const submitButton = document.querySelector('#doctorForm button[type="submit"]');
    if (submitButton) {
        submitButton.textContent = "{{ translateText('تازه کول') }}";
    }
    
    // Show the form
    document.getElementById('doctorFormContainer').style.display = 'block';
    
    // Scroll to form
    document.getElementById('doctorFormContainer').scrollIntoView({ behavior: 'smooth' });
}

// Image preview functionality
document.getElementById('dr_image').addEventListener('change', function(e) {
    const file = this.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('image_preview');
            preview.src = e.target.result;
            preview.style.display = 'block';
        }
        reader.readAsDataURL(file);
    }
});

// Filter toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const filterToggleBtn = document.getElementById('filterToggleBtn');
    const filterCard = document.getElementById('filterCard');

    if (filterToggleBtn && filterCard) {
        filterToggleBtn.addEventListener('click', function() {
            if (filterCard.classList.contains('hidden')) {
                filterCard.classList.remove('hidden');
                filterToggleBtn.innerHTML = '<i class="fas fa-times mr-2"></i> {{ translateText("فلټر بندول") }}';
                filterToggleBtn.classList.add('bg-red-100', 'text-red-700');
                filterToggleBtn.classList.remove('bg-white', 'text-gray-700');
            } else {
                filterCard.classList.add('hidden');
                filterToggleBtn.innerHTML = '<i class="fas fa-filter mr-2"></i> {{ translateText("فلټر") }}';
                filterToggleBtn.classList.remove('bg-red-100', 'text-red-700');
                filterToggleBtn.classList.add('bg-white', 'text-gray-700');
            }
        });
    }
});
</script>
@endsection












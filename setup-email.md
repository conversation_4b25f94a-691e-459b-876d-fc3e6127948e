# Email Configuration Setup Guide

## Current Issue
Your emails are not being sent because the system is configured to only log emails instead of sending them.

## Solution Options

### Option 1: Gmail SMTP (Recommended for Testing)

1. **Update your .env file with these settings:**
```
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="KU Mental Health"
```

2. **Get Gmail App Password:**
   - Go to your Google Account settings
   - Enable 2-Factor Authentication
   - Go to Security > App passwords
   - Generate an app password for "Mail"
   - Use this app password (not your regular password)

3. **Replace the placeholders:**
   - Replace `<EMAIL>` with your actual Gmail address
   - Replace `your-app-password` with the app password from step 2

### Option 2: Mailtrap (For Testing)

1. **Sign up at mailtrap.io (free)**
2. **Get your credentials from Mailtrap dashboard**
3. **Update .env:**
```
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=your-mailtrap-username
MAIL_PASSWORD=your-mailtrap-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="KU Mental Health"
```

### Option 3: Local Testing with MailHog

1. **Install MailHog:**
   - Download from: https://github.com/mailhog/MailHog
   - Run MailHog.exe
   - Access web interface at: http://localhost:8025

2. **Update .env:**
```
MAIL_MAILER=smtp
MAIL_HOST=127.0.0.1
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="KU Mental Health"
```

## After Configuration

1. **Clear config cache:**
```bash
php artisan config:clear
php artisan cache:clear
```

2. **Test the email system**
3. **Check logs if emails fail:**
   - Check `storage/logs/laravel.log` for errors

## Quick Gmail Setup (Recommended)

1. Use your Gmail account: <EMAIL>
2. Enable 2FA on your Google account
3. Generate app password
4. Update .env with your details
5. Run: `php artisan config:clear`
6. Test sending email

## Troubleshooting

- If emails still don't send, check `storage/logs/laravel.log`
- Make sure your internet connection is working
- Verify Gmail app password is correct
- Check if Gmail is blocking the connection

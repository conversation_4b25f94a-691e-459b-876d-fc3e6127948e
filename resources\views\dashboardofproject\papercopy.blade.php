@extends('layouts/Admin')

@section('title','Papers Management')

@section('contents')
<!-- Main Content -->
<div class="p-4 md:p-8 min-h-screen transition-all w-full bg-gradient-to-br from-blue-50 to-indigo-50">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div class="flex items-center mb-4 md:mb-0">
            <div class="w-12 h-12 rounded-full bg-gradient-to-r from-green-600 to-teal-600 flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300">
                <i class="fas fa-file-alt text-white text-xl"></i>
            </div>
            <h1 class="text-2xl md:text-3xl font-bold text-gray-800 ml-4">{{ translateText('د مقالو مدیریت') }}</h1>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('papers.index') }}" class="bg-gradient-to-r from-green-600 to-teal-600 text-white px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-300">
                <i class="fas fa-sync-alt mr-2"></i> {{ translateText('تازه کول') }}
            </a>
        </div>
    </div>

    <!-- Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Form Card -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-2xl shadow-xl border border-green-100 overflow-hidden transform transition-all duration-300 hover:shadow-2xl">
                <div class="bg-gradient-to-r from-green-600 to-teal-600 px-6 py-4">
                    <h2 class="text-xl md:text-2xl font-bold text-white flex items-center">
                        <i class="fas fa-plus-circle mr-2"></i> {{ isset($editPaper) ? translateText('مقاله تازه کول') : translateText('نوې مقاله') }}
                    </h2>
                </div>

                <div class="p-6">
                    <form action="{{ isset($editPaper) ? route('papers.update', $editPaper->A_Id) : route('bookd.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                        @csrf
                        @if(isset($editPaper))
                            @method('PUT')
                        @endif

                        <div class="grid grid-cols-1 gap-6">
                            <div class="group">
                                <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-green-600 transition-colors">{{ translateText('عنوان') }}</label>
                                <input type="text" name="title" value="{{ isset($editPaper) ? $editPaper->A_Title : old('title') }}" required
                                    class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300">
                            </div>

                            <div class="group">
                                <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-green-600 transition-colors">{{ translateText('لیکوال') }}</label>
                                <input type="text" name="author" value="{{ isset($editPaper) ? $editPaper->A_Author : old('author') }}" required
                                    class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300">
                            </div>

                            <div class="group">
                                <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-green-600 transition-colors">{{ translateText('د خپرېدو نېټه') }}</label>
                                <input type="date" name="publishdate" value="{{ isset($editPaper) ? $editPaper->A_Publication_date : old('publishdate') }}" required
                                    class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300">
                            </div>

                            <div class="group">
                                <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-green-600 transition-colors">{{ translateText('ژبه') }}</label>
                                <select name="language" class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300">
                                    <option value="pashto" {{ (isset($editPaper) && $editPaper->A_Language == 'pashto') ? 'selected' : '' }}>{{ translateText('پښتو') }}</option>
                                    <option value="dari" {{ (isset($editPaper) && $editPaper->A_Language == 'dari') ? 'selected' : '' }}>{{ translateText('دري') }}</option>
                                    <option value="english" {{ (isset($editPaper) && $editPaper->A_Language == 'english') ? 'selected' : '' }}>{{ translateText('انګلیسي') }}</option>
                                </select>
                            </div>

                            <div class="group">
                                <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-green-600 transition-colors">{{ translateText('نوع') }}</label>
                                <select name="type" class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300">
                                    <option value="مقالي" selected>{{ translateText('مقالي') }}</option>
                                </select>
                            </div>

                            <div class="group">
                                <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-green-600 transition-colors">{{ translateText('کټګوري') }}</label>
                                <select name="catagory" class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300">
                                    <option value="academic" {{ (isset($editPaper) && $editPaper->A_catagory == 'academic') ? 'selected' : '' }}>{{ translateText('علمي') }}</option>
                                    <option value="research" {{ (isset($editPaper) && $editPaper->A_catagory == 'research') ? 'selected' : '' }}>{{ translateText('څیړنیز') }}</option>
                                </select>
                            </div>

                            <div class="group">
                                <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-green-600 transition-colors">{{ translateText('فایل') }}</label>
                                <input type="file" name="file" class="w-full p-3 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300">
                                @if(isset($editPaper) && $editPaper->A_File)
                                    <p class="mt-2 text-sm text-gray-600">{{ translateText('اوسنی فایل') }}: {{ $editPaper->A_File }}</p>
                                @endif
                            </div>
                        </div>

                        <div class="flex justify-end space-x-4">
                            <button type="submit" class="px-6 py-3 bg-gradient-to-r from-green-600 to-teal-600 text-white rounded-lg hover:from-green-700 hover:to-teal-700 transition-all duration-300 transform hover:-translate-y-1 shadow-md hover:shadow-lg flex items-center justify-center">
                                <i class="fas fa-save mr-2"></i> {{ isset($editPaper) ? translateText('تازه کول') : translateText('ثبتول') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Table Card -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-2xl shadow-xl border border-green-100 overflow-hidden">
                <div class="bg-gradient-to-r from-green-600 to-teal-600 px-6 py-4">
                    <h2 class="text-xl md:text-2xl font-bold text-white flex items-center">
                        <i class="fas fa-list mr-2"></i> {{ translateText('د مقالو لیست') }}
                    </h2>
                </div>

                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full bg-white rounded-lg overflow-hidden">
                            <thead class="bg-gray-100">
                                <tr class="text-gray-700">
                                    <th class="py-3 px-4 text-left">{{ translateText('عنوان') }}</th>
                                    <th class="py-3 px-4 text-left">{{ translateText('لیکوال') }}</th>
                                    <th class="py-3 px-4 text-left">{{ translateText('ژبه') }}</th>
                                    <th class="py-3 px-4 text-left">{{ translateText('کټګوري') }}</th>
                                    <th class="py-3 px-4 text-left">{{ translateText('عملیات') }}</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                @forelse($papers as $paper)
                                <tr class="hover:bg-gray-50">
                                    <td class="py-3 px-4">{{ $paper->A_Title }}</td>
                                    <td class="py-3 px-4">{{ $paper->A_Author }}</td>
                                    <td class="py-3 px-4">{{ $paper->A_Language }}</td>
                                    <td class="py-3 px-4">{{ $paper->A_catagory }}</td>
                                    <td class="py-3 px-4 flex">
                                        <a href="{{ route('papers.edit', $paper->A_Id) }}"
                                            class="px-3 py-1 bg-gradient-to-r from-green-500 to-teal-500 text-white rounded hover:from-green-600 hover:to-teal-600 transition-all duration-300 mr-2">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="{{ route('papers.destroy', $paper->A_Id) }}" method="POST" onsubmit="return confirm('{{ translateText('آیا تاسو ډاډه یاست چې دا مقاله غواړئ لرې کړئ؟') }}')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="px-3 py-1 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded hover:from-red-600 hover:to-pink-600 transition-all duration-300">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="py-6 text-center text-gray-500">{{ translateText('هیڅ مقاله شتون نلري') }}</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

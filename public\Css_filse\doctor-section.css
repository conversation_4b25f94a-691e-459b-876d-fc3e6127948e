  * {
      box-sizing: border-box;
      font-family: sans-serif;
    }

    body {
      margin: 0;
      padding: 20px;
      background: #f0f4f8;
    }
 #search_sec{
    width: 50%;
    height: 3em;
    direction: rtl;
    border: 3px solid white;
    border-radius: 10px;
    margin: 20px auto;
    background-color: azure;
    display: flex;
    }
    #search_sec input{
    width: 95%;
    height: 100%;
    padding-right: 1em;
    border: none;
    margin-right: 1em;
    border-radius: 10px;
    background-color: azure;
    outline: none;
    color: black;
    }
    #search_sec button{
    border: none;
    background-color: azure;
    color: black;
    font-size: 1.5em;
  
    }
    #ssd{
      margin-top: 4.6em;
      
    }
    #ssd h1{
      color: white;
      text-align: center;
    }
    .card-container {
      margin-top: 30px;
      direction: rtl;
      display: flex;
      flex-wrap: wrap;
      gap: 30px;
      justify-content: center;
      padding: 0 20px;
    }

    .card {
      background: #e6f0fa;
      border-radius: 16px;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      width: 100%;
      max-width: 500px;
      padding: 20px;
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      gap: 20px;
      transition: all 0.3s ease;
    }

    .card img {
      width: 10em;
      height: 10em;
      object-fit: cover;
      border-radius: 50%;
      flex-shrink: 0;
    }

    .card h3 {
      color: #006400;
      margin: 0 0 10px;
      font-size: 18px;
    }

    .card p {
      font-size: 14px;
      color: #333;
      margin: 20px 0;
    }

    .card .icons {
      margin-top: 12px;
      display: flex;
      gap: 15px;
    }

    .card .icons i {
      font-size: 20px;
      color: green;
      cursor: pointer;
    }

    .card .icons a {
      color: green;
      text-decoration: none;
    }

    .card .icons a:hover i {
      color: darkgreen;
    }

    /* Show More Button Styling */
    .show-more-container {
      text-align: center;
      margin: 40px 0;
    }

    #showMoreDoctors {
      background: linear-gradient(135deg, #006400 0%, #228B22 100%);
      color: white;
      border: none;
      padding: 15px 30px;
      border-radius: 25px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 100, 0, 0.3);
    }

    #showMoreDoctors:hover {
      background: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 100, 0, 0.4);
    }

    #showMoreDoctors:active {
      transform: translateY(0);
    }

    .show-more-btn {
      margin-top: 20px;
      padding: 10px 20px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
    }

    /* No Results Styling */
    .no-results {
      text-align: center;
      padding: 60px 20px;
      color: #6c757d;
      font-size: 18px;
    }

    /* Show More Button */
    #showMoreDoctors {
      margin: 40px auto 20px;
      padding: 15px 30px;
      background: linear-gradient(135deg, #4361ee 0%, #3a36e0 100%);
      color: white;
      border: none;
      border-radius: 50px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      transition: all 0.3s ease;
      box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3);
      display: block;
    }

    #showMoreDoctors:hover {
      transform: translateY(-2px);
      box-shadow: 0 12px 25px rgba(67, 97, 238, 0.4);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .card-container {
        gap: 20px;
        padding: 0 15px;
      }

      .card {
        flex-direction: column;
        text-align: center;
        padding: 25px;
        max-width: 100%;
      }

      .card-content {
        order: 2;
        align-items: center;
      }

      .doctor-image {
        order: 1;
        align-self: center;
      }

      .doctor-image img {
        width: 100px;
        height: 100px;
      }

      .card h3 {
        font-size: 20px;
      }

      .contact-info {
        align-items: center;
      }

      .card .icons {
        justify-content: center;
      }
    }

    @media (max-width: 480px) {
      .card {
        padding: 20px;
        gap: 15px;
      }

      .doctor-image img {
        width: 80px;
        height: 80px;
      }

      .card h3 {
        font-size: 18px;
      }

      .card .description {
        font-size: 14px;
      }

      .contact-info p {
        font-size: 13px;
      }

      .card .icons a {
        width: 35px;
        height: 35px;
      }

      .card .icons a i {
        font-size: 16px;
      }
    }
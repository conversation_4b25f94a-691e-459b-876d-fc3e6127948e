<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PatientModel;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\PatientsExport;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\DoctorModel;
use App\Models\PatientAddModel;
use App\Models\QuestionerModel;
use App\Models\User;


// Alternative imports if the above doesn't work
// use PDF; // If you've defined an alias in config/app.php
// use Barryvdh\DomPDF\PDF; // For older versions

class PatientController extends Controller
{
 
    public function index()
    {
        // Get all patients with their address information using eager loading
        $patients = PatientModel::with('address')->paginate(10);

        // Get all doctors for the patient form dropdown
        $doctors = DoctorModel::all();

        // Get filter data for dropdowns
        $provinces = PatientModel::with('address')
            ->get()
            ->pluck('address.P_Province')
            ->filter()
            ->unique()
            ->values();

        $years = PatientModel::selectRaw('YEAR(created_at) as year')
            ->distinct()
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->filter();

        return view('dashboardofproject.patientcopy', compact('patients', 'doctors', 'provinces', 'years'));
    }

    /**
     * Filter patients based on criteria.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function filter(Request $request)
    {
        try {
            // Start with base query
            $query = PatientModel::with(['address', 'doctor']);

            // Apply gender filter
            if ($request->filled('gender')) {
                $query->where('Patient_Gender', $request->gender);
            }

            // Apply age range filter
            if ($request->filled('age_range')) {
                $ageRange = $request->age_range;
                switch ($ageRange) {
                    case '0-18':
                        $query->where('Patient_Age', '<=', 18);
                        break;
                    case '19-35':
                        $query->whereBetween('Patient_Age', [19, 35]);
                        break;
                    case '36-50':
                        $query->whereBetween('Patient_Age', [36, 50]);
                        break;
                    case '51+':
                        $query->where('Patient_Age', '>', 50);
                        break;
                }
            }

            // Apply province filter
            if ($request->filled('province')) {
                $query->whereHas('address', function($q) use ($request) {
                    $q->where('P_Province', $request->province);
                });
            }

            // Apply year filter
            if ($request->filled('year')) {
                $query->whereYear('created_at', $request->year);
            }

            // Get filtered patients
            $patients = $query->paginate(10);

            // Get all doctors for the patient form dropdown
            $doctors = DoctorModel::all();

            // Get filter data for dropdowns
            $provinces = PatientModel::with('address')
                ->get()
                ->pluck('address.P_Province')
                ->filter()
                ->unique()
                ->values();

            $years = PatientModel::selectRaw('YEAR(created_at) as year')
                ->distinct()
                ->orderBy('year', 'desc')
                ->pluck('year')
                ->filter();

            // Flash success message
            session()->flash('success', translateText('فلټر په بریالیتوب سره تطبیق شو!'));

            return view('dashboardofproject.patientcopy', compact('patients', 'doctors', 'provinces', 'years'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', translateText('تېروتنه: ') . $e->getMessage());
        }
    }

    /**
     * Store a newly created patient in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        try {
            // Begin transaction
            DB::beginTransaction();
            
            // Validate patient data
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'age' => 'required|numeric',
                'gender' => 'required|string',
                'phone' => 'nullable|string|max:20',
                'email' => 'nullable|email|max:255',
                'doctor_id' => 'required|exists:doctors,Dr_id',
                'province' => 'required|string|max:255',
                'district' => 'required|string|max:255',
                'village' => 'required|string|max:255',
            ]);
            
            // Create patient
            $patient = new PatientModel();
            $patient->Patiet_Name = $validatedData['name'];
            $patient->Patient_Age = $validatedData['age'];
            $patient->Patient_Gender = $validatedData['gender'];
            $patient->Patient_phone = $validatedData['phone'];
            $patient->Patient_email = $validatedData['email'];
            $patient->Dr_id = $validatedData['doctor_id'];
            $patient->save();
            
            // Create patient address
            $patientAddress = new PatientAddModel();
            $patientAddress->P_Province = $validatedData['province'];
            $patientAddress->P_Distract = $validatedData['district'];
            $patientAddress->P_Village = $validatedData['village'];
            $patientAddress->Patient_Id = $patient->Patient_id;
            $patientAddress->save();
            
            // Notification functionality removed
            
            // Commit transaction
            DB::commit();
            
            // Store notification in session
            Session::flash('notification', [
                'type' => 'success',
                'message' => 'ناروغ په بریالیتوب سره ثبت شو.',
                'patient_name' => $patient->Patiet_Name,
                'patient_id' => $patient->Patient_id
            ]);
            
            return redirect()->route('patient.index')
                ->with('success', 'ناروغ په بریالیتوب سره ثبت شو.');
                
        } catch (\Exception $e) {
            // Rollback transaction on error
            DB::rollBack();
            
            return redirect()->back()
                ->withInput()
                ->with('error', 'تېروتنه: ' . $e->getMessage());
        }
    }
    
    /**
     * Show the form for editing the specified patient.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $patient = PatientModel::with('address')->findOrFail($id);
        $patients = PatientModel::with('address')->paginate(10);
        $doctors = DoctorModel::all();
        
        return view('dashboardofproject.patientcopy', compact('patient', 'patients', 'doctors'));
    }
    
    /**
     * Update the specified patient in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'Patiet_Name' => 'required|string|max:255',
            'Patient_Age' => 'required|numeric',
            'Patient_Gender' => 'required|string',
            'Patient_phone' => 'nullable|string',
            'Patient_email' => 'nullable|email',
            'P_Province' => 'required|string',
            'P_Distract' => 'required|string',
            'P_Village' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ]);
        }

        try {
            // Find the patient
            $patient = PatientModel::findOrFail($id);

            // Update patient data
            $patient->Patiet_Name = $request->Patiet_Name;
            $patient->Patient_Age = $request->Patient_Age;
            $patient->Patient_Gender = $request->Patient_Gender;
            $patient->Patient_phone = $request->Patient_phone;
            $patient->Patient_email = $request->Patient_email;
            $patient->save();

            // Update address data
            $address = $patient->address;
            if (!$address) {
                // Create new address if it doesn't exist
                $address = new PatientAddModel();
                $address->Patient_Id = $patient->Patient_id;
            }

            $address->P_Province = $request->P_Province;
            $address->P_Distract = $request->P_Distract;
            $address->P_Village = $request->P_Village;
            $address->save();

            return response()->json([
                'success' => true,
                'message' => 'د ناروغ معلومات په بریالیتوب سره تازه شول!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'تېروتنه: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            // Find the patient
            $patient = PatientModel::findOrFail($id);
            $patientName = $patient->Patiet_Name;
            
            // Delete the patient (address will be deleted automatically due to cascade)
            $patient->delete();
            
            // Store notification in session
            Session::flash('notification', [
                'type' => 'success',
                'message' => 'ناروغ په بریالیتوب سره لرې شو.',
                'patient_name' => $patientName
            ]);
            
            return redirect()->route('patient.index')
                ->with('success', 'ناروغ په بریالیتوب سره لرې شو.');
                
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'تېروتنه: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function show($id)
    {
        try {
            $patient = PatientModel::with(['address', 'doctor', 'questions'])->findOrFail($id);
            
            return view('dashboardofproject.patient_details', compact('patient'));
        } catch (\Exception $e) {
            \Log::error('Error showing patient: ' . $e->getMessage());
            
            return redirect()->route('patient.index')
                ->with('error', 'تېروتنه: ناروغ ونه موندل شو. ' . $e->getMessage());
        }
    }

    /**
     * Store a patient with questions.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storePatientWithQuestions(Request $request)
    {
        try {
            // د ټولو ورودي معلوماتو لاګ کول
            \Log::info('Request data received in PatientController', ['data' => $request->all()]);
            
            // ټرانزکشن پیل کول
            DB::beginTransaction();
            
            // 1. د ناروغ معلومات ذخیره کول
            $patient = new PatientModel();
            $patient->Patiet_Name = $request->name;
            $patient->Patient_Age = $request->age;
            $patient->Patient_Gender = $request->gender;
            $patient->Patient_phone = $request->phone;
            $patient->Patient_email = $request->email ?? null;
            $patient->Dr_id = $request->Dr_id;
            $patient->save();
            
            \Log::info('Patient saved', ['patient_id' => $patient->Patient_id]);
            
            // 2. د ناروغ پته ذخیره کول (که اړین وي)
            if ($request->has('province') && $request->has('district') && $request->has('village')) {
                $patientAddress = new PatientAddModel();
                $patientAddress->Patient_Id = $patient->Patient_id;
                $patientAddress->P_Province = $request->province;
                $patientAddress->P_Distract = $request->district;
                $patientAddress->P_Village = $request->village;
                $patientAddress->save();
                
                \Log::info('Patient address saved');
            }
            
            // 3. د پوښتنو ذخیره کول
            // د ډیټابیس څخه د پوښتنو ترلاسه کول
            $databaseQuestions = \App\Models\Question::orderBy('created_at', 'asc')->get();

            // که د ډیټابیس څخه پوښتنې شتون ولري، نو هغه وکاروئ، که نه نو د پخوانیو پوښتنو څخه کار واخلئ
            $questions = $databaseQuestions->count() > 0
                ? $databaseQuestions->pluck('question_text')->toArray()
                : [
                    "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي د تقویت درمل ته اړتیا لري؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دي د سستي او کمزوري احساس کړی؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې د ناروغۍ احساس کړی؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه د سر درد لري؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي سر دي په دستمال وتړم تر څو د درد کم شي؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي ګرم یا یخ کیږي؟",
                    "آیا له دیرشو(۳۰) ورځو راهیسي د تشویش له امله بې خوبه شوی یې؟",
                    "آیا د خوب په منځ کې راویښیږې او خوب دې ګډوډیږي؟",
                    "آیا همېشه د فشار لاندې یې؟"
                ];

            $questionCount = 0;
            foreach ($request->all() as $key => $value) {
                if (strpos($key, 'q') === 0 && is_numeric(substr($key, 1))) {
                    $questionIndex = (int)substr($key, 1);
                    $questionText = $questions[$questionIndex] ?? "پوښتنه نمبر " . ($questionIndex + 1);

                    \Log::info("Processing question", [
                        'index' => $questionIndex,
                        'text' => $questionText,
                        'answer' => $value
                    ]);
                    
                    // د پوښتنې ځواب ذخیره کول
                    $question = new QuestionerModel();
                    $question->Q_Discription = $questionText;
                    $question->Question_No = $questionIndex + 1;

                    // یوازې د غوره شوي ځواب ذخیره کول
                    $selectedAnswer = match($value) {
                        'A' => 'هیڅ نه',
                        'B' => 'لږه اندازه',
                        'C' => 'لږه ډېره اندازه',
                        'D' => 'ډېره اندازه',
                        default => $value
                    };

                    // یوازې د غوره شوي ځواب ستون ډک کول
                    $question->Selected_Option = $selectedAnswer;
                    
                    $question->Patient_Id = $patient->Patient_id;
                    $question->save();
                    
                    $questionCount++;
                    \Log::info("Question saved", ['id' => $question->Q_Id]);
                }
            }
            
            // ټرانزکشن کمیټ کول
            DB::commit();
            \Log::info('Transaction committed successfully', ['questions_saved' => $questionCount]);
            
            // Create notification for patient with questionnaire registration
            \App\Models\Notification::createPatientNotification(
                'questionnaire_completed',
                'نوی ناروغ د پوښتنلیک سره ثبت شو',
                'د ' . $patient->Patiet_Name . ' نوم ناروغ د پوښتنلیک سره ثبت شو. ' . $questionCount . ' پوښتنې ثبت شوي.',
                $patient->Patient_id,
                $patient->Patiet_Name,
                [
                    'age' => $patient->Patient_Age,
                    'gender' => $patient->Patient_Gender,
                    'phone' => $patient->Patient_phone,
                    'email' => $patient->Patient_email,
                    'registration_date' => now()->format('Y-m-d H:i:s')
                ],
                [
                    'total_questions' => $questionCount,
                    'registration_type' => 'patient_with_questionnaire'
                ]
            );
            
            return redirect()->route('QuestionS')->with('success', 'معلومات او سوالونه په بریالیتوب سره ثبت شول!');
            
        } catch (\Exception $e) {
            // ټرانزکشن رولبک کول
            DB::rollBack();
            
            // د خطا معلوماتو ثبتول
            \Log::error('Error storing patient with questions in PatientController', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            return redirect()->route('QuestionS')->with('error', 'د معلوماتو په ثبتولو کې ستونزه: ' . $e->getMessage());
        }
    }

    /**
     * Display patient report page
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View|\Illuminate\Http\Response
     */
    public function report(Request $request)
    {
        try {
            // Get filter parameters
            $year = $request->input('year', Carbon::now()->year);
            $month = $request->input('month');
            $gender = $request->input('gender');
            
            // Start query
            $query = PatientModel::with('address');
            
            // Apply filters
            if ($year) {
                $query->whereYear('created_at', $year);
            }
            
            if ($month) {
                $query->whereMonth('created_at', $month);
            }
            
            if ($gender) {
                $query->where('Patient_Gender', $gender);
            }
            
            // Get patients
            $patients = $query->get();
            
            // Check if export is requested
            if ($request->has('export')) {
                $exportType = $request->input('export');
                
                if ($exportType === 'csv') {
                    // Set headers for CSV download
                    $headers = [
                        'Content-Type' => 'text/csv; charset=UTF-8',
                        'Content-Disposition' => 'attachment; filename="patients_report.csv"',
                    ];

                    // Create a callback function that will be used to stream the CSV content
                    $callback = function() use ($patients) {
                        // Open output stream
                        $file = fopen('php://output', 'w');

                        // Add UTF-8 BOM to fix Excel encoding issues
                        fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

                        // Add headers
                        fputcsv($file, [
                            'شمېره',
                            'نوم',
                            'عمر',
                            'جنسیت',
                            'اړیکه',
                            'ولایت',
                            'ولسوالي',
                            'کلی',
                            'نېټه',
                        ]);

                        // Add data rows
                        foreach ($patients as $patient) {
                            fputcsv($file, [
                                $patient->Patient_id,
                                $patient->Patiet_Name,
                                $patient->Patient_Age,
                                $patient->Patient_Gender,
                                $patient->Patient_phone ?? 'نشته',
                                $patient->address->P_Province ?? 'نشته',
                                $patient->address->P_Distract ?? 'نشته',
                                $patient->address->P_Village ?? 'نشته',
                                $patient->created_at ? $patient->created_at->format('Y-m-d') : 'نشته',
                            ]);
                        }

                        // Close the file
                        fclose($file);
                    };

                    // Stream the CSV file to the browser
                    return response()->stream($callback, 200, $headers);
                } elseif ($exportType === 'pdf') {
                    // Get monthly stats
                    $monthlyStats = $this->getMonthlyStats($patients);
                    
                    // Get yearly stats
                    $yearlyStats = $this->getYearlyStats($patients);
                    
                    // Generate PDF using mPDF
                    $mpdf = new \Mpdf\Mpdf([
                        'mode' => 'utf-8',
                        'format' => 'A4',
                        'margin_left' => 10,
                        'margin_right' => 10,
                        'margin_top' => 10,
                        'margin_bottom' => 10,
                        'tempDir' => sys_get_temp_dir(),
                    ]);
                    
                    $html = view('dashboardofproject.patientreport_pdf', compact(
                        'patients',
                        'monthlyStats',
                        'yearlyStats',
                        'year',
                        'month'
                    ))->render();
                    
                    // Create temporary file
                    $tempFile = tempnam(sys_get_temp_dir(), 'pdf_');
                    file_put_contents($tempFile, $html);
                    
                    // Use wkhtmltopdf command line tool to convert HTML to PDF
                    $outputFile = tempnam(sys_get_temp_dir(), 'pdf_output_');
                    $command = "wkhtmltopdf --encoding utf-8 $tempFile $outputFile";
                    exec($command);
                    
                    // Read the PDF file
                    $pdf = file_get_contents($outputFile);
                    
                    // Clean up temporary files
                    @unlink($tempFile);
                    @unlink($outputFile);
                    
                    // Return PDF response
                    return response($mpdf->Output('patients_report.pdf', 'S'), 200, [
                        'Content-Type' => 'application/pdf',
                        'Content-Disposition' => 'attachment; filename="patients_report.pdf"',
                    ]);
                }
            }
            
            // Get monthly stats
            $monthlyStats = $this->getMonthlyStats($patients);
            
            // Get yearly stats
            $yearlyStats = $this->getYearlyStats($patients);
            
            // Get years for dropdown
            $years = PatientModel::selectRaw('YEAR(created_at) as year')
                ->distinct()
                ->orderBy('year', 'desc')
                ->pluck('year');
                
            if ($years->isEmpty()) {
                $years = collect([Carbon::now()->year]);
            }
            
            return view('dashboardofproject.patientreport', compact(
                'patients',
                'monthlyStats',
                'yearlyStats',
                'years',
                'year',
                'month',
                'gender'
            ));
            
        } catch (\Exception $e) {
            \Log::error('Error generating patient report: ' . $e->getMessage());
            
            return redirect()->route('patient.index')
                ->with('error', 'تېروتنه: د راپور جوړولو پر مهال ستونزه رامنځته شوه. ' . $e->getMessage());
        }
    }

    /**
     * Get monthly statistics for patients
     *
     * @param  \Illuminate\Support\Collection  $patients
     * @return array
     */
    private function getMonthlyStats($patients)
    {
        $monthlyStats = [];
        for ($i = 1; $i <= 12; $i++) {
            $monthlyStats[$i] = [
                'male' => 0,
                'female' => 0,
                'total' => 0
            ];
        }
        
        foreach ($patients as $patient) {
            if ($patient->created_at) {
                $month = $patient->created_at->month;
                $monthlyStats[$month]['total']++;
                
                if ($patient->Patient_Gender == 'نارینه') {
                    $monthlyStats[$month]['male']++;
                } else if ($patient->Patient_Gender == 'ښځینه') {
                    $monthlyStats[$month]['female']++;
                }
            }
        }
        
        return $monthlyStats;
    }

    /**
     * Get yearly statistics for patients
     *
     * @param  \Illuminate\Support\Collection  $patients
     * @return array
     */
    private function getYearlyStats($patients)
    {
        $maleCount = $patients->where('Patient_Gender', 'نارینه')->count();
        $femaleCount = $patients->where('Patient_Gender', 'ښځینه')->count();
        
        return [
            'male' => $maleCount,
            'female' => $femaleCount,
            'total' => $patients->count()
        ];
    }
}






































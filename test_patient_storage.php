<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\PatientModel;
use App\Models\PatientAddModel;
use App\Models\QuestionerModel;

try {
    // Create a test patient
    $patient = new PatientModel();
    $patient->Patiet_Name = 'احمد خان';
    $patient->Patient_Age = 35;
    $patient->Patient_Gender = 'نارینه';
    $patient->Patient_phone = '**********';
    $patient->Patient_email = '<EMAIL>';
    $patient->Dr_id = 1; // Assuming doctor with ID 1 exists
    $patient->save();

    echo "Patient created with ID: " . $patient->Patient_id . "\n";

    // Create patient address
    $address = new PatientAddModel();
    $address->Patient_Id = $patient->Patient_id;
    $address->Patient_Country = 'افغانستان';
    $address->Patient_Province = 'کابل';
    $address->Patient_Distract = 'کابل ښار';
    $address->Patient_Village = 'شهر نو';
    $address->save();

    echo "Patient address created\n";

    // Create sample questions and answers
    $questions = [
        "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟",
        "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي د تقویت درمل ته اړتیا لري؟",
        "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دي د سستي او کمزوري احساس کړی؟"
    ];

    $answers = ['A', 'B', 'C']; // Sample answers

    foreach ($questions as $index => $questionText) {
        $questionModel = new QuestionerModel();
        $questionModel->Q_Discription = $questionText;
        $questionModel->Question_No = $index + 1;
        $questionModel->Patient_Id = $patient->Patient_id;
        $questionModel->A = 'هیڅ نه';
        $questionModel->B = 'لږه اندازه';
        $questionModel->C = 'لږه ډېره اندازه';
        $questionModel->D = 'ډېره اندازه';
        
        $answer = $answers[$index];
        $questionModel->Selected_Option = match($answer) {
            'A' => 'هیڅ نه',
            'B' => 'لږه اندازه',
            'C' => 'لږه ډېره اندازه',
            'D' => 'ډېره اندازه',
            default => $answer
        };
        
        $questionModel->save();
        echo "Question " . ($index + 1) . " saved with answer: " . $questionModel->Selected_Option . "\n";
    }

    echo "\nTest patient with questions created successfully!\n";
    echo "Check the admin panel at http://127.0.0.1:8000/patientd to see the data.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

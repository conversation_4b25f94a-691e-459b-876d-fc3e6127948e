@extends('layouts/Admin')
@section('title','Notifications')
@section('contents')
<div class="container-fluid px-4 py-6">
    <!-- Professional Header -->
    <div class="bg-white rounded-2xl shadow-xl mb-8 border-0 overflow-hidden transform transition-all duration-300 hover:shadow-2xl">
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl md:text-3xl font-bold text-white flex items-center">
                    <i class="fas fa-bell mr-3"></i>
                    {{ translateText('د ناروغانو خبرتیاوې') }}
                </h1>
                <div class="flex items-center space-x-4">
                    <div class="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                        <span class="text-white font-medium">
                            {{ translateText('ټولې') }}: {{ $totalCount }}
                        </span>
                    </div>
                    <div class="bg-white bg-opacity-20 rounded-lg px-4 py-2">
                        <span class="text-white font-medium">
                            {{ translateText('نالوستل شوي') }}: {{ $unreadCount }}
                        </span>
                    </div>
                    <button onclick="markAllAsRead()" class="bg-white text-blue-600 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium">
                        <i class="fas fa-check-double mr-2"></i>
                        {{ translateText('ټول د لوستل شوي په توګه نښه کړئ') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-bell text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-800">{{ translateText('ټولې خبرتیاوې') }}</h3>
                    <p class="text-2xl font-bold text-blue-600">{{ $totalCount }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-bell-slash text-red-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-800">{{ translateText('نالوستل شوي') }}</h3>
                    <p class="text-2xl font-bold text-red-600">{{ $unreadCount }}</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <h3 class="text-lg font-semibold text-gray-800">{{ translateText('لوستل شوي') }}</h3>
                    <p class="text-2xl font-bold text-green-600">{{ $totalCount - $unreadCount }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Debug information removed -->

    @if(session('error'))
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        {{ session('error') }}
    </div>
    @endif

    <!-- Notifications List -->
    <div class="bg-white rounded-2xl shadow-xl border-0 overflow-hidden">
        <div class="p-6">
            @if($notifications && $notifications->count() > 0)
                <div class="space-y-4">
                    @foreach($notifications as $notification)
                        <div class="notification-item {{ $notification->isUnread() ? 'unread' : 'read' }} p-6 rounded-xl border transition-all duration-300 hover:shadow-lg cursor-pointer"
                             onclick="viewNotification({{ $notification->id }}, {{ $notification->patient_id }})">
                            
                            <div class="flex items-start space-x-4">
                                <!-- Notification Icon -->
                                <div class="notification-icon w-12 h-12 rounded-full flex items-center justify-center text-white">
                                    <i class="{{ $notification->icon }}"></i>
                                </div>

                                <!-- Notification Content -->
                                <div class="flex-1">
                                    <div class="flex justify-between items-start mb-2">
                                        <h3 class="font-bold text-lg text-gray-900">{{ $notification->title }}</h3>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-sm text-gray-500">{{ $notification->time_ago }}</span>
                                            @if($notification->isUnread())
                                                <span class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></span>
                                            @endif
                                        </div>
                                    </div>
                                    
                                    <p class="text-gray-700 mb-3">{{ $notification->message }}</p>
                                    
                                    <!-- Patient Information -->
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <span class="font-medium text-gray-600">{{ translateText('د ناروغ نوم') }}:</span>
                                                <span class="text-gray-900">{{ $notification->patient_name }}</span>
                                            </div>
                                            <div>
                                                <span class="font-medium text-gray-600">{{ translateText('د ناروغ شمېره') }}:</span>
                                                <span class="text-gray-900">{{ $notification->patient_id }}</span>
                                            </div>
                                            @if($notification->patient_data && isset($notification->patient_data['age']))
                                            <div>
                                                <span class="font-medium text-gray-600">{{ translateText('عمر') }}:</span>
                                                <span class="text-gray-900">{{ $notification->patient_data['age'] }} {{ translateText('کلنۍ') }}</span>
                                            </div>
                                            @endif
                                            @if($notification->patient_data && isset($notification->patient_data['gender']))
                                            <div>
                                                <span class="font-medium text-gray-600">{{ translateText('جنس') }}:</span>
                                                <span class="text-gray-900">{{ $notification->patient_data['gender'] }}</span>
                                            </div>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex items-center justify-between mt-4">
                                        <div class="flex space-x-3">
                                            <button onclick="event.stopPropagation(); viewPatientDetails({{ $notification->id }}, {{ $notification->patient_id }})" 
                                                    class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center">
                                                <i class="fas fa-eye mr-2"></i>
                                                {{ translateText('د ناروغ تفصیلات') }}
                                            </button>
                                            
                                            @if($notification->isUnread())
                                            <button onclick="event.stopPropagation(); markAsRead({{ $notification->id }})" 
                                                    class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors flex items-center">
                                                <i class="fas fa-check mr-2"></i>
                                                {{ translateText('لوستل شوی') }}
                                            </button>
                                            @endif
                                        </div>
                                        
                                        <button onclick="event.stopPropagation(); deleteNotification({{ $notification->id }})" 
                                                class="text-red-500 hover:text-red-700 transition-colors">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="mt-8">
                    {{ $notifications->links() }}
                </div>
            @else
                <!-- Empty State -->
                <div class="text-center py-16">
                    <i class="fas fa-bell-slash text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">{{ translateText('هیڅ خبرتیاوې نشته') }}</h3>
                    <p class="text-gray-500">{{ translateText('کله چې ناروغان پوښتنلیک بشپړ کړي، دلته به یې وګورئ') }}</p>
                </div>
            @endif
        </div>
    </div>
</div>

<style>
.notification-item.unread {
    background: linear-gradient(145deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.05) 100%);
    border-color: rgba(59, 130, 246, 0.3);
    border-left: 4px solid #3b82f6;
}

.notification-item.read {
    background: #f9fafb;
    border-color: #e5e7eb;
}

.notification-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.notification-icon {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.notification-item.unread .notification-icon {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}
</style>

<script>
// Mark notification as read
function markAsRead(notificationId) {
    fetch(`/notifications/${notificationId}/mark-as-read`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload(); // Refresh page to update UI
        } else {
            alert('خطا: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('د خبرتیا د لوستل شوي په توګه نښه کولو کې تېروتنه');
    });
}

// Mark all notifications as read
function markAllAsRead() {
    if (confirm('{{ translateText("ایا تاسو غواړئ ټولې خبرتیاوې د لوستل شوي په توګه نښه کړئ؟") }}')) {
        fetch('/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload(); // Refresh page to update UI
            } else {
                alert('خطا: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('د خبرتیاوو د لوستل شوي په توګه نښه کولو کې تېروتنه');
        });
    }
}

// View patient details
function viewPatientDetails(notificationId, patientId) {
    window.location.href = `/notifications/${notificationId}/patient/${patientId}`;
}

// View notification (general click)
function viewNotification(notificationId, patientId) {
    // Mark as read and view patient details
    viewPatientDetails(notificationId, patientId);
}

// Delete notification
function deleteNotification(notificationId) {
    if (confirm('{{ translateText("ایا تاسو غواړئ دا خبرتیا ړنګه کړئ؟") }}')) {
        fetch(`/notifications/${notificationId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload(); // Refresh page to update UI
            } else {
                alert('خطا: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('د خبرتیا د ړنګولو کې تېروتنه');
        });
    }
}

// Auto-refresh notifications every 30 seconds
setInterval(function() {
    // Only refresh if there are unread notifications
    if ({{ $unreadCount }} > 0) {
        location.reload();
    }
}, 30000);
</script>
@endsection

 



 
    #search_sec{
    width: 50%;
    height: 3em;
    direction: rtl;
    border: 3px solid white;
    border-radius: 10px;
    margin: 20px auto;
    background-color: azure;
    display: flex;
    }
    #search_sec input{
    width: 95%;
    height: 100%;
    padding-right: 1em;
    border: none;
    margin-right: 1em;
    border-radius: 10px;
    background-color: azure;
    outline: none;
    color: black;
    }
    #search_sec button{
    border: none;
    background-color: azure;
    color: black;
    font-size: 1.5em;
  
    }
    #ssd{
      margin-top: 4.6em;
      
    }
    #ssd h1{
      color: white;
      text-align: center;
    }
        body {
      background:  #1d2a3a;
     
    
    }

     
     
    

    .team-container {
       padding: 40px;
      font-family: sans-serif;
      display: flex;
      flex-wrap: wrap;
      gap: 30px;
      justify-content: center;
    }

    .team-card {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      width: 340px;
      padding: 0;
      overflow: hidden;
      direction: rtl;
      display: flex;
      flex-direction: column;
      transition: transform 0.3s ease;
    }

    .team-card:hover {
      transform: translateY(-5px);
    }

    .team-card img.top-img {
      width: 100%;
      height: 180px;
      object-fit: cover;
    }

    .team-card .card-body {
      padding: 20px;
      flex-grow: 1;
    }

    .team-card h3 {
      margin: 0 0 10px;
      color: #1a202c;
      font-size: 18px;
    }

    .team-card p {
      font-size: 14px;
      color: #4a5568;
      margin: 0 0 15px;
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-top: 1px solid #e2e8f0;
      background-color: #f7fafc;
    }

    .card-footer .profile {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .card-footer img.profile-img {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
    }

    .card-footer a {
      font-size: 13px;
      color: orange;
      text-decoration: none;
      background-color: #225483;
      border-radius: 10px;
      padding: 7px 20px;
    }

    @media (max-width: 768px) {
      .team-card {
        width: 100%;
      }
    }

    .hidden-card {
      display: none;
    }

    .show-more-btn {
      display: block;
      margin: 30px auto;
      padding: 10px 25px;
      background-color: #225483;
      color: #fff;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
    }

    .show-more-btn:hover {
      background-color: #1a3b60;
    }
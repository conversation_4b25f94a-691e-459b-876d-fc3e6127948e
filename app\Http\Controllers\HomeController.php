<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\VideoModel;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index(Request $request)
    {
        // Get videos with pagination (8 per page)
        $videos = VideoModel::latest()->paginate(8);

        // If this is an AJAX request, return only the videos section
        if ($request->ajax()) {
            return view('partials.videos_grid', compact('videos'))->render();
        }

        // Pass videos to the view
        return view('index', compact('videos'));
    }
}


@extends('layouts/Main')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow-lg">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">{{ translateText('د ناروغ نتیجه') }}</h3>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="patient-info mb-4">
                        <h4 class="border-bottom pb-2 mb-3">{{ translateText('د ناروغ معلومات') }}</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>{{ translateText('نوم:') }}</strong> {{ $patient->Patiet_Name }}</p>
                                <p><strong>{{ translateText('عمر:') }}</strong> {{ $patient->Patient_Age }}</p>
                                <p><strong>{{ translateText('جنسیت:') }}</strong> {{ $patient->Patient_Gender }}</p>
                                <p><strong>{{ translateText('تلیفون:') }}</strong> {{ $patient->Patient_phone }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>{{ translateText('هیواد:') }}</strong> {{ $patient->address->P_Country }}</p>
                                <p><strong>{{ translateText('ولایت:') }}</strong> {{ $patient->address->P_Province }}</p>
                                <p><strong>{{ translateText('ولسوالی:') }}</strong> {{ $patient->address->P_Distract }}</p>
                                <p><strong>{{ translateText('کلی:') }}</strong> {{ $patient->address->P_Village }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="result-summary mb-4">
                        <h4 class="border-bottom pb-2 mb-3">{{ translateText('د پوښتنلیک نتیجه') }}</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5>{{ translateText('ټولټال نمبر') }}</h5>
                                        <h2 class="display-4">{{ $totalScore }}</h2>
                                        <p class="lead">{{ translateText('د 30 څخه') }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h5>{{ translateText('تشخیص') }}</h5>
                                        <h2 class="display-4">{{ $diagnosis }}</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="recommendations mb-4">
                        <h4 class="border-bottom pb-2 mb-3">{{ translateText('سپارښتنې') }}</h4>
                        <div class="card">
                            <div class="card-body">
                                @if($totalScore <= 10)
                                    <p>{{ translateText('تاسو نارمل حالت لرئ. د روغتیا ساتنې لپاره منظم ورزش وکړئ او صحي خواړه وخورئ.') }}</p>
                                @elseif($totalScore <= 20)
                                    <p>{{ translateText('تاسو خفیف اضطراب لرئ. د سترس کمولو لپاره تنفسي تمرینونه وکړئ او کافي خوب وکړئ.') }}</p>
                                @elseif($totalScore <= 30)
                                    <p>{{ translateText('تاسو متوسط اضطراب لرئ. د مشورې لپاره روغتیایي مرکز ته مراجعه وکړئ.') }}</p>
                                @else
                                    <p>{{ translateText('تاسو شدید اضطراب لرئ. ژر تر ژره د متخصص ډاکټر سره وګورئ.') }}</p>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <a href="{{ route('questions') }}" class="btn btn-primary">
                            {{ translateText('بیرته') }}
                        </a>
                        <button onclick="window.print()" class="btn btn-success ms-2">
                            {{ translateText('چاپ') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: sans-serif;
  background-image: url('../imagese/log_img.jpg');
  background-size: cover;
  background-position: center;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-box {
  direction: rtl;
  margin-top: 3em;
  background-color: rgba(0, 0, 0, 0.001);
  padding: 40px;
  border-radius: 10px;
  width: 500px;
  color: white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.999);
}

.login-box h2 {
  text-align: center;
  margin-bottom: 30px;
}

.input-box {
  position: relative;
  margin-bottom: 20px;
}

.input-box input {
  width: 100%;
  padding: 12px 40px 12px 12px;
  border: none;
  border-radius: 5px;
  outline: none;
  font-size: 14px;
}

.input-box i {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 10px;
  color: #999;
}

.options {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  margin-bottom: 20px;
}

.options input {
  margin-left: 5px;
}

.login-box button {
  width: 120px;
  background-color: orangered;
  border: none;
  padding: 8px;
  border-radius: 5px;
  font-size: 14px;
  color: white;
  cursor: pointer;
  transition: background 0.3s;
  margin: 0 auto;
  display: block;
}

.login-box button:hover {
  background-color: darkorange;
}

footer {
  position: absolute;
  bottom: 10px;
  width: 100%;
  text-align: center;
  font-size: 12px;
  color: white;
}

.error {
  color: #ff8080;
  font-size: 13px;
  margin-top: -15px;
  margin-bottom: 10px;
}


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e(trans_static('ثبت نوم')); ?></title>
    <link rel="stylesheet" href="<?php echo e(url('public/../Css_filse/sign_up.css')); ?>">
    <style>
        /* General Form Styling */
form {
    max-width: 600px;
    margin: 40px auto;
    padding: 30px;
    border-radius: 15px;
    background: #f9fafb;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    direction: rtl;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

form h1 {
    text-align: center;
    color: #059669;
    font-size: 28px;
    margin-bottom: 20px;
}

/* Label styling */
label {
    font-weight: 600;
    color: #374151;
    margin-top: 10px;
    display: block;
}

/* Text and password inputs */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="date"],
select {
    width: 100%;
    padding: 12px;
    margin-top: 6px;
    margin-bottom: 14px;
    border: 1px solid #d1d5db;
    border-radius: 10px;
    background-color: #fff;
    font-size: 16px;
    transition: 0.3s border-color;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="date"]:focus,
select:focus {
    border-color: #10b981;
    outline: none;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

/* Buttons */
input[type="submit"],
input[type="reset"] {
    width: 48%;
    padding: 12px;
    font-weight: 600;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: background 0.3s ease;
    font-size: 16px;
}

#submit_b {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    margin-left: 2%;
}

#submit_b:hover {
    background: linear-gradient(135deg, #059669, #047857);
}

#reset_b {
    background: #f87171;
    color: white;
    margin-right: 2%;
}

#reset_b:hover {
    background: #ef4444;
}

/* Alert Box */
.alert {
    padding: 10px 15px;
    background-color: #fee2e2;
    color: #991b1b;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #fca5a5;
}

/* Responsive Design */
@media (max-width: 600px) {
    form {
        padding: 20px;
    }

    input[type="submit"],
    input[type="reset"] {
        width: 100%;
        margin-bottom: 10px;
    }

    .form-group.row {
        display: block;
    }

    .form-group .col-md-4,
    .form-group .col-md-6 {
        width: 100%;
    }
}

    </style>
    
</head>
<body>
<script defer>
    function checkTheForm(){
        let sd=document.getElementById("user").value;
        if(sd[0]=""){
            alert("<?php echo e(trans_static('په مهربانی سره تاسی نشی کولی چی لمړی فاصله داخله کړی')); ?>");
            return false;
        }
        else if(sd.length==""){
            alert("<?php echo e(trans_static('مهربانی وکړی نوموړی فیلډ پوره کړی')); ?>");
            return false;
        }
        else if(sd.length < 2 || sd.length > 25 ){
            alert(" 2 <charaacters > 25");
            return false;
        }

        // Check if passwords match
        let password = document.getElementById("password").value;
        let confirmPassword = document.getElementById("confirm-password").value;
        if(password !== confirmPassword) {
            alert("<?php echo e(trans_static('امنیتی کودونه سره برابر نه دي')); ?>");
            return false;
        }
        
        // Form is valid, it will be submitted
        // You can add a success message here if needed
        return true;
    }
    
    function ChekTheClearance(){
        if(confirm("<?php echo e(trans_static('آیا تاسی اطمنانی یی')); ?>")){
            return true;
        }
        else{
            return false;
        }
    }
    
    function ChangeTheColor() {
        event.target.style.borderColor = "#1a2a6c";
        event.target.style.boxShadow = "0 0 0 3px rgba(26, 42, 108, 0.2)";
        event.target.style.backgroundColor = "#fff";
    }
    
    function backToTheColor() {
        event.target.style.borderColor = "#ddd";
        event.target.style.boxShadow = "none";
        event.target.style.backgroundColor = "#f8f9fa";
    }
</script>

<form action="<?php echo e(route('register')); ?>" onsubmit="return checkTheForm()" onreset="return ChekTheClearance()" method="POST">
    <?php echo csrf_field(); ?>
    <!-- Remove this line as we're handling redirects in the backend -->
    <!-- <input type="hidden" name="redirect_to" value="<?php echo e(route('dashboard')); ?>"> -->
    <h1 dir="rtl"> <?php echo e(trans_static('ثبت نام ته ښه راغلاست!')); ?></h1>
    
    <?php if($errors->any()): ?>
        <div class="alert alert-danger">
            <ul>
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <label class="ssdss" for="user"><?php echo e(trans_static('پوره نوم')); ?></label>
    <br>
    <input dir="rtl" type="text" id="user" name="name" value="<?php echo e(old('name')); ?>"
           onfocus="ChangeTheColor()" onblur="backToTheColor()"
           placeholder="<?php echo e(trans_static('خپل نوم مو داخل کړی')); ?>" class="awz" required>
    <br>

    <label id="dds" for="date_of_birth"><?php echo e(trans_static('د زیږیدو نیټه')); ?></label>
    <br>
    <div class="form-group row">
        <label for="date_of_birth" class="col-md-4 col-form-label text-md-right"><?php echo e(trans_static('Date of Birth')); ?></label>

        <div class="col-md-6">
            <input id="date_of_birth" type="date" class="form-control <?php $__errorArgs = ['date_of_birth'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="date_of_birth" value="<?php echo e(old('date_of_birth')); ?>" required>

            <?php $__errorArgs = ['date_of_birth'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="invalid-feedback" role="alert">
                    <strong><?php echo e($message); ?></strong>
                </span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
    </div>
    <br>
    
    <label id="dsds" for="email"><?php echo e(trans_static('ایمیل')); ?></label>
    <br>
    <input dir="rtl" type="email" required id="email" name="email" value="<?php echo e(old('email')); ?>"
           onfocus="ChangeTheColor()" onblur="backToTheColor()"
           placeholder="<?php echo e(trans_static('خپل ایمیل ادرس داخل کړی')); ?>" class="awz">
    <br>

    <label class="ssdss" for="password"><?php echo e(trans_static('امنیتی کود')); ?></label>
    <br>
    <input dir="rtl" type="password" required id="password" name="password"
           onfocus="ChangeTheColor()" onblur="backToTheColor()"
           placeholder="<?php echo e(trans_static('امنیتی کود موداخل کړی')); ?>" class="awz">
    <br>

    <label for="confirm-password"><?php echo e(trans_static('تصدیق شوی کود')); ?></label>
    <br>
    <input dir="rtl" type="password" required id="confirm-password" name="password_confirmation"
           onfocus="ChangeTheColor()" onblur="backToTheColor()"
           placeholder="<?php echo e(trans_static('تصدیق شوی کود')); ?>" class="awz">
    <br>
    
    <div class="form-group row">
        <label for="role" class="col-md-4 col-form-label text-md-right"><?php echo e(trans_static('Role')); ?></label>

        <div class="col-md-6">
            <select id="role" class="form-control <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="role" required>
             
                <option value="admin"><?php echo e(trans_static('Admin')); ?></option>
                
            </select>

            <?php $__errorArgs = ['role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <span class="invalid-feedback" role="alert">
                    <strong><?php echo e($message); ?></strong>
                </span>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
    </div>
    
    <input type="submit" id="submit_b" class="btn" value="<?php echo e(trans_static('استول')); ?>">
</form>
</body>
</html>






<?php /**PATH D:\KU_Mental_Health\resources\views/auth/register.blade.php ENDPATH**/ ?>
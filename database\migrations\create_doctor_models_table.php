<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('doctor_models', function (Blueprint $table) {
            $table->id();
            $table->string('Dr_name');
            $table->string('Dr_last_name')->nullable();
            $table->string('Dr_father_name')->nullable();
            $table->string('Dr_phone')->nullable();
            $table->string('Dr_office_phone')->unique();
            $table->string('Dr_email')->nullable();
            $table->string('Dr_specialty')->nullable();
            $table->string('Dr_education')->nullable();
            $table->string('Dr_experience')->nullable();
            $table->string('Dr_address')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('doctor_models');
    }
};

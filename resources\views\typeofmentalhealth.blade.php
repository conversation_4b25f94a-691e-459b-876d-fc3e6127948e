@extends('layouts/Main')

@section('title', trans_static('🧐 د رواني ناروغيو ډولونه'))

@section('contents')
 <div id="content_sec">
  
<h1>{{ trans_static('🧐 د رواني ناروغيو ډولونه') }}</h1>

<div class="container">

<div class="section" id="depression" onclick="toggleSection('depression')">
<i class="fas fa-sad-tear icon"></i>
<h3>{{ trans_static('افسردګي (Depression)') }}</h3>
<div class="description"><p>د خپګان، بې دلیله خپه کیدل او بې اميده کيدو حالت.</p></div>
<div class="sub-description">  <img src="{{url('public/../imagese/deprassion.jpg')}}">
<h4>حل لاری:</h4>
<p>
  رواني درملنه، ټولنیزه همکاري، درمل، خوب او تغذیه منظمه ساتل، مثبتو افکارو تمرین</p>
 <h4>اړوند ډاکتر:</h4>
  <p>استاد قدرت الله نظری</p>
  <p>0700009617</p>
  <p>په کندهار پوهنتون کی د ارواپوهنی استاد</p>
</div>
</div>

<div class="section" id="anxiety" onclick="toggleSection('anxiety')">
<i class="fas fa-exclamation-circle icon"></i>
<h3>{{ trans_static('اضطراب (Anxiety Disorders)') }}</h3>
<div class="description"><p>دوامداره ويره، وارخطايي او تشويش.</p></div>
<div class="sub-description">  <img src="{{url('public/../imagese/afsardagy.jpg')}}">
  <h4>حل لاری:</h4>
  <p> تنفسي تمرینونه، مراقبه، CBT (شناختي سلوکي درملنه)، فزیکي تمرین، درمل لکه SSRIs (له ډاکټر سره مشوره).</p>
   <h4>اړوند ډاکتر:</h4>
   <p>ډاکتر ننګیالی صدیقی</p>
    <p>0706009863</p>
    <p>په کندهار پوهنتون کی دروانی کلینیک عمومی مدیر</p>
</div>
</div>

<div class="section" id="personality" onclick="toggleSection('personality')">
<i class="fas fa-user-alt icon"></i>
<h3>{{ trans_static('شخصيتي اختلالات (Personality Disorders)') }}</h3>
<div class="description"><p>ناسمه یا غیرمعمولي چلند یا اښتکه له نورو سره.</p></div>
<div class="sub-description"><img src="{{url('public/../imagese/personal.png')}}">
  <h4>حل لاری:</h4>
  <p>  اوږدمهاله رواني درملنه (لکه DBT/BPD لپاره)، ټولنیزه روزنه، د خشم کنټرول تمرین.
  </p>
   <h4>اړوند ډاکتر:</h4>
    <p>استاد قدرت الله نظری</p>
    <p>0700009617</p>
    <p>په کندهار پوهنتون کی د ارواپوهنی استاد</p>
</div>
</div>

<div class="section" id="mood" onclick="toggleSection('mood')">
<i class="fas fa-cloud-sun-rain icon"></i>
<h3>{{ trans_static('د خوښښت اختلالات (Mood Disorders)') }}</h3>
<div class="description"><p>د مزاج شديد بدلونه (لکه شوقي یا مايوسي).</p></div>
<div class="sub-description"><img src="{{url('public/../imagese/mood.png')}}">
  <h4>حل لاری:</h4>
  <p> ERP درملنه (Exposure and Response Prevention)، SSRIs، منظم مشورې، د افکارو تحلیل تمرین.</p>
   <h4>اړوند ډاکتر:</h4>
   <p>ډاکتر ننګیالی صدیقی</p>
    <p>0706009863</p>
    <p>په کندهار پوهنتون کی دروانی کلینیک عمومی مدیر</p>
  </div>
</div>

<div class="section" id="schizophrenia" onclick="toggleSection('schizophrenia')">
<i class="fas fa-brain icon"></i>
<h3>{{ trans_static('شيزوفرينيا (Schizophrenia)') }}</h3>
<div class="description"><p>وهمونه، خيالونه او واقعيت سره بې ارزښته ارتباط.</p></div>
<div class="sub-description"><img src="{{url('public/../imagese/schizoph.png')}}">
  <h4>حل لاری:</h4>
  <p>  انتي‌سایکوتیک درمل، رواني درملنه، کورنۍ ملاتړ، دوامداره طبي څارنه.
  </p>
   <h4>اړوند ډاکتر:</h4>
    <p>استاد قدرت الله نظری</p>
    <p>0700009617</p>
    <p>په کندهار پوهنتون کی د ارواپوهنی استاد</p>
  </div>
</div>

<div class="section" id="ocd" onclick="toggleSection('ocd')">
<i class="fas fa-sync icon"></i>
<h3>{{ trans_static('وسواسي اختلال (OCD)') }}</h3>
<div class="description"><p>تکراري فکرونه او جبري چاره.</p></div>
<div class="sub-description"><img src="{{url('public/../imagese/ocd.png')}}">
  <h4>حل لاری:</h4>
 <p> ERP درملنه (Exposure and Response Prevention)، SSRIs، منظم مشورې، د افکارو تحلیل تمرین.</p>
   <h4>اړوند ډاکتر:</h4>
   <p>ډاکتر ننګیالی صدیقی</p>
    <p>0706009863</p>
    <p>په کندهار پوهنتون کی دروانی کلینیک عمومی مدیر</p>
  </div>
</div>

<div class="section" id="ptsd" onclick="toggleSection('ptsd')">
<i class="fas fa-bolt icon"></i>
<h3>{{ trans_static('د صدمې وروسته اختلال (PTSD)') }}</h3>
<div class="description"><p>د سختي حادثې وروسته ويره او اضطراب.</p></div>
<div class="sub-description"><img src="{{url('public/../imagese/ptsd.png')}}">
  <h4>حل لاری:</h4>
<p>   EMDR، CBT، ملاتړ کوونکې چاپېریال، درمل (که اړتیا وي)</p>
   <h4>اړوند ډاکتر:</h4>
    <p>استاد قدرت الله نظری</p>
    <p>0700009617</p>
    <p>په کندهار پوهنتون کی د ارواپوهنی استاد</p>
  </div>
</div>

<div class="section" id="eating" onclick="toggleSection('eating')">
<i class="fas fa-hamburger icon"></i>
<h3>{{ trans_static('د خوراک اختلالات (Eating Disorders)') }}</h3>
<div class="description"><p>د خوراک سره ناسمه اښته لیکه دېر کم کول يا څرنډ زياتول.</p></div>
<div class="sub-description"><img src="{{url('public/../imagese/eating.png')}}">
  <h4>حل لاری:</h4>
 <p>   تغذیه‌يي مشورې، رواني درملنه، کورنۍ درملنه، ډاکټري څارنه</p>
   <h4>اړوند ډاکتر:</h4>
   <p>ډاکتر ننګیالی صدیقی</p>
   <p>0706009863</p>
   <p>په کندهار پوهنتون کی دروانی کلینیک عمومی مدیر</p>
  </div>
</div>

<div class="section" id="sleep" onclick="toggleSection('sleep')">
<i class="fas fa-bed icon"></i>
<h3>{{ trans_static('د خوب اختلالات (Sleep Disorders)') }}</h3>
<div class="description"><p>بې خوبي، زيات خوب يا بد خوبونه.</p></div>
<div class="sub-description"><img src="{{url('public/../imagese/sleepdepresion.png')}}">
  <h4>حل لاری:</h4>
  <p> خوب ته مناسب عادتونه، د خوب وخت منظم ساتل، سکرین کمول، درمل (که اړتیا وي)</p>
   <h4>اړوند ډاکتر:</h4>
    <p>استاد قدرت الله نظری</p>
    <p>0700009617</p>
    <p>په کندهار پوهنتون کی د ارواپوهنی استاد</p>
  </div>
</div>

<div class="section" id="dementia" onclick="toggleSection('dementia')">
<i class="fas fa-user-clock icon"></i>
<h3>{{ trans_static('د حافظې اختلالات (Dementia)') }}</h3>
<div class="description"><p>د يادونو او فکر کولو کمزوري.</p></div>
<div class="sub-description"><img src="{{url('public/../imagese/domenit.png')}}">
  <h4>حل لاری:</h4>
<p> معاینه او درملنه د مغزي ستونزو لپاره، ذهني تمرینونه، حافظه هڅوونکې فعالیتونه</p>
   <h4>اړوند ډاکتر:</h4>
   <p>ډاکتر ننګیالی صدیقی</p>
   <p>0706009863</p>
   <p>په کندهار پوهنتون کی دروانی کلینیک عمومی مدیر</p>
  </div>
</div>

</div>
</div>

<div id="kjkj"></div>
  <!-- ====contact_us section=============== -->


  <div id="contact_us">
    <h1>زموږ سره په اړیکه کی شی</h1>
    <p>د لاندی آدرسونو په واسطه کولی شی چی زموږ سره په اړیکه کی شی او هم مو خپل نظر راسره شریک کړۍ </p>
    <div id="conteaner">
      <form action="">
     <div id="left_section">
       <h1>خپل نظر مو راسره شریک کړی</h1>
       <input placeholder="دکارکوونکی نوم" type="text">
       <input placeholder="موبایل نمبر" type="phone">
       <textarea name="" id="" cols="30" rows="10" placeholder="خپل نظر مو دلته ولیکی"></textarea>
      <button>لیږل</button>
    </div>
  </form>
     <div id="right_section" >
     <div class="location_section">
      <p> د عیدګاه څنګ ته- نهمه ناحیه –کندهار - افغانستان</p>
      <img src="../imagese/location.PNG" alt="">
       </div>
           <!-- ===== -->
           <div class="location_section">
            <p>دکندهار پوهنتون روانی کلینیک</p>
            <a href="https://www.facebook.com/share/v/191MszWPsK/">
            <img src="../imagese/facebook.png" alt="">
          </a>
             </div>
     
        
          <!-- ===== -->
       <div class="location_section">
        <p>ډاکټرروغتیایی اومعلوماتی خپرونه</p>
        <a href="https://youtu.be/z2PqTt7xvM4?si=D0xvy8WZr8eFY-nS">
        <img src="../imagese/yotub.png" alt="">
      </a>
         </div>
           <!-- ===== -->
       <div class="location_section">
        <p>ارواپوهنه(psychology)</p>
        <a href="https://t.me/Psychology786">
        <img src="../imagese/telegram.png" alt="">
      </a>
         </div>
      
           <!-- ===== -->
       <div class="location_section">
        <p>۰۷۰۶۰۰۹۸۶۳</p>
        <img src="../imagese/whatsapp.png" alt="">
         </div>
      </div>


    
    
  </div>
<script>
  function toggleSection(id) {
    const section = document.getElementById(id);

    if (section.classList.contains('active')) {
      // If already active, close it
      section.classList.remove('active');
      section.querySelector('.sub-description').style.display = 'none';
    } else {
      // Close all other sections
      document.querySelectorAll('.section.active').forEach(activeSection => {
        activeSection.classList.remove('active');
        activeSection.querySelector('.sub-description').style.display = 'none';
      });

      // Open this section
      section.classList.add('active');
      section.querySelector('.sub-description').style.display = 'block';

      // Scroll to the section
      setTimeout(() => {
        section.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }, 100);
    }
  }

  // Initialize - make sure all sections are closed on page load
  document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('.section').forEach(section => {
      section.classList.remove('active');
      section.querySelector('.sub-description').style.display = 'none';
    });
  });
</script>

<style>
  .mental-health-types-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    direction: rtl;
  }

  h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
  }

  .container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }

  .section {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    position: relative;
  }

  .section:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  .icon {
    font-size: 2rem;
    margin-bottom: 15px;
    color: #3498db;
  }

  h3 {
    margin-bottom: 10px;
    color: #2c3e50;
  }

  .description p {
    color: #7f8c8d;
    margin-bottom: 15px;
  }

  .sub-description {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
    animation: fadeIn 0.5s ease-in-out;
  }

  .sub-description img {
    width: 100%;
    border-radius: 8px;
    margin-bottom: 15px;
    height: 200px;
    object-fit: cover;
  }

  .sub-description p {
    line-height: 1.6;
    color: #34495e;
  }

  .section.active {
    height: auto;
    min-height: 400px;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
</style>
@endsection


<?php

namespace App\Http\Controllers;

use Exception;
use App\Models\NewsModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;

class NewsController extends Controller
{
    /**
     * Display a listing of the news with filters for dashboard.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $query = NewsModel::query();

        // Apply search filter if provided (title search)
        if ($request->has('search') && !empty($request->search)) {
            $query->where('News_Title', 'like', '%' . $request->search . '%');
        }

        // Apply date filter if provided (specific date)
        if ($request->has('date') && !empty($request->date)) {
            $query->whereDate('created_at', $request->date);
        }

        // Apply year filter if provided
        if ($request->has('year') && !empty($request->year)) {
            $query->whereYear('created_at', $request->year);
        }

        // Apply month filter if provided
        if ($request->has('month') && !empty($request->month)) {
            $query->whereMonth('created_at', $request->month);
        }

        // Get news with latest first
        $news = $query->orderBy('created_at', 'desc')->paginate(10);

        // Preserve the query string for pagination
        $news->appends($request->all());

        // Get available years from the database
        $years = NewsModel::selectRaw('YEAR(created_at) as year')
                        ->distinct()
                        ->orderBy('year', 'desc')
                        ->pluck('year')
                        ->toArray();

        // Month names for display
        $monthNames = [
            '1' => translateText('جنوري'),
            '2' => translateText('فبروري'),
            '3' => translateText('مارچ'),
            '4' => translateText('اپریل'),
            '5' => translateText('می'),
            '6' => translateText('جون'),
            '7' => translateText('جولای'),
            '8' => translateText('اګست'),
            '9' => translateText('سپتمبر'),
            '10' => translateText('اکتوبر'),
            '11' => translateText('نومبر'),
            '12' => translateText('دسمبر')
        ];

        // Show filter card if filters are applied
        $showFilter = $request->has('search') || $request->has('year') || $request->has('month');

        return view('dashboardofproject.newscopy', compact('news', 'years', 'monthNames', 'showFilter'));
    }

    /**
     * Display the news page for public site.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function News(Request $request)
    {
        $query = NewsModel::query();

        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $query->where('News_Title', 'like', '%' . $request->search . '%');
        }

        // Get news with latest first
        $news = $query->orderBy('created_at', 'desc')->paginate(10);

        // Preserve the query string for pagination
        $news->appends($request->all());

        return view('news', compact('news'));
    }

    /**
     * Store a newly created news in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validate the request with custom messages
        $request->validate([
            'title' => 'required|string|max:255',
            'contents' => 'required|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ], [
            'title.required' => translateText('سرلیک اړین دی'),
            'title.max' => translateText('سرلیک باید له 255 توریو څخه لږ وي'),
            'contents.required' => translateText('منځپانګه اړینه ده'),
            'image.required' => translateText('انځور اړین دی'),
            'image.image' => translateText('دا فایل باید انځور وي'),
            'image.mimes' => translateText('انځور باید د JPEG، PNG، JPG یا GIF په بڼه وي'),
            'image.max' => translateText('انځور باید له 2MB څخه لوی نه وي'),
        ]);

        try {
            $imageName = time() . '.' . $request->image->extension();
            $request->image->move(public_path('imagese'), $imageName);

            // ډاډ ترلاسه کړئ چې ټول اړین ستونونه شامل دي
            $news = new NewsModel();
            $news->News_Title = $request->title;
            $news->News_Discription = $request->contents;
            $news->New_Images = $imageName;
            $news->user_id = 2024; // پیش فرض کارن ID
            $news->save();

            return redirect()->route('news.index')
                ->with('success', translateText('خبر په بریالیتوب سره خپور شو!'));
        } catch (Exception $e) {
            return redirect()->back()
                ->with('error', translateText('تیروتنه: ') . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show the form for editing the specified news.
     *
     * @param  int  $id
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function edit($id)
    {
        try {
            // Find the news to edit using News_Id
            $editNews = NewsModel::where('News_Id', $id)->firstOrFail();

            // Get all news for the table
            $query = NewsModel::query();
            $news = $query->orderBy('created_at', 'desc')->paginate(10);

            // Get available years from the database
            $years = NewsModel::selectRaw('YEAR(created_at) as year')
                            ->distinct()
                            ->orderBy('year', 'desc')
                            ->pluck('year')
                            ->toArray();

            // Get available months (all months)
            $monthNames = [
                '1' => translateText('جنوري'),
                '2' => translateText('فبروري'),
                '3' => translateText('مارچ'),
                '4' => translateText('اپریل'),
                '5' => translateText('می'),
                '6' => translateText('جون'),
                '7' => translateText('جولای'),
                '8' => translateText('اګست'),
                '9' => translateText('سپتمبر'),
                '10' => translateText('اکتوبر'),
                '11' => translateText('نومبر'),
                '12' => translateText('دسمبر')
            ];

            return view('dashboardofproject.newscopy', compact('editNews', 'news', 'years', 'monthNames'));
        } catch (Exception $e) {
            return redirect()->route('news.index')->with('error', translateText('خبر ونه موندل شو: ') . $e->getMessage());
        }
    }
    /**
     * Update the specified news in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        // Validate the request with custom messages
        $request->validate([
            'title' => 'required|string|max:255',
            'contents' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ], [
            'title.required' => translateText('سرلیک اړین دی'),
            'title.max' => translateText('سرلیک باید له 255 توریو څخه لږ وي'),
            'contents.required' => translateText('منځپانګه اړینه ده'),
            'image.image' => translateText('دا فایل باید انځور وي'),
            'image.mimes' => translateText('انځور باید د JPEG، PNG، JPG یا GIF په بڼه وي'),
            'image.max' => translateText('انځور باید له 2MB څخه لوی نه وي'),
        ]);

        try {
            // Find the news to update using News_Id
            $news = NewsModel::where('News_Id', $id)->firstOrFail();

            // Update the news
            $news->News_Title = $request->title;
            $news->News_Discription = $request->contents;

            // Handle image upload if a new image is provided
            if ($request->hasFile('image')) {
                // Delete old image if it exists
                if ($news->New_Images && File::exists(public_path('imagese/' . $news->New_Images))) {
                    File::delete(public_path('imagese/' . $news->New_Images));
                }

                $imageName = time() . '.' . $request->image->extension();
                $request->image->move(public_path('imagese'), $imageName);
                $news->New_Images = $imageName;
            }

            // Save the news
            $news->save();

            return redirect()->route('news.index')->with('success', translateText('خبر په بریالیتوب سره تازه شو!'));
        } catch (Exception $e) {
            return redirect()->back()->with('error', translateText('تیروتنه: ') . $e->getMessage())->withInput();
        }
    }

    /**
     * Remove the specified news from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            $news = NewsModel::findOrFail($id);

            // Delete the image file if it exists
            if ($news->New_Images && File::exists(public_path('imagese/' . $news->New_Images))) {
                File::delete(public_path('imagese/' . $news->New_Images));
            }

            $news->delete();

            return redirect()->route('news.index')->with('success', translateText('خبر په بریالیتوب سره لرې شو!'));
        } catch (Exception $e) {
            return redirect()->back()->with('error', translateText('تیروتنه: ') . $e->getMessage());
        }
    }


}





















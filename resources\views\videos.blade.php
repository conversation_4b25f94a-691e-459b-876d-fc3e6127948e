@extends('layouts/Main')

@section('title', trans_static('ویدیوګانې'))

@section('contents')
<div id="videos-page">
    <div class="container">
        <h1 class="page-title">{{ trans_static('ویدیوګانې') }}</h1>

        <div class="filter-section">
            <form action="{{ route('videos.public') }}" method="GET">
                <div class="filter-buttons">
                    <button type="submit" name="category" value="" class="{{ !request('category') ? 'active' : '' }}">
                        {{ trans_static('ټول') }}
                    </button>
                    <button type="submit" name="category" value="doctor" class="{{ request('category') == 'doctor' ? 'active' : '' }}">
                        {{ trans_static('ډاکتران') }}
                    </button>
                    <button type="submit" name="category" value="psychologist" class="{{ request('category') == 'psychologist' ? 'active' : '' }}">
                        {{ trans_static('روانپوهان') }}
                    </button>
                    <button type="submit" name="category" value="teacher" class="{{ request('category') == 'teacher' ? 'active' : '' }}">
                        {{ trans_static('استادان') }}
                    </button>
                </div>
            </form>
        </div>

        <div class="videos-grid">
            @forelse($videos as $video)
                <div class="video-card">
                    <div class="video-thumbnail" data-video-url="{{ $video->video_url }}" onclick="playVideo(this)">
                        @if($video->thumbnail)
                            <img src="{{ asset($video->thumbnail) }}" alt="{{ $video->title }}">
                        @else
                            <img src="{{ asset('images/video-placeholder.jpg') }}" alt="{{ $video->title }}">
                        @endif
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="video-info">
                        <h3 class="video-title">{{ $video->title }}</h3>
                        <p class="video-category">
                            @if($video->category == 'doctor')
                                <span class="badge badge-primary">{{ trans_static('ډاکټر') }}</span>
                            @elseif($video->category == 'psychologist')
                                <span class="badge badge-success">{{ trans_static('روانپوه') }}</span>
                            @elseif($video->category == 'teacher')
                                <span class="badge badge-info">{{ trans_static('استاد') }}</span>
                            @endif
                        </p>
                        <p class="video-date">{{ $video->created_at->format('Y-m-d') }}</p>
                        <a href="{{ route('videos.show.public', $video->Video_Id) }}" class="view-details">
                            {{ translateText('نور معلومات') }}
                        </a>
                    </div>
                </div>
            @empty
                <div class="no-videos">
                    <p>{{ translateText('هیڅ ویډیو ونه موندل شوه') }}</p>
                </div>
            @endforelse
        </div>

        <div class="pagination-container">
            {{ $videos->links() }}
        </div>
    </div>

    <!-- Video Modal -->
    <div id="videoModal" class="modal">
        <div class="modal-content">
            <span class="close-button" onclick="closeVideoModal()">&times;</span>
            <div class="video-container">
                <iframe id="videoFrame" src="" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function getYoutubeEmbedUrl(url) {
        // Convert YouTube URL to embed URL
        let video
</augment_code_snippet>



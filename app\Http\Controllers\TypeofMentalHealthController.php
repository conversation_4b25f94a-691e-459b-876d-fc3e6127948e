<?php

namespace App\Http\Controllers;

use App\Models\PatientModel;
use Illuminate\Http\Request;
use App\Models\PatientAddModel;
use App\Models\QuestionerModel;
use App\Models\User;


class TypeofMentalHealthController extends Controller
{
    public function TypeofMentlhealth(){

        return view('typeofmentalhealth');
    }


       public function submitForm(Request $request) {
        // Validation
        $data = $request->validate([
            'name' => 'required|string',
            'phone' => 'required|string',
            'age' => 'required|integer',
            'gender' => 'required|in:نارینه,ښځینه',
            'province' => 'required|string',
            'district' => 'required|string',
            'village' => 'required|string',
            'dr_id' => 'required|exists:doctor_models,Dr_id'
        ]);
        
        // Create patient
        $patient = PatientModel::create([
            'Patiet_Name' => $data['name'],
            'Patient_phone' => $data['phone'],
            'Patient_Age' => $data['age'],
            'Patient_Gender' => $data['gender'],
            'Patient_email' => $data['email'] ?? '<EMAIL>', // ډیفالټ ایمیل ورکول
            'Dr_id' => $data['dr_id']
        ]);

        // Notification functionality removed
        
        // Create address
        PatientAddModel::create([
            'P_Province' => $data['province'],
            'P_Distract' => $data['district'],
            'P_Village' => $data['village'],
            'Patient_Id' => $patient->Patient_id
        ]);
        
        // Save questions
        $questions = [
            "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟",
            // ... all 10 questions ...
        ];
        
        $options = [
            'A' => "هیڅ نه",
            'B' => "لږه اندازه",
            'C' => "لږه ډېره اندازه",
            'D' => "ډېره اندازه"
        ];
        
        for ($i = 0; $i < 10; $i++) {
            QuestionerModel::create([
                'Q_Discription' => $questions[$i],
                'Question_No' => $i + 1,
                'A' => $options['A'],
                'B' => $options['B'],
                'C' => $options['C'],
                'D' => $options['D'],
                'answer' => $request->input("q{$i}"),
                'Patient_Id' => $patient->Patient_id
            ]);
        }
        
        return response()->json(['success' => true]);
    }
}



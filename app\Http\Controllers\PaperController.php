<?php

namespace App\Http\Controllers;

use App\Models\ArticalModel;
use Illuminate\Http\Request;
use app\Http\Controllers\BookController;

class PaperController extends Controller
{
      public function index()
      {
          try {
              // Get papers based on their type
              $papers = ArticalModel::where('A_Type', 'مقالي')
                                  ->orWhere('A_Type', 'paper')
                                  ->orWhere('A_Type', 'مقالات')
                                  ->get();

              return view('dashboardofproject.papercopy', compact('papers'));
          } catch (\Exception $e) {
              return redirect()->back()->with('error', translateText('تېروتنه: ') . $e->getMessage());
          }
      }

      public function edit($id)
      {
          try {
              // Find the paper to edit
              $editPaper = ArticalModel::findOrFail($id);

              // Get all papers for the table
              $papers = ArticalModel::where('A_Type', 'مقالي')
                                  ->orWhere('A_Type', 'paper')
                                  ->orWhere('A_Type', 'مقالات')
                                  ->get();

              return view('dashboardofproject.papercopy', compact('editPaper', 'papers'));
          } catch (\Exception $e) {
              return redirect()->route('papers.index')
                  ->with('error', translateText('مقاله ونه موندل شوه: ') . $e->getMessage());
          }
      }

      public function update(Request $request, $id)
      {
          $request->validate([
              'title' => 'required|string|max:255',
              'author' => 'required|string|max:255',
              'publishdate' => 'required|date',
              'language' => 'required|string|max:255',
              'catagory' => 'required|string|max:255',
              'description' => 'nullable|string',
              'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
              'author_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
              'file' => 'nullable|mimes:pdf,doc,docx|max:10240',
          ]);

          try {
              $paper = ArticalModel::findOrFail($id);
              $paper->A_Title = $request->title;
              $paper->A_Author = $request->author;
              $paper->A_Publication_date = $request->publishdate;
              $paper->A_Language = $request->language;
              $paper->A_Type = 'مقالي'; // Always set type to مقالي
              $paper->A_catagory = $request->catagory;
              $paper->A_Description = $request->description;

              // Handle paper cover image upload if a new image is provided
              if ($request->hasFile('image')) {
                  // Delete old image if it exists
                  if ($paper->A_Image && file_exists(public_path('imagese/' . $paper->A_Image))) {
                      unlink(public_path('imagese/' . $paper->A_Image));
                  }

                  $imageName = time() . '_image.' . $request->image->extension();
                  $request->image->move(public_path('imagese'), $imageName);
                  $paper->A_Image = $imageName;
              }

              // Handle author image upload if a new author image is provided
              if ($request->hasFile('author_image')) {
                  // Delete old author image if it exists
                  if ($paper->A_Author_Image && file_exists(public_path('imagese/' . $paper->A_Author_Image))) {
                      unlink(public_path('imagese/' . $paper->A_Author_Image));
                  }

                  $authorImageName = time() . '_author.' . $request->author_image->extension();
                  $request->author_image->move(public_path('imagese'), $authorImageName);
                  $paper->A_Author_Image = $authorImageName;
              }

              // Handle file upload if a new file is provided
              if ($request->hasFile('file')) {
                  // Delete old file if it exists
                  if ($paper->A_File && file_exists(public_path('imagese/' . $paper->A_File))) {
                      unlink(public_path('imagese/' . $paper->A_File));
                  }

                  $fileName = time() . '_file.' . $request->file->extension();
                  $request->file->move(public_path('imagese'), $fileName);
                  $paper->A_File = $fileName;
              }

              $paper->save();

              return redirect()->route('papers.index')
                  ->with('success', translateText('مقاله په بریالیتوب سره تازه شوه!'));
          } catch (\Exception $e) {
              return redirect()->back()
                  ->with('error', translateText('تېروتنه: ') . $e->getMessage())
                  ->withInput();
          }
      }

      public function destroy($id)
      {
          try {
              $paper = ArticalModel::findOrFail($id);

              // Delete image file if it exists
              if ($paper->A_Image && file_exists(public_path('imagese/' . $paper->A_Image))) {
                  unlink(public_path('imagese/' . $paper->A_Image));
              }

              // Delete document file if it exists
              if ($paper->A_File && file_exists(public_path('imagese/' . $paper->A_File))) {
                  unlink(public_path('imagese/' . $paper->A_File));
              }

              $paper->delete();

              return redirect()->route('papers.index')
                  ->with('success', translateText('مقاله په بریالیتوب سره لرې شوه!'));
          } catch (\Exception $e) {
              return redirect()->route('papers.index')
                  ->with('error', translateText('تېروتنه: ') . $e->getMessage());
          }
      }

      /**
       * Display a listing of papers for the public view.
       *
       * @return \Illuminate\Contracts\View\View
       */
      public function Papers()
      {
          try {
              // Get papers based on their type
              $papers = ArticalModel::where('A_Type', 'مقالي')
                                  ->orWhere('A_Type', 'paper')
                                  ->orWhere('A_Type', 'مقالات')
                                  ->latest()
                                  ->paginate(10);

              return view('paper', compact('papers'));
          } catch (\Exception $e) {
              return redirect()->back()->with('error', translateText('تېروتنه: ') . $e->getMessage());
          }
      }

      /**
       * Search for papers.
       *
       * @param  \Illuminate\Http\Request  $request
       * @return \Illuminate\View\View
       */
      public function searchPaper(Request $request)
      {
          $search = $request->input('search');

          // Search in paper title, author, category, and description
          $papers = ArticalModel::where(function($query) {
                  $query->where('A_Type', 'مقالي')
                        ->orWhere('A_Type', 'paper')
                        ->orWhere('A_Type', 'مقالات');
              })
              ->where(function($query) use ($search) {
                  $query->where('A_Title', 'LIKE', "%{$search}%")
                        ->orWhere('A_Author', 'LIKE', "%{$search}%")
                        ->orWhere('A_catagory', 'LIKE', "%{$search}%")
                        ->orWhere('A_Description', 'LIKE', "%{$search}%");
              })
              ->latest()
              ->paginate(10);

          // Preserve search parameter in pagination links
          $papers->appends(['search' => $search]);

          return view('paper', compact('papers'));
      }

      /**
       * Store a newly created paper in storage.
       *
       * @param  \Illuminate\Http\Request  $request
       * @return \Illuminate\Http\RedirectResponse
       */
      public function store(Request $request)
      {
          $request->validate([
              'title' => 'required|string|max:255',
              'author' => 'required|string|max:255',
              'publishdate' => 'required|date',
              'language' => 'required|string|max:255',
              'catagory' => 'required|string|max:255',
              'description' => 'nullable|string',
              'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
              'author_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
              'file' => 'nullable|mimes:pdf,doc,docx|max:10240',
          ]);

          try {
              $paper = new ArticalModel();
              $paper->A_Title = $request->title;
              $paper->A_Author = $request->author;
              $paper->A_Publication_date = $request->publishdate;
              $paper->A_Language = $request->language;
              $paper->A_Type = 'مقالي'; // Always set type to مقالي
              $paper->A_catagory = $request->catagory;
              $paper->A_Description = $request->description;
              $paper->user_id = 2024;

              // Handle paper cover image upload
              if ($request->hasFile('image')) {
                  $imageName = time() . '_image.' . $request->image->extension();
                  $request->image->move(public_path('imagese'), $imageName);
                  $paper->A_Image = $imageName;
              } else {
                  $paper->A_Image = 'default.jpg';
              }

              // Handle author image upload
              if ($request->hasFile('author_image')) {
                  $authorImageName = time() . '_author.' . $request->author_image->extension();
                  $request->author_image->move(public_path('imagese'), $authorImageName);
                  $paper->A_Author_Image = $authorImageName;
              } else {
                  $paper->A_Author_Image = 'author-default.jpg';
              }

              // Handle file upload
              if ($request->hasFile('file')) {
                  $fileName = time() . '_file.' . $request->file->extension();
                  $request->file->move(public_path('imagese'), $fileName);
                  $paper->A_File = $fileName;
              } else {
                  $paper->A_File = 'default.pdf';
              }

              $paper->save();

              return redirect()->route('papers.index')
                  ->with('success', translateText('مقاله په بریالیتوب سره ثبت شوه!'));
          } catch (\Exception $e) {
              return redirect()->back()
                  ->with('error', translateText('تېروتنه: ') . $e->getMessage())
                  ->withInput();
          }
      }
}







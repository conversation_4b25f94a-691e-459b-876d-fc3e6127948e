<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ArticalModel;

class ArticleController extends Controller
{
    public function show($id)
    {
        $article = ArticalModel::findOrFail($id);
        return view('article.show', compact('article'));
    }


     public function Book()
    {
        // Get all books
        $books = ArticalModel::where('A_Type', 'کتاب')->latest()->paginate(10);
        return view('bookpage', compact('books'));
    }

}


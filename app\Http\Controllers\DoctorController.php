<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DoctorModel;
use App\Models\DoctorAddModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;


class DoctorController extends Controller
{
    /**
     * Display a listing of the doctors.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Check if the request is coming from the dashboard
        if (request()->is('doctorcopy') || request()->is('doctord')) {
            // Get all doctors with their addresses for dashboard
            $doctors = DoctorModel::with('address')->latest('Dr_Id')->paginate(10);

            // Get filter data for dropdowns
            $years = DoctorModel::selectRaw('YEAR(created_at) as year')
                ->distinct()
                ->orderBy('year', 'desc')
                ->pluck('year')
                ->filter();

            return view('dashboardofproject.doctorcopy', compact('doctors', 'years'));
        } else {
            // Get all doctors with their addresses for public view
            $doctors = DoctorModel::with('address')->latest('Dr_Id')->paginate(10);
            return view('doctor', compact('doctors'));
        }
    }

    /**
     * Filter doctors based on criteria.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function filter(Request $request)
    {
        try {
            // Start with base query
            $query = DoctorModel::with('address');

            // Apply year filter
            if ($request->filled('year')) {
                $query->whereYear('created_at', $request->year);
            }

            // Apply search filter (name or phone)
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('Dr_Name', 'like', '%' . $search . '%')
                      ->orWhere('Dr_office_phone', 'like', '%' . $search . '%')
                      ->orWhere('Dr_Personal_phone', 'like', '%' . $search . '%');
                });
            }

            // Get filtered doctors
            $doctors = $query->latest('Dr_Id')->paginate(10);

            // Get filter data for dropdowns
            $years = DoctorModel::selectRaw('YEAR(created_at) as year')
                ->distinct()
                ->orderBy('year', 'desc')
                ->pluck('year')
                ->filter();

            // Flash success message
            session()->flash('success', translateText('فلټر په بریالیتوب سره تطبیق شو!'));

            return view('dashboardofproject.doctorcopy', compact('doctors', 'years'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', translateText('تېروتنه: ') . $e->getMessage());
        }
    }

    /**
     * Show the form for creating a new doctor.
     */
    public function create()
    {
        return view('dashboardofproject.doctor_create');
    }

    /**
     * Show the form for editing the specified doctor.
     *
    * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $doctor = DoctorModel::with('address')->findOrFail($id);
        return view('dashboardofproject.doctorcopy', compact('doctor'));
    }

    /**
     * Display the specified doctor.
     */
    public function show($id)
    {
        $doctor = DoctorModel::with('address')->findOrFail($id);

        return view('dashboardofproject.doctor_show', compact('doctor'));
    }

    /**
     * Store a newly created doctor in storage.
     */
    public function store(Request $request)
    {
        // Validate the request data with custom messages
        $request->validate([
            'dr_name' => 'required|string|max:255',
            'dr_specialty' => 'nullable|string|max:255',
            'dr_description' => 'nullable|string|max:1000',
            'dr_facebook' => 'nullable|url|max:255',
            'dr_youtube' => 'nullable|url|max:255',
            'dr_telegram' => 'nullable|string|max:255',
            'dr_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:10248',
        ], [
            'dr_name.required' => translateText('د ډاکټر نوم اړین دی'),
            'dr_name.max' => translateText('د ډاکټر نوم باید له 255 توریو څخه لږ وي'),
            'dr_specialty.max' => translateText('تخصص باید له 255 توریو څخه لږ وي'),
            'dr_description.max' => translateText('توضیحات باید له 1000 توریو څخه لږ وي'),
            'dr_facebook.url' => translateText('د فیسبوک لینک سمه نه ده'),
            'dr_youtube.url' => translateText('د یوټیوب لینک سمه نه ده'),
            'dr_image.image' => translateText('دا فایل باید انځور وي'),
            'dr_image.mimes' => translateText('انځور باید د JPEG، PNG، JPG یا GIF په بڼه وي'),
            'dr_image.max' => translateText('انځور باید له 10MB څخه لوی نه وي'),
        ]);

        try {
            // Create a new doctor
            $doctor = new DoctorModel();
            $doctor->Dr_Name = $request->dr_name;
            $doctor->Dr_office_phone = null; // Default value
            $doctor->Dr_Personal_phone = null; // Default value
            $doctor->Dr_specialty = $request->dr_specialty;
            $doctor->Dr_description = $request->dr_description;
            $doctor->Dr_facebook = $request->dr_facebook;
            $doctor->Dr_youtube = $request->dr_youtube;
            $doctor->Dr_telegram = $request->dr_telegram;
            $doctor->user_id = Auth::id() ?? 2024; // Use authenticated user ID or default to 2024

            // Handle image upload
            if ($request->hasFile('dr_image')) {
                $image = $request->file('dr_image');
                $imageName = time() . '.' . $image->extension();
                $image->move(public_path('imagese'), $imageName);
                $doctor->Dr_image = 'imagese/' . $imageName;
            }

            $doctor->save();

            // Create doctor address with default values
            $address = new DoctorAddModel();
            $address->Dr_Id = $doctor->Dr_Id;
            $address->Dr_Country = 'افغانستان'; // Default country
            $address->Dr_Province = 'کابل'; // Default province
            $address->Dr_Distract = 'کابل ښار'; // Default district
            $address->Dr_Village = 'مرکز'; // Default village
            $address->save();

            // Redirect back to the same page instead of doctord.index
            return redirect()->back()->with('success', translateText('ډاکټر په بریالیتوب سره ثبت شو!'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', translateText('تیروتنه: ') . $e->getMessage())->withInput();
        }
    }

    /**
     * Update the specified doctor in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        // Validate the request data
        $request->validate([
            'dr_name' => 'required|string|max:255',
            'dr_specialty' => 'nullable|string|max:255',
            'dr_description' => 'nullable|string|max:1000',
            'dr_facebook' => 'nullable|url|max:255',
            'dr_youtube' => 'nullable|url|max:255',
            'dr_telegram' => 'nullable|string|max:255',
            'dr_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:10248',
        ], [
            'dr_name.required' => translateText('د ډاکټر نوم اړین دی'),
            'dr_name.max' => translateText('د ډاکټر نوم باید له 255 توریو څخه لږ وي'),
            'dr_specialty.max' => translateText('تخصص باید له 255 توریو څخه لږ وي'),
            'dr_description.max' => translateText('توضیحات باید له 1000 توریو څخه لږ وي'),
            'dr_facebook.url' => translateText('د فیسبوک لینک سمه نه ده'),
            'dr_youtube.url' => translateText('د یوټیوب لینک سمه نه ده'),
            'dr_image.image' => translateText('دا فایل باید انځور وي'),
            'dr_image.mimes' => translateText('انځور باید د JPEG، PNG، JPG یا GIF په بڼه وي'),
            'dr_image.max' => translateText('انځور باید له 10MB څخه لوی نه وي'),
        ]);

        try {
            // Find the doctor
            $doctor = DoctorModel::findOrFail($id);
            
            // Update doctor details
            $doctor->Dr_Name = $request->dr_name;
            $doctor->Dr_specialty = $request->dr_specialty;
            $doctor->Dr_description = $request->dr_description;
            $doctor->Dr_facebook = $request->dr_facebook;
            $doctor->Dr_youtube = $request->dr_youtube;
            $doctor->Dr_telegram = $request->dr_telegram;
            
            // Handle image upload if provided
            if ($request->hasFile('dr_image')) {
                // Delete old image if it exists
                if ($doctor->Dr_image && file_exists(public_path($doctor->Dr_image))) {
                    unlink(public_path($doctor->Dr_image));
                }
                
                // Upload new image
                $image = $request->file('dr_image');
                $imageName = time() . '.' . $image->extension();
                $image->move(public_path('imagese'), $imageName);
                $doctor->Dr_image = 'imagese/' . $imageName;
            }
            
            // Save doctor changes
            $doctor->save();
            
            return redirect()->route('doctord.index')->with('success', translateText('ډاکټر په بریالیتوب سره تازه شو!'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', translateText('تیروتنه: ') . $e->getMessage())->withInput();
        }
    }

    /**
     * Remove the specified doctor from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            $doctor = DoctorModel::findOrFail($id);

            // Delete doctor image if it exists
            if ($doctor->Dr_image && Storage::disk('public')->exists($doctor->Dr_image)) {
                Storage::disk('public')->delete($doctor->Dr_image);
            }

            // Delete doctor (address will be deleted automatically due to cascade delete)
            $doctor->delete();

            // Redirect to the doctor dashboard page
            return redirect()->route('doctord.index')->with('success', 'ډاکټر په بریالیتوب سره لرې شو!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'د ډاکټر په لرې کولو کې ستونزه: ' . $e->getMessage());
        }
    }

    /**
     * Search for doctors.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function search(Request $request)
    {
        $search = $request->input('search');

        // Search in doctor name, phone numbers, and address
        $doctors = DoctorModel::with('address')
            ->where('Dr_Name', 'LIKE', "%{$search}%")
            ->orWhere('Dr_Personal_phone', 'LIKE', "%{$search}%")
            ->orWhere('Dr_office_phone', 'LIKE', "%{$search}%")
            ->orWhereHas('address', function($query) use ($search) {
                $query->where('Dr_Province', 'LIKE', "%{$search}%")
                      ->orWhere('Dr_Country', 'LIKE', "%{$search}%")
                      ->orWhere('Dr_Distract', 'LIKE', "%{$search}%")
                      ->orWhere('Dr_Village', 'LIKE', "%{$search}%");
            })
            ->latest('Dr_Id')
            ->paginate(10);

        // Append search query to pagination links
        $doctors->appends(['search' => $search]);

        return view('doctor', compact('doctors'));
    }

    /**
     * Display a listing of the doctors for public view.
     *
     * @return \Illuminate\View\View
     */
    public function publicIndex()
    {
        // Get all doctors with their addresses for public view
        $doctors = DoctorModel::with('address')->latest('Dr_Id')->paginate(10);

        return view('doctor', compact('doctors'));
    }
}











































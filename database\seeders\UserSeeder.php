<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if admin user already exists to avoid duplicates
        if (!User::where('email', env('ADMIN_EMAIL', 'mnazi<PERSON><EMAIL>'))->exists()) {
            User::create([
                'user_id' => 2024,
                'name' => env('ADMIN_NAME', 'Mohammad Nazir'),
                'email' => env('ADMIN_EMAIL', 'mna<PERSON><PERSON><EMAIL>'),
                'password' => Hash::make(env('ADMIN_PASSWORD', 'Reyan5050khan')),
                'date_of_birth' => env('ADMIN_DOB', '1990-01-01'),
                'email_verified_at' => now(),
                'role' => 'admin', // Role is now always included
            ]);
        }
    }
}






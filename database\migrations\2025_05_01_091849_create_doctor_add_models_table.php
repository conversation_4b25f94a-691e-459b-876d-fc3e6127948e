<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('doctor_add_models', function (Blueprint $table) {
            $table->bigIncrements('Dr_Add_Id'); // اصلي ID
            $table->unsignedBigInteger('Dr_Id'); // د ډاکټر ID د فارن کي په توګه
            $table->string('Dr_Country');
            $table->string('Dr_Province');
            $table->string('Dr_Distract');
            $table->string('Dr_Village');
            // Make sure the foreign key constraint is correct
            $table->foreign('Dr_Id')->references('Dr_Id')->on('doctor_models')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('doctor_add_models');
    }
};




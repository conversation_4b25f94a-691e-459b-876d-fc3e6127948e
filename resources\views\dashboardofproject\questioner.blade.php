@extends('layouts/Admin')

@section('title', 'پوښتنلیک')

@section('contents')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">{{ translateText('د پوښتنو لیست') }}</h1>
        <button id="addQuestionBtn" onclick="document.getElementById('addQuestionModal').style.display='flex';" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition duration-300">
            <i class="fas fa-plus mr-2"></i> {{ translateText('نوې پوښتنه') }}
        </button>
    </div>
    
    @if(session('success'))
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
            <p>{{ session('success') }}</p>
        </div>
    @endif
    
    @if(session('error'))
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
            <p>{{ session('error') }}</p>
        </div>
    @endif
    
    @if(session('warning'))
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4" role="alert">
            <p>{{ session('warning') }}</p>
        </div>
    @endif

    <!-- Search and Filter Section -->
    <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg mb-6 overflow-hidden">
        <div class="p-6">
            <form id="searchForm" method="GET" action="{{ route('questioner.index') }}" class="flex flex-wrap gap-4">
                <!-- Search Input -->
                <div class="flex-1 min-w-64">
                    <input type="text" id="searchInput" name="search" value="{{ request('search') }}"
                           placeholder="{{ translateText('د پوښتنو لټون...') }}"
                           class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                </div>

                <!-- Question Type Filter -->
                <div class="min-w-48">
                    <select id="questionTypeFilter" name="question_type" class="w-full px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">{{ translateText('ټول ډولونه') }}</option>
                        <option value="mental" {{ request('question_type') == 'mental' ? 'selected' : '' }}>{{ translateText('رواني') }}</option>
                        <option value="physical" {{ request('question_type') == 'physical' ? 'selected' : '' }}>{{ translateText('فزیکي') }}</option>
                        <option value="social" {{ request('question_type') == 'social' ? 'selected' : '' }}>{{ translateText('ټولنیز') }}</option>
                    </select>
                </div>
            </form>
        </div>
    </div>

    <!-- Questions Count -->
    <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg mb-4 overflow-hidden">
        <div class="p-4">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">
                    {{ translateText('ټولې پوښتنې') }}: <span class="text-blue-600">{{ $questions->total() }}</span>
                </h3>
                @if(request('search') || request('question_type'))
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        {{ translateText('د فلټر شوو پایلو شمیر') }}: <span class="text-green-600">{{ $questions->count() }}</span>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Questions List Section -->
    <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg overflow-hidden">
        <div class="p-6">
            @if($questions->count() == 0)
                <div class="text-center py-8">
                    <p class="text-gray-500 dark:text-gray-400">{{ translateText('هیڅ پوښتنه ونه موندل شوه') }}</p>
                </div>
            @else
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    {{ translateText('آی ډی') }}
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    {{ translateText('پوښتنه') }}
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    {{ translateText('ډول') }}
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    {{ translateText('ځوابونه') }}
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    {{ translateText('عملیات') }}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($questions as $index => $question)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        {{ ($questions->currentPage() - 1) * $questions->perPage() + $index + 1 }}
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        <div class="max-w-xs truncate">{{ $question->question_text }}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                        <span class="px-2 py-1 text-xs rounded-full
                                            @if($question->question_type == 'mental') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                            @elseif($question->question_type == 'physical') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                            @elseif($question->question_type == 'social') bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200
                                            @else bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 @endif">
                                            @if($question->question_type == 'mental') {{ translateText('رواني') }}
                                            @elseif($question->question_type == 'physical') {{ translateText('فزیکي') }}
                                            @elseif($question->question_type == 'social') {{ translateText('ټولنیز') }}
                                            @else {{ translateText('عمومي') }} @endif
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                        <div class="text-xs">
                                            <div><strong>A:</strong> {{ $question->A }}</div>
                                            <div><strong>B:</strong> {{ $question->B }}</div>
                                            <div><strong>C:</strong> {{ $question->C }}</div>
                                            <div><strong>D:</strong> {{ $question->D }}</div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button onclick="showEditQuestionModal({{ $question->id }})"
                                                class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300 mr-3">
                                            <i class="fas fa-edit"></i> {{ translateText('تعدیل') }}
                                        </button>

                                        <button onclick="confirmDeleteQuestion({{ $question->id }})"
                                                class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                                            <i class="fas fa-trash-alt"></i> {{ translateText('حذف') }}
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    {{ $questions->links() }}
                </div>
            @endif
        </div>
    </div>
    
    <!-- Add Question Modal -->
    <div id="addQuestionModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; align-items: center; justify-content: center;" class="flex">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl mx-4">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800 dark:text-white">{{ translateText('نوې پوښتنه اضافه کول') }}</h3>
                    <button onclick="document.getElementById('addQuestionModal').style.display='none';" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="addQuestionForm" action="{{ route('questioner.store') }}" method="POST">
                    @csrf

                    <div class="mb-4">
                        <label for="question_text" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ translateText('پوښتنه') }}</label>
                        <textarea id="question_text" name="question_text" rows="3"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                            required></textarea>
                    </div>

                    <div class="mb-4">
                        <label for="question_type" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ translateText('د پوښتنې ډول') }}</label>
                        <select id="question_type" name="question_type"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" required>
                            <option value="">{{ translateText('د پوښتنې ډول غوره کړئ') }}</option>
                            <option value="mental">{{ translateText('رواني') }}</option>
                            <option value="physical">{{ translateText('فزیکي') }}</option>
                            <option value="social">{{ translateText('ټولنیز') }}</option>
                        </select>
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="option_a" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ translateText('ځواب A') }}</label>
                            <input type="text" id="option_a" name="A" value="هیڅ نه"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" required>
                        </div>
                        <div>
                            <label for="option_b" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ translateText('ځواب B') }}</label>
                            <input type="text" id="option_b" name="B" value="لږه اندازه"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" required>
                        </div>
                        <div>
                            <label for="option_c" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ translateText('ځواب C') }}</label>
                            <input type="text" id="option_c" name="C" value="لږه ډېره اندازه"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" required>
                        </div>
                        <div>
                            <label for="option_d" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ translateText('ځواب D') }}</label>
                            <input type="text" id="option_d" name="D" value="ډېره اندازه"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" required>
                        </div>
                    </div>
                    
                    <div class="flex justify-end">
                        <button type="button" onclick="document.getElementById('addQuestionModal').style.display='none';" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition duration-300 mr-2">
                            {{ translateText('لغوه کول') }}
                        </button>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition duration-300">
                            {{ translateText('ثبتول') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Edit Question Modal -->
    <div id="editQuestionModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl mx-4">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800 dark:text-white">{{ translateText('د پوښتنې تعدیل') }}</h3>
                    <button onclick="hideEditQuestionModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="editQuestionForm" action="" method="POST">
                    @csrf
                    @method('PUT')

                    <div class="mb-4">
                        <label for="edit_question_text" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ translateText('پوښتنه') }}</label>
                        <textarea id="edit_question_text" name="question_text" rows="3"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                            required></textarea>
                    </div>

                    <div class="mb-4">
                        <label for="edit_question_type" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ translateText('د پوښتنې ډول') }}</label>
                        <select id="edit_question_type" name="question_type"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" required>
                            <option value="">{{ translateText('د پوښتنې ډول غوره کړئ') }}</option>
                            <option value="mental">{{ translateText('رواني') }}</option>
                            <option value="physical">{{ translateText('فزیکي') }}</option>
                            <option value="social">{{ translateText('ټولنیز') }}</option>
                        </select>
                    </div>

                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label for="edit_option_a" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ translateText('ځواب A') }}</label>
                            <input type="text" id="edit_option_a" name="A"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" required>
                        </div>
                        <div>
                            <label for="edit_option_b" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ translateText('ځواب B') }}</label>
                            <input type="text" id="edit_option_b" name="B"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" required>
                        </div>
                        <div>
                            <label for="edit_option_c" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ translateText('ځواب C') }}</label>
                            <input type="text" id="edit_option_c" name="C"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" required>
                        </div>
                        <div>
                            <label for="edit_option_d" class="block text-gray-700 dark:text-gray-300 font-medium mb-2">{{ translateText('ځواب D') }}</label>
                            <input type="text" id="edit_option_d" name="D"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" required>
                        </div>
                    </div>
                    
                    <div class="flex justify-end">
                        <button type="button" onclick="hideEditQuestionModal()" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition duration-300 mr-2">
                            {{ translateText('لغوه کول') }}
                        </button>
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition duration-300">
                            {{ translateText('تازه کول') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Delete Question Confirmation Modal -->
    <div id="deleteQuestionModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md mx-4">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800 dark:text-white">{{ translateText('د پوښتنې حذف کول') }}</h3>
                    <button onclick="hideDeleteQuestionModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <p class="text-gray-700 dark:text-gray-300 mb-6">{{ translateText('آیا تاسو ډاډه یاست چې دا پوښتنه حذف کړئ؟') }}</p>
                
                <form id="deleteQuestionForm" action="" method="POST">
                    @csrf
                    @method('DELETE')
                    
                    <div class="flex justify-end">
                        <button type="button" onclick="hideDeleteQuestionModal()" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition duration-300 mr-2">
                            {{ translateText('لغوه کول') }}
                        </button>
                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition duration-300">
                            {{ translateText('حذف کول') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        // Add Question Modal
        function showAddQuestionModal() {
            const modal = document.getElementById('addQuestionModal');
            if (modal) {
                modal.style.display = 'flex';
            }
        }

        function hideAddQuestionModal() {
            const modal = document.getElementById('addQuestionModal');
            if (modal) {
                modal.style.display = 'none';
                document.getElementById('addQuestionForm').reset();
            }
        }
        
        // Edit Question Modal
        function showEditQuestionModal(id) {
            // Fetch question data via AJAX
            fetch(`{{ url('questioner') }}/${id}/edit`)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('edit_question_text').value = data.question_text;
                    document.getElementById('edit_question_type').value = data.question_type;
                    document.getElementById('edit_option_a').value = data.A;
                    document.getElementById('edit_option_b').value = data.B;
                    document.getElementById('edit_option_c').value = data.C;
                    document.getElementById('edit_option_d').value = data.D;
                    document.getElementById('editQuestionForm').action = `{{ url('questioner') }}/${id}`;

                    const editModal = document.getElementById('editQuestionModal');
                    editModal.classList.remove('hidden');
                    editModal.classList.add('flex');
                    editModal.style.display = 'flex';
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('د پوښتنې معلوماتو په راوړلو کې ستونزه');
                });
        }
        
        function hideEditQuestionModal() {
            const editModal = document.getElementById('editQuestionModal');
            editModal.classList.remove('flex');
            editModal.classList.add('hidden');
            editModal.style.display = 'none';
        }
        
        // Delete Question Modal
        function confirmDeleteQuestion(id) {
            document.getElementById('deleteQuestionForm').action = `{{ url('questioner') }}/${id}`;

            const deleteModal = document.getElementById('deleteQuestionModal');
            deleteModal.classList.remove('hidden');
            deleteModal.classList.add('flex');
            deleteModal.style.display = 'flex';
        }

        function hideDeleteQuestionModal() {
            const deleteModal = document.getElementById('deleteQuestionModal');
            deleteModal.classList.remove('flex');
            deleteModal.classList.add('hidden');
            deleteModal.style.display = 'none';
        }

        // Auto-search functionality
        let searchTimeout;

        function performSearch() {
            document.getElementById('searchForm').submit();
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Get elements
            const addModal = document.getElementById('addQuestionModal');
            const editModal = document.getElementById('editQuestionModal');
            const deleteModal = document.getElementById('deleteQuestionModal');
            const searchInput = document.getElementById('searchInput');
            const questionTypeFilter = document.getElementById('questionTypeFilter');

            // Auto-search on typing
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(function() {
                        performSearch();
                    }, 500); // Wait 500ms after user stops typing
                });
            }

            // Auto-filter on dropdown change
            if (questionTypeFilter) {
                questionTypeFilter.addEventListener('change', function() {
                    performSearch();
                });
            }

            // Add click event listener to the close button
            const closeBtn = document.getElementById('closeAddModalBtn');
            if (closeBtn) {
                closeBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Close button clicked!');
                    hideAddQuestionModal();
                });
            }

            // Add click event listener to the cancel button
            const cancelBtn = document.getElementById('cancelAddBtn');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Cancel button clicked!');
                    hideAddQuestionModal();
                });
            }

            // Close modals when clicking outside
            if (addModal) {
                addModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        hideAddQuestionModal();
                    }
                });
            }

            if (editModal) {
                editModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        hideEditQuestionModal();
                    }
                });
            }

            if (deleteModal) {
                deleteModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        hideDeleteQuestionModal();
                    }
                });
            }
        });
    </script>
</div>
@endsection
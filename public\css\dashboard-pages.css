/* Dashboard Pages Shared Styles */
:root {
    --primary: #4a6baf;
    --primary-dark: #3a5a9f;
    --secondary: #5d9cec;
    --success: #5cb85c;
    --danger: #d9534f;
    --warning: #f0ad4e;
    --info: #5bc0de;
    --light: #f8f9fa;
    --dark: #343a40;
    --purple: #8e44ad;
    --teal: #20c997;
    --orange: #fd7e14;
    --pink: #e83e8c;
    --indigo: #6610f2;
}

/* Page Container */
.dashboard-page-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
    min-height: 100vh;
    padding: 2rem;
    transition: all 0.3s ease;
}

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--primary);
    position: relative;
}

.page-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100px;
    height: 2px;
    background: var(--secondary);
}

.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.page-title i {
    background: var(--primary);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.page-actions {
    display: flex;
    gap: 0.75rem;
}

/* Cards */
.dashboard-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 2rem;
    border-top: 5px solid var(--primary);
    position: relative;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-title i {
    color: var(--primary);
}

.card-body {
    padding: 1.5rem;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark);
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(74, 107, 175, 0.25);
    outline: none;
}

.form-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%234a6baf' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 16px 12px;
}

.form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(74, 107, 175, 0.25);
    outline: none;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    gap: 0.5rem;
    border: none;
}

.btn-primary {
    background: var(--primary);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background: var(--secondary);
    color: white;
}

.btn-secondary:hover {
    background: #4a8ddc;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-success {
    background: var(--success);
    color: white;
}

.btn-success:hover {
    background: #4cae4c;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-danger {
    background: var(--danger);
    color: white;
}

.btn-danger:hover {
    background: #c9302c;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Tables */
.dashboard-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.dashboard-table th {
    background: var(--primary);
    color: white;
    padding: 1rem;
    text-align: right;
    font-weight: 500;
}

.dashboard-table th:first-child {
    border-top-right-radius: 0.5rem;
}

.dashboard-table th:last-child {
    border-top-left-radius: 0.5rem;
}

.dashboard-table tr:last-child td:first-child {
    border-bottom-right-radius: 0.5rem;
}

.dashboard-table tr:last-child td:last-child {
    border-bottom-left-radius: 0.5rem;
}

.dashboard-table td {
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.dashboard-table tbody tr {
    transition: all 0.3s ease;
}

.dashboard-table tbody tr:hover {
    background-color: rgba(74, 107, 175, 0.05);
}

.dashboard-table tbody tr:last-child td {
    border-bottom: none;
}

/* Action buttons in tables */
.table-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.action-btn-view {
    background: var(--info);
}

.action-btn-edit {
    background: var(--secondary);
}

.action-btn-delete {
    background: var(--danger);
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    border-right: 4px solid;
}

.alert-success {
    background-color: #d4edda;
    border-color: var(--success);
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: var(--danger);
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: var(--warning);
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: var(--info);
    color: #0c5460;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.pagination-item {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    color: var(--dark);
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.pagination-item:hover {
    background: var(--primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.pagination-item.active {
    background: var(--primary);
    color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-page-container {
        padding: 1rem;
    }
    
    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .table-actions {
        flex-direction: column;
    }
}

/* Animation effects */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.dashboard-card {
    animation: fadeIn 0.5s ease-out forwards;
}

.dashboard-card:nth-child(2) {
    animation-delay: 0.1s;
}

.dashboard-card:nth-child(3) {
    animation-delay: 0.2s;
}

.dashboard-card:nth-child(4) {
    animation-delay: 0.3s;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}
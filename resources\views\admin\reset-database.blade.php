@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">Reset Database</div>

                <div class="card-body">
                    <p>This will reset the database and run all migrations and seeders. All data will be lost.</p>
                    <p>Are you sure you want to continue?</p>

                    <div class="mt-3">
                        <button id="resetButton" class="btn btn-danger">Reset Database</button>
                        <a href="{{ route('home') }}" class="btn btn-secondary">Cancel</a>
                    </div>

                    <div id="result" class="mt-3" style="display: none;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.getElementById('resetButton').addEventListener('click', function() {
        if (confirm('Are you sure you want to reset the database? All data will be lost.')) {
            this.disabled = true;
            this.textContent = 'Resetting...';
            
            fetch('{{ route("reset-database") }}')
                .then(response => response.json())
                .then(data => {
                    const resultDiv = document.getElementById('result');
                    resultDiv.style.display = 'block';
                    
                    if (data.success) {
                        resultDiv.className = 'alert alert-success';
                        resultDiv.textContent = data.message;
                    } else {
                        resultDiv.className = 'alert alert-danger';
                        resultDiv.textContent = data.message;
                    }
                    
                    this.disabled = false;
                    this.textContent = 'Reset Database';
                })
                .catch(error => {
                    const resultDiv = document.getElementById('result');
                    resultDiv.style.display = 'block';
                    resultDiv.className = 'alert alert-danger';
                    resultDiv.textContent = 'Error: ' + error.message;
                    
                    this.disabled = false;
                    this.textContent = 'Reset Database';
                });
        }
    });
</script>
@endsection
<?php

namespace App\Http\Controllers;

use App\Models\NewsModel;
use Illuminate\Http\Request;
use App\Models\ArticalModel;

class SearchController extends Controller
{
    public function search(Request $request)
    {
        $query = $request->input('query');
        
        if (empty($query)) {
            return redirect()->route('home')->with('search_error', trans_static('لطفۍ د لټون لپاره متن داخل کړئ'));
        }
        
        // Search in ArticalModel for all types of content
        $articles = ArticalModel::where(function($q) use ($query) {
            $q->where('A_Title', 'like', "%{$query}%")
              ->orWhere('A_Author', 'like', "%{$query}%")
              ->orWhere('A_Language', 'like', "%{$query}%")
              ->orWhere('A_catagory', 'like', "%{$query}%")
              ->orWhere('A_Type', 'like', "%{$query}%");
        })
        ->orderBy('created_at', 'desc')
        ->paginate(10);
        
        // Search in NewsModel
        $news = NewsModel::where(function($q) use ($query) {
            $q->where('News_Title', 'like', "%{$query}%")
              ->orWhere('News_Discription', 'like', "%{$query}%");
        })
        ->orderBy('created_at', 'desc')
        ->paginate(10);
        
        // Check the first news item to see column names
        if ($news->count() > 0) {
            // Uncomment this line to debug
            // dd($news->first()->toArray());
        }
        
        // Preserve search parameter in pagination links
        $articles->appends(['query' => $query]);
        $news->appends(['query' => $query]);
        
        return view('search_results', [
            'search_query' => $query,
            'articles' => $articles,
            'news' => $news
        ]);
    }
}



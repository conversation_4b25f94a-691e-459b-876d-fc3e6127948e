<!DOCTYPE html>
<html>
<head>
    <title>Translation Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-info { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .translation-test { background: #e8f4fd; padding: 10px; margin: 5px 0; border-radius: 3px; }
        .language-links { margin: 20px 0; }
        .language-links a { padding: 10px 15px; background: #007cba; color: white; text-decoration: none; margin: 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Translation System Debug</h1>
    
    <div class="language-links">
        <a href="{{ url('/language/ps') }}">Switch to Pashto (پښتو)</a>
        <a href="{{ url('/language/fa') }}">Switch to Dari (دری)</a>
    </div>
    
    <div class="debug-info">
        <h3>Current Session Info:</h3>
        <p><strong>Session Locale:</strong> {{ session('locale', 'not set') }}</p>
        <p><strong>App Locale:</strong> {{ app()->getLocale() }}</p>
        <p><strong>Session ID:</strong> {{ session()->getId() }}</p>
    </div>
    
    <div class="debug-info">
        <h3>Translation Tests:</h3>
        
        <div class="translation-test">
            <strong>Test 1 - translateText():</strong><br>
            Input: "ستاسو نوم"<br>
            Output: "{{ translateText('ستاسو نوم') }}"
        </div>
        
        <div class="translation-test">
            <strong>Test 2 - trans_static():</strong><br>
            Input: "ستاسو نوم"<br>
            Output: "{{ trans_static('ستاسو نوم') }}"
        </div>
        
        <div class="translation-test">
            <strong>Test 3 - Direct trans():</strong><br>
            Input: "messages.ستاسو نوم"<br>
            Output: "{{ trans('messages.ستاسو نوم', [], session('locale', 'ps')) }}"
        </div>
        
        <div class="translation-test">
            <strong>Test 4 - Language Button:</strong><br>
            Input: "ژبه"<br>
            translateText: "{{ translateText('ژبه') }}"<br>
            trans_static: "{{ trans_static('ژبه') }}"
        </div>
        
        <div class="translation-test">
            <strong>Test 5 - Contact Form:</strong><br>
            Input: "ستاسو ایمیل"<br>
            translateText: "{{ translateText('ستاسو ایمیل') }}"<br>
            trans_static: "{{ trans_static('ستاسو ایمیل') }}"
        </div>
    </div>
    
    <div class="debug-info">
        <h3>Available Translations (FA):</h3>
        @php
            $faTranslations = trans('messages', [], 'fa');
            if (is_array($faTranslations)) {
                foreach (array_slice($faTranslations, 0, 10) as $key => $value) {
                    echo "<p><strong>$key:</strong> $value</p>";
                }
            } else {
                echo "<p>No translations found or error loading</p>";
            }
        @endphp
    </div>
    
    <div class="debug-info">
        <h3>Test Current Page:</h3>
        <a href="{{ url('/') }}">Go to Homepage</a> |
        <a href="{{ url('/ketabs') }}">Go to Books</a> |
        <a href="{{ url('/paper') }}">Go to Papers</a>
    </div>
</body>
</html>

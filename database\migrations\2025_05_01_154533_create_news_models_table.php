<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('news_models', function (Blueprint $table) {
              $table->bigIncrements('News_Id');
              $table->string('News_Title');
              $table->longText('News_Discription');
              $table->unsignedBigInteger('user_id')->default(2024);
              $table->foreign('user_id')->references('user_id')->on('users')->onDelete('cascade');
              $table->string('New_Images')->nullable();
              $table->timestamps();
        });
    }

    
   


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('news_models');
    }
};
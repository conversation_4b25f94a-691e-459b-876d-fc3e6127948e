@extends('layouts/Main')

@section('title','Books')

@section('contents')
<div id="ssd">
    <h1>هغه معتبر کتابونه چی په واسطه یی کولی شی دروانی ناروغیو کچه په ټولنه کی کمه کړی</h1>


<form action="{{ route('policy.search') }}" method="GET">
    <div id="search_sec">
        <button type="submit" id="serch_btn">
            <i class="fas fa-search"></i>
        </button>
        <input type="search" name="search" placeholder="{{trans_static('پلټنه وکړئ....')}}" value="{{ request('search') }}">
    </div>
</form>

    <!-- ======== Cards Section ======== -->
    <section class="team-container">
        @if($policy->count() > 0)
            @foreach($policy as $index => $book)
                <!-- Card {{ $index + 1 }} -->
                <div class="team-card {{ $index >= 4 ? 'hidden' : '' }}">
                    @if($book->A_Image && file_exists(public_path('imagese/' . $book->A_Image)))
                        <img src="{{ asset('imagese/' . $book->A_Image) }}" alt="{{ $book->A_Title }}" class="top-img">
                    @else
                        <img src="{{ asset('imagese/book1.jpg') }}" alt="{{ trans_static('پالیسي') }}" class="top-img">
                    @endif

                    <div class="card-body">
                        <p>{{ $book->A_catagory }}</p>
                        <h3>{{ $book->A_Title }}</h3>
                        <p>{{ $book->A_Description ?? 'دا پالیسي د ذهني روغتیا اړوند مفید معلومات لري او د ټولنې د ذهني روغتیا د ښه کولو لپاره ګټوره ده.' }}</p>
                    </div>

                    <div class="card-footer">
                        <div class="profile">
                            @if($book->A_Author_Image && file_exists(public_path('imagese/' . $book->A_Author_Image)))
                                <img src="{{ asset('imagese/' . $book->A_Author_Image) }}" alt="{{ $book->A_Author }}" class="profile-img">
                            @else
                                <img src="{{ asset('imagese/author-default.jpg') }}" alt="{{ $book->A_Author }}" class="profile-img">
                            @endif
                            <div>
                                <span>{{ $book->A_Author }}</span>
                                <p>{{ \Carbon\Carbon::parse($book->A_Publication_date)->format('Y,m,d') }}</p>
                            </div>
                        </div>
                        @if($book->A_File && file_exists(public_path('imagese/' . $book->A_File)))
                            <a href="{{ asset('imagese/' . $book->A_File) }}" target="_blank">{{ trans_static('نور ولولئ') }}</a>
                        @else
                            <a href="{{ asset('book/دعاګاني.pdf') }}" target="_blank">{{ trans_static('نور ولولئ') }}</a>
                        @endif
                    </div>
                </div>
            @endforeach
        @else
            <div class="no-books">
                <p>{{ trans_static('اوس مهال پالیسي شتون نلري.') }}</p>
            </div>
        @endif
    </section>

    @if($policy->count() > 4)
        <!-- Show More Button -->
        <div style="text-align: center; margin-top: 20px;">
            <button id="showMoreBtn" class="show-more-btn">{{ trans_static('نور پالیسي ښکاره کړئ') }}</button>
        </div>
    @endif
</div>
<div class="pagination">
    @php
    $current = $policy->currentPage();
    $last = $policy->lastPage();
    @endphp

    @if ($current > 1)
    <button onclick="goToPage({{ $current - 1 }})">مخکنی</button>
    @endif

    @for ($i = 1; $i <= $last; $i++)
        <button onclick="goToPage({{ $i }})" class="{{ $i == $current ? 'active' : '' }}">
        {{ $i }}
        </button>
    @endfor

    @if ($current < $last)
        <button onclick="goToPage({{ $current + 1 }})">ورستنی</button>
    @endif
</div>

<script>
function goToPage(page) {
    const url = new URL(window.location.href);
    url.searchParams.set('page', page);

    // Preserve search parameter when navigating between pages
    const search = document.querySelector('input[name="search"]')?.value;
    if (search) {
        url.searchParams.set('search', search);
    }

    window.location.href = url.toString();
}

document.addEventListener("DOMContentLoaded", function() {
    const cards = document.querySelectorAll('.team-card');
    const showMoreBtn = document.getElementById('showMoreBtn');

    if (showMoreBtn) {
        showMoreBtn.addEventListener('click', function() {
            cards.forEach(card => {
                card.classList.remove('hidden');
            });
            showMoreBtn.style.display = 'none';
        });
    }
});
</script>
@endsection
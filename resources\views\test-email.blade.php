<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Configuration Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold mb-6 text-center text-gray-800">Email Configuration Test</h1>
            
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif
            
            @if(session('error'))
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {{ session('error') }}
                </div>
            @endif
            
            <div class="mb-6 p-4 bg-blue-50 rounded-lg">
                <h3 class="font-semibold text-blue-800 mb-2">Current Email Configuration:</h3>
                <ul class="text-sm text-blue-700">
                    <li><strong>Mailer:</strong> {{ config('mail.default') }}</li>
                    <li><strong>Host:</strong> {{ config('mail.mailers.smtp.host') }}</li>
                    <li><strong>Port:</strong> {{ config('mail.mailers.smtp.port') }}</li>
                    <li><strong>From:</strong> {{ config('mail.from.address') }}</li>
                </ul>
            </div>
            
            <form method="POST" action="{{ route('send.test.email') }}" class="space-y-4">
                @csrf
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Test Email Address:</label>
                    <input type="email" name="email" id="email" required 
                           value="<EMAIL>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors">
                    Send Test Email
                </button>
            </form>
            
            @if(config('mail.mailers.smtp.username') == '<EMAIL>' && config('mail.mailers.smtp.password') == 'REPLACE_WITH_YOUR_APP_PASSWORD')
                <div class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <h3 class="font-semibold text-red-800 mb-2">⚠️ Configuration Required!</h3>
                    <p class="text-sm text-red-700 mb-3">You need to set up your Gmail App Password first.</p>
                    <a href="/gmail-setup" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors inline-block">
                        📧 Gmail Setup Guide
                    </a>
                </div>
            @else
                <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h3 class="font-semibold text-green-800 mb-2">✅ Configuration Looks Good!</h3>
                    <p class="text-sm text-green-700">Your email settings appear to be configured. Test sending an email below.</p>
                </div>
            @endif

            <div class="mt-6 p-4 bg-yellow-50 rounded-lg">
                <h3 class="font-semibold text-yellow-800 mb-2">Quick Setup:</h3>
                <ol class="text-sm text-yellow-700 list-decimal list-inside space-y-1">
                    <li><a href="/gmail-setup" class="text-blue-600 underline">Follow Gmail Setup Guide</a></li>
                    <li>Update your .env file with app password</li>
                    <li>Run: <code class="bg-gray-200 px-1 rounded">php artisan config:clear</code></li>
                    <li>Test email sending here</li>
                </ol>
            </div>
            
            <div class="mt-4 text-center">
                <a href="/feedbackd" class="text-blue-600 hover:text-blue-800 underline">
                    ← Back to Feedback System
                </a>
            </div>
        </div>
    </div>
</body>
</html>

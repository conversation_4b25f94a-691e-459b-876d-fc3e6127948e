<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContactUsModel extends Model
{
    use HasFactory;
    
    protected $table = 'contact_us_models';
    protected $primaryKey = 'contact_id';
    
    protected $fillable = [
        'fname',
        'lname',
        'email',
        'phone',
        'message',
        'User_id'
    ];
    
    public function user()
    {
        return $this->belongsTo(User::class, 'User_id', 'id');
    }
}




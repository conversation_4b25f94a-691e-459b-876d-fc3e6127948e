
    *{
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
 body{
  background-color: #212d40;
 }
  #button-ssection{
     width: 100%;
     height: 8vh;
     display: flex;
     background-color: black;
     justify-content: space-between;
     align-items: center;
     position: fixed;
     top: 0;
     z-index: 100;


  }
  #rigth-button-ssection{
    display: flex;
    flex-direction: row;

  }
  #lefte-button-ssection{
    display: flex;
    flex-direction: row;

  }
  #search {
    width: 40%;
    height: 2.5em;
    background-color: white;
    display: flex;
    border-radius: 10px;
  }
#search input[type="text"] {
  background: white;
  color: rgb(53, 50, 50);
  padding: 10px;
  width: 80%;
  direction: rtl;
  border: none;
  height: 100%;
  border-radius: 10px;
  }
  #search button{
    background-color: white;
    text-decoration: none;
  }
  #search button img{
  margin: auto;
  width: 30px;
  height: 30px;
  border-radius: 10px;
  cursor: pointer;
  margin-top: 4px;
  }

  #button-ssection button a{
     color: black;
     text-decoration: none;


  }
  #button-ssection button{
     margin: 0.5vh;

     width: 50px;
     height: 5vh;
     border: none;
     outline: none;
     border-radius: 2em;


  }
  #button-ssection button  :hover{
      background-color: gray;
  }
  @media only screen and (max-width: 999px){
    #button-ssection{
      width: 100%;
    }
  }
  /* ======Navbar=============  */
  .navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #212d40;
    padding: 0 20px;
    /* position: sticky; */
    width: 100%;
    position: fixed;
    top: 3em;
    z-index: 100;
  }

  .logo img {
  width: 70px;
  height: 70px;
  border-radius: 50%;

  }

  .menu-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 28px;
    cursor: pointer;
    display: none;

  }
  .menu-toggle:hover{
    border: 1px solid  white;
  }
  .nav-links {
    list-style: none;
    display: flex;
    padding-right: 20px;
  }

  .nav-links li {
    margin-left: 20px;
  }

  .nav-links a {
    color: white;
    text-decoration: none;
    font-size: 18px;
    font-weight: bold;
    padding-right: 20px;
    position: relative;
    transition: all 0.4s ease;
    direction: rtl;
  }
  .nav-links a::before{
    content: '';
    position: absolute;
    width: 0;
    left: 0;
    bottom: 0;
    height: 2px;
    background-color: rgb(198, 83, 83);
    transition: all 0.4s ease-in-out;
  }
  .nav-links a:hover::before{
    width: 100%;
  }
  .nav-links a:hover,a.activ{
    color:rgb(198, 83, 83);
  }

  /* Responsive Settings */
  @media (max-width: 768px) {
    .navbar{
      width: 100%;
    }
    .menu-toggle {
      display: block;
    }

    .nav-links {
      display: none;
      flex-direction: column;
      width: 60%;
      background-color: #212d40;
      position: absolute;
      top: 70px;
      right: 0;
    }

    .nav-links.active {
      display: flex;
    }
    .nav-links a{
      padding-top: 2em;
      box-shadow: 0 0 5px rgb(65, 47, 47);
      transition: all 0.4s ease-in-out;
    }
    .nav-links li {

  text-align: center;
      margin: 10px 0;
    }
  }

  /* Content */
  .content {
    padding: 50px 20px;
    background-color: #f8f9fa;
  }

  .more-content {
    padding: 50px 20px;
    background-color: #e9ecef;
  }


/* =====Home page============================  */

.slider {
height: 79.5vh;
/* background-color: black; */
width: 100%;
margin-top: 7.6em;
background-size: cover;
background-position: center;
/* text-align: center; */
display: flex;
flex-direction: column;
/* justify-content: center; */
/* align-items: center; */
transition: background-image 2s ease;

}

.slider .content {
/* background: rgba(255, 255, 255, 0); */
padding: 10px auto;
border-radius: 10px;
position: relative;
width: 100%;
height: 3em;
}

 .slider h1 {
  text-align: center;
  direction: rtl;
font-size: 2em;
background: rgba(255, 255, 255, 0.7);
padding: 10px 0.1px;
border-radius: 14px;
width: 85%;
z-index: 1;
margin: 0.01em auto;

}


.slider #slide-text {
margin-top: 19.5em;
/* background: rgba(255, 255, 255, 1); */
color: white;
background-color: black;
direction: rtl;
text-align: center;
padding: 10px 20px;
border-radius: 14px;
font-size: 1.2rem;
width: 100%;
transition: opacity 0.5s;
}
.content .nav-buttons {
margin-top: 20px;
}

.nav-buttons i {
font-size: 30px;
margin: 0 10px;
cursor: pointer;
transition: color 0.3s ease;
}


/* Responsive */
 @media (max-width: 999px) {
  .slider h1 {
  font-size: 2em;
}
}
@media (max-width: 600px) {
  .slider h1 {
    font-size: 1.7em;
    width: 90%;
  }
}
@media (max-width: 1150px) {
  .slider h1 {
    font-size: 1em;
  }
}
    /* ====populor-section=========================  */
    #populor-section{
      width: 100%;
      height: 72vh;
      /* background-image: linear-gradient(rgba(0,0,0,0.4),rgba(0,0,0,0.5)),url(./assets/New_imagese/library.jpg); */
      background-color: #212d40;
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;

    }
    #populor-section h1{
      color: white;
      font-size: 2em;
      text-align: center;
      padding-top: 20px;
    }
    #populor-section p{
      color: white;
      font-size: 1.3em;
      text-align: center;
      padding-top: 5px;
      width: 100%;
    }


    #populor-section #wrapper{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6em 2em;
      margin-top: 70px;
      width: 100%;
    }

    #populor-section #wrapper .free-part{
      width: 24rem;
      border: 3px solid white;
      border-radius: 20px;
      display: flex;
      flex-direction: column;
      transition: 2s;

    }


    #populor-section #wrapper .free-part h3{
       color: white;
       font-size: 2em;
       margin: 10px auto;
       text-align: center;
       direction: rtl;

    }
    #populor-section #wrapper .free-part p{
       color: white;
       font-size: 20px;
       margin-bottom: 5px;

    }

    #populor-section #wrapper .free-part .btns{
      z-index: 1;
      display: flex;
      background-color: white;
      color: black;
      padding: 1em 3em;
      text-decoration: none;
      border-radius: 10px;
      margin: 0 6.5em ;
      margin-bottom: 0.5em;
    }

  #populor-section #wrapper .free-part .btns:hover{
      text-decoration: underline;
      background-color: rgb(79, 51, 51);
    color: bisque;


    }

          @media only screen and (max-width: 600px) {
              #populor-section {
                  /* flex-wrap: wrap; */
                  height: auto;
                  width: 100%;

              }
              #populor-section #wrapper{
                display: flex;
                flex-direction: column;
                width: 100%;
                height: auto;
                padding-top: 15px;
              }
              #populor-section #wrapper .free-part{

                 margin-top: 10px;

              }

          }


          @media only screen and (max-width: 999px) {
            #populor-section {
                flex-wrap: wrap;
                height: auto;
                width: 100%;

            }
            #populor-section #wrapper{
              display: flex;
              flex-direction: column;
              width: 100%;
              height: auto;
              padding-top: 15px;
            }
            #populor-section #wrapper .free-part{

               margin-top: 10px;

            }

        }

   /* =====video_section==================  */
   #video_section{
    width: 100%;
    background-color: white;
    height: auto;

   }
   #video_section #wrapper{
    margin: 1em auto ;
    display: flex;
    justify-content: space-between;
    /* flex-direction: row; */
    background-image: url(./assets/New_imagese/New\ folder/sdd.jpg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;

   }
   .newsletter-card {
    background:black;
    border-radius: 10px;
    overflow: hidden;
    width: 23%;
   max-width: 400px;
    color: white;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  }
  .image-section{
    position: relative;
  }
  .image-section img {
    width: 100%;
    height: 14em;
    display: block;

  }
  .image-section::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9), transparent);
  }

  .content-section {
    padding: 20px;
    text-align: center;
  }

  .content-section h1 {
    font-size: 25px;
    margin-bottom: 15px;
    color: #ffffff;
    font-weight: bold;
  }

  .content-section p {
    font-size: 13px;
    margin-bottom: 20px;
    color: #ffffff;
    font-weight: bold;
  }

  .content-section a img{
    background: rgb(216, 213, 213);
    text-decoration: none;
    color: black;
    /* padding: 10px 25px; */
    font-size: 15px;
    font-weight: bold;
    height: 3.5em;
    width: 3.5em;
    border: 2px solid white;
    border-radius: 50%;
    cursor: pointer;

  }
  .content-section a img:hover{
    border: 2px solid red;
  }
   @media only screen and (max-width: 600px) {
    #video_section {
        flex-wrap: wrap;
        height: auto;
        width: 100%;
    }

    #video_section #wrapper{
        width: 100%;
        margin: 1em auto;
        display: flex;
        flex-direction: column;
        margin-top: 0.5em;


    }
    .newsletter-card{

        padding: 1em;
       margin-left: 6em;
        width: 60%;
        margin-bottom: 1em;


    }
    .image-section{
     position: relative;
    }
}
   /* ======testing section==================  */

#addmission-section{
  /* background-image: linear-gradient(rgba(0,0,0,0.6),rgba(0,0,0,0.6)),url('./assets/New_imagese/strees.jpg'); */
  background-color: #212d40;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: auto;
  padding: 100px 0;
  text-align: center;
}
#addmission-section #addmission-content{
  background-color: rgba(255, 255, 255, 0.2);
  width: 70%;
  height: auto;
  margin: 0 auto;
  padding: 30px 30px;
  border-radius: 12px;
}
#addmission-section #addmission-content h1{
  color: white;
  font-size: 32px;

}
#addmission-section #addmission-content p{
  color: white;
  font-weight: bold;
 padding: 20px 0;
 direction: rtl;
}
#addmission-section #addmission-content a{
  padding: 10px 30px;
  background-color: black;
  color: white;
  margin-top: 15px;
  text-decoration: none;
  font-size: 17px;
  display: inline-block;
  border-radius: 0.4em;

}
#addmission-section #addmission-content a:hover{
  background-color:#0d6b6b;
  color: white;
}
/*=======testemonial section==========  */
#boody {
  /* font-family: Arial, sans-serif; */
  background-color: #212d40;
  margin: 0;
  padding: 0;
  text-align: center;
}

.page-title {
  color: white;
  /* margin-top: 20px; */
  padding-top: 25px;
  font-size: 28px;
}

.slider-container {
  width: 90%;
  max-width: 800px;
  background-color: white;
  color: black;
  border-radius: 10px;
  margin: 30px auto;
  position: relative;
}

.slide {
  display: none;
  /* display: flex; */
  align-items: center;
  /* justify-content: center; */
  gap: 20px;
  flex-direction: row;
}

.slide img {
  height: 300px;
  width: 200px;
  object-fit: cover;
  border-radius: 8px;
  box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.2);
}

.text {
  text-align: right;
}
.text h1 {
  direction: rtl;
}
.text p{
  direction: rtl;
}
.slide.active {
  display: flex;
}

.dots {
  text-align: center;
  margin-top: 20px;
}

.dot {
  height: 15px;
  width: 15px;
  margin: 0 5px;
  background-color: #bbb;
  display: inline-block;
  border-radius: 50%;
  cursor: pointer;
}

.dot.active {
  background-color: #333;
}
 @media (max-width: 600px) {
  .slide {
    flex-direction: column;
  }
  .text {
    text-align: center;
  }
}

/*   ====== contact_us  section ===========    */


#contact_us {
  width: 100%;
  height: 30em;
  background-color: #212d40;

}
#contact_us h1{
  text-align: center;
  padding-top: 0.5em;
  color: white;
}
#contact_us p{
  text-align: center;
  margin-top: 1.5em;
  direction: rtl;
  color: white;
  font-size: 1em;
  padding-bottom: 1em;
}
#conteaner{
  display: flex;
  justify-content: space-between;
  margin: 0 15%;

}

#left_section{
  width: 18em;
  height: 21em;
  border: 1px black solid;
  border-radius: 1em;
  display: flex;
  flex-direction: column;
  background-color: rgb(223, 222, 222);

}
#left_section h1{
  font-size: 1.5em;
  /* margin-bottom: 0.5em; */
  color: white;
}
#left_section input ,textarea{
  direction: rtl;
  margin-right: 0.6em;
  margin-left: 0.6em;

  border: none;
  border-bottom: 1.4px  black solid ;
padding: 0.7em 0;
padding-right: 2em;
background-color: rgb(223, 222, 222);

}
#left_section textarea{
  height: 10em;
}
#left_section button{

  width: 120px;
    height: 35px;
    background-color: black;
    color: white;
    margin: 1em auto;
    border-radius: 1.5em;

}
#right_section{
  width: 23em;
  height: 23em;
  display: flex;
  background-color: #212d40;
  flex-direction: column;
}
.location_section{
display: flex;
justify-content: right;
margin-top: 0.7em;
}
.location_section img{
  width: 45px;
  height: 45px;
  border-radius: 50%;
}
.location_section a img{
  width: 45px;
  height: 45px;
  border-radius: 50%;
}
.location_section p{
padding-right: 1em;
}
#dsfhgfdg{
  background-color: #212d40;
}
#dfdfdf{
  width: 90%;
  height: 4px;
  background-color: white;
margin:  auto;
border-radius: 20px;
}
@media only screen and (max-width: 900px) {
  #conteaner{
    display: flex;
    flex-direction: column;
  }
  #left_section{
    width: 100%;
    margin: 0 auto;
  }
  #right_section{
   width: 100%;
  }
}
   /* =====footer-section============== */
#footer-section {
  background-color: #212d40;
  /* color: white; */
  width: 100%;
  height: auto;
  /* margin-top: 3em; */
  display: flex;
}
#footer-section .aboutUs-section {
/* color: white; */
  margin: 2em 5%;
  width: 40%;
  display: flex;
  flex-direction: column;
}
.headings {
  color: white;
  margin-bottom: 10px;
  font-size: 1.3em;
  font-weight: bold;
  direction: rtl;

}
.mylinks {
  text-decoration: none;
  color: white;
  /* display: flex; */
  margin-bottom: 4px;
  direction: rtl;
  padding-top: 1em;

}
.mylinks:hover {
  text-decoration: underline;
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Artisan;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Run only the UserSeeder after all migrations are complete
        Artisan::call('db:seed', [
            '--class' => 'Database\\Seeders\\UserSeeder',
            '--force' => true,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // No need to do anything here
    }
};


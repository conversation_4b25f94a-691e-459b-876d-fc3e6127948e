<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Redirect;

class LanguageController extends Controller
{
    public function translatePage($lang)
    {
        // Validate language parameter
        if (!in_array($lang, ['ps', 'fa', 'en'])) {
            $lang = 'ps'; // Default to Pashto if invalid language
        }

        // Store the selected language in the session
        Session::put('locale', $lang);
        Session::save(); // Force save the session

        // Also set the app locale immediately
        app()->setLocale($lang);

        // Redirect back to the previous page
        return Redirect::back();
    }
}


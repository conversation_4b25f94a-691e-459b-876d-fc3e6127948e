<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DoctorAddModel;
use App\Models\DoctorModel;

class DoctorAddressController extends Controller
{
    /**
     * Display a listing of doctor addresses.
     */
    public function index()
    {
        // Get addresses with their related doctors
        $addresses = DoctorAddModel::with('doctor')->get();
        
        return view('dashboardofproject.doctor_addresses', compact('addresses'));
    }
    
    /**
     * Display a listing of doctors with their addresses.
     */
    public function doctors()
    {
        // Get doctors with their addresses
        $doctors = DoctorModel::with('address')->paginate(10);
        
        return view('dashboardofproject.doctors', compact('doctors'));
    }
}




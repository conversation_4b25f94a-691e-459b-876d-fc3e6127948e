<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('questioner_models', function (Blueprint $table) {
            $table->bigIncrements('Q_Id');
            $table->text('Q_Discription');
            $table->integer('Question_No');
            $table->string('A')->nullable();
            $table->string('B')->nullable();
            $table->string('C')->nullable();
            $table->string('D')->nullable();
            $table->string('Selected_Option')->nullable(); // اضافه کول د Selected_Option ستون
            $table->unsignedBigInteger('Patient_Id');
       
            $table->foreign('Patient_Id')->references('Patient_Id')->on('patient_models')->onDelete('cascade');
            

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('questioner_models');
    }
};


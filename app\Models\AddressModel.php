<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AddressModel extends Model
{
    use HasFactory;

    protected $table = 'address_models';
    
    protected $fillable = [
        'doctor_id',
        'country',
        'province',
        'district',
        'village',
        // Add any other address fields you need
    ];

    /**
     * Get the doctor that owns the address.
     */
    public function doctor()
    {
        return $this->belongsTo(DoctorModel::class, 'doctor_id', 'Dr_Id');
    }
}
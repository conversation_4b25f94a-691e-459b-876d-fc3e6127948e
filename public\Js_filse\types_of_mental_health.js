
function toggleSection(sectionId) {
  const sections = document.querySelectorAll('.section');
  sections.forEach(section => {
  if (section.id === sectionId) {
  section.classList.toggle('active');
  const desc = section.querySelector('.description');
  const subDesc = section.querySelector('.sub-description');
  if (section.classList.contains('active')) {
  if (desc) desc.style.display = 'block';
  if (subDesc) subDesc.style.display = 'block';
  } else {
  if (desc) desc.style.display = 'none';
  if (subDesc) subDesc.style.display = 'none';
  }
  } else {
  section.classList.remove('active');
  const desc = section.querySelector('.description');
  const subDesc = section.querySelector('.sub-description');
  if (desc) desc.style.display = 'none';
  if (subDesc) subDesc.style.display = 'none';
  }
  });
  }
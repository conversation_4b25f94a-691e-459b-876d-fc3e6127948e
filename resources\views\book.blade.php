@extends('layouts/Main')

@section('title', $book->A_Title)

@section('contents')
<!-- Use the existing book view format -->
<div id="book_details">
    <div id="book_details_left">
        @if($book->A_Image)
            <img src="{{ asset('imagese/' . $book->A_Image) }}" alt="{{ $book->A_Title }}">
        @else
            <img src="{{ asset('imagese/default-book.jpg') }}" alt="Default Book Image">
        @endif
    </div>
    
    <div id="book_details_right">
        <h1>{{ $book->A_Title }}</h1>
        <p><span>{{ trans_static('لیکوال') }}:</span> {{ $book->A_Author }}</p>
        <p><span>{{ trans_static('کټګوري') }}:</span> {{ $book->A_catagory }}</p>
        <p><span>{{ trans_static('ژبه') }}:</span> {{ $book->A_Language }}</p>
        <p><span>{{ trans_static('خپریدو نیټه') }}:</span> {{ \Carbon\Carbon::parse($book->A_Publication_date)->format('Y-m-d') }}</p>

        @if($book->A_File)
            <div class="book-actions">
                <a href="{{ asset('imagese/' . $book->A_File) }}" class="download-btn" download>
                    <i class="fas fa-download"></i> {{ trans_static('ډاونلوډ') }}
                </a>
            </div>
        @endif
    </div>
</div>

@if($book->A_Description)
    <div id="book_description">
        <h2>{{ translateText('تفصیل') }}</h2>
        <div class="description-content">
            {!! $book->A_Description !!}
        </div>
    </div>
@endif

@if($book->A_File && pathinfo($book->A_File, PATHINFO_EXTENSION) == 'pdf')
    <div id="pdf_viewer">
        <h2>{{ translateText('آنلاین کتل') }}</h2>
        <iframe src="{{ asset('imagese/' . $book->A_File) }}" width="100%" height="600px"></iframe>
    </div>
@endif

<div class="back-button">
    <a href="{{ url()->previous() }}" class="back-btn">
        <i class="fas fa-arrow-right"></i> {{ translateText('شاته') }}
    </a>
</div>
@endsection
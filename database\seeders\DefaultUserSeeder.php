<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class DefaultUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if user with ID 2024 already exists
        if (!DB::table('users')->where('user_id', 2024)->exists()) {
            DB::table('users')->insert([
                'user_id' => 2024,
                'name' => '<PERSON>',
                'email' => 'mnazi<PERSON><EMAIL>',
                'password' => Hash::make('Reyankhan'),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}

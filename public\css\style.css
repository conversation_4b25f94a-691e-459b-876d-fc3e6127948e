/* Main Search Section Styles */
.search-section {
    background: linear-gradient(135deg, #4a90e2, #2c3e50);
    padding: 60px 0;
    text-align: center;
    margin-bottom: 40px;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.search-container {
    max-width: 800px;
    margin: 0 auto;
}

.search-title {
    color: white;
    font-size: 2.5rem;
    margin-bottom: 15px;
    font-weight: 700;
}

.search-subtitle {
    color: rgba(255,255,255,0.9);
    font-size: 1.2rem;
    margin-bottom: 30px;
}

.main-search-form {
    width: 100%;
}

.search-input-wrapper {
    display: flex;
    max-width: 700px;
    margin: 0 auto;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    border-radius: 50px;
    overflow: hidden;
}

.search-input-wrapper input {
    flex: 1;
    padding: 18px 25px;
    font-size: 1.1rem;
    border: none;
    outline: none;
    direction: rtl;
}

.search-button {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0 30px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background 0.3s;
}

.search-button:hover {
    background: #c0392b;
}

.search-examples {
    margin-top: 20px;
    color: white;
}

.search-examples span {
    margin-right: 10px;
    opacity: 0.8;
}

.search-examples a {
    color: white;
    margin: 0 8px;
    text-decoration: none;
    background: rgba(255,255,255,0.2);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: background 0.3s;
}

.search-examples a:hover {
    background: rgba(255,255,255,0.3);
}

/* Search Results Page Styles */
.search-results-container {
    max-width: 1200px;
    margin: 40px auto;
    padding: 20px;
}

.search-header {
    margin-bottom: 30px;
    text-align: center;
}

.search-header h1 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 20px;
}

.search-form {
    max-width: 600px;
    margin: 0 auto;
}

.search-input-container {
    display: flex;
    border: 2px solid #ddd;
    border-radius: 50px;
    overflow: hidden;
}

.search-input-container input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    outline: none;
    font-size: 1rem;
    direction: rtl;
}

.search-input-container button {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0 25px;
    cursor: pointer;
}

.no-results {
    text-align: center;
    padding: 50px 0;
    color: #666;
}

.no-results i {
    color: #ddd;
    margin-bottom: 20px;
}

.result-section {
    margin-bottom: 40px;
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.result-section h2 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

.result-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.result-item {
    display: flex;
    background: #f9f9f9;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.result-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.result-image {
    width: 100px;
    min-width: 100px;
    height: 150px;
    background: #eee;
    display: flex;
    align-items: center;
    justify-content: center;
}

.result-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: #e0e0e0;
    color: #999;
}

.no-image i {
    font-size: 2rem;
}

.result-content {
    padding: 15px;
    flex: 1;
}

.result-content h3 {
    font-size: 1.1rem;
    margin-bottom: 10px;
}

.result-content h3 a {
    color: #333;
    text-decoration: none;
}

.result-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 10px;
}

.result-description {
    font-size: 0.9rem;
    color: #555;
    margin-bottom: 15px;
    line-height: 1.5;
}

.result-actions {
    display: flex;
    gap: 10px;
}

.view-btn, .download-btn {
    padding: 5px 12px;
    border-radius: 4px;
    font-size: 0.8rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.view-btn {
    background: #3498db;
    color: white;
}

.download-btn {
    background: #2ecc71;
    color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .search-title {
        font-size: 2rem;
    }

    .search-input-wrapper {
        flex-direction: column;
        border-radius: 10px;
    }

    .search-button {
        padding: 15px;
    }

    .result-items {
        grid-template-columns: 1fr;
    }
}


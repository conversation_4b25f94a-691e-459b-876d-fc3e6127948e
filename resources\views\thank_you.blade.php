@extends('layouts/Main')

@section('title', trans_static('مننه'))

@section('contents')

<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto text-center">
        <div class="bg-green-50 border border-green-200 rounded-lg p-8 shadow-lg">
            <div class="mb-6">
                <svg class="mx-auto h-16 w-16 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            
            <h1 class="text-3xl font-bold text-green-800 mb-4">
                {{ trans_static('مننه!') }}
            </h1>

            <p class="text-lg text-green-700 mb-6">
                {{ trans_static('ستاسو ځوابونه بریالي وسپارل شول') }}. {{ trans_static('ستاسو پایلې د ډاکټر لخوا وڅېړل کیږي') }}.
            </p>

            @if($patientId)
            <div class="bg-white border border-green-200 rounded-lg p-4 mb-6">
                <p class="text-sm text-gray-600 mb-2">{{ trans_static('ستاسو د ناروغۍ شمیره:') }}</p>
                <p class="text-xl font-bold text-green-800">{{ $patientId }}</p>
            </div>
            @endif

            <div class="space-y-4">
                <a href="{{ route('QuestionS') }}" class="inline-block bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors duration-200">
                    {{ trans_static('بله ازموینه') }}
                </a>

                <a href="{{ url('/') }}" class="inline-block bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors duration-200 ml-4">
                    {{ trans_static('کور ته') }}
                </a>
            </div>
        </div>
    </div>
</div>

@endsection 
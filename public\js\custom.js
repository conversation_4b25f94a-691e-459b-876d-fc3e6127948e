// Filter toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    // Toggle filter card visibility
    const filterToggleBtn = document.getElementById('filterToggleBtn');
    const filterCard = document.getElementById('filterCard');
    
    if (filterToggleBtn && filterCard) {
        filterToggleBtn.addEventListener('click', function() {
            if (filterCard.classList.contains('hidden')) {
                filterCard.classList.remove('hidden');
                // Smooth scroll to the filter card
                filterCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
            } else {
                filterCard.classList.add('hidden');
            }
        });
    }
    
    // Show filter card if there are active filters
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('year') || urlParams.has('month')) {
        if (filterCard) {
            filterCard.classList.remove('hidden');
        }
    }
});
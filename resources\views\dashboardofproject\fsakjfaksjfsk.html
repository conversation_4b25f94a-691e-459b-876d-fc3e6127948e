<!DOCTYPE html>
<html lang="ps" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>د ډاکټرانو مدیریت - رواني روغتیا مرکز</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#6366f1',
                        accent: '#8b5cf6',
                        dark: '#1e293b',
                        light: '#f8fafc'
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Nastaliq+Urdu&display=swap');
        body {
            font-family: 'Noto Nastaliq <PERSON>', serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        }
        .card {
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            border-radius: 16px;
            overflow: hidden;
            border: 1px solid #e2e8f0;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .gradient-header {
            background: linear-gradient(90deg, #3b82f6 0%, #6366f1 100%);
        }
        .action-btn {
            transition: all 0.2s ease;
        }
        .action-btn:hover {
            transform: scale(1.1);
        }
        .file-upload {
            border: 2px dashed #cbd5e1;
            transition: all 0.3s ease;
            border-radius: 12px;
        }
        .file-upload:hover {
            border-color: #3b82f6;
            background-color: #f0f9ff;
        }
        .table-row:hover {
            background-color: #f8fafc;
        }
        .input-field {
            transition: all 0.3s ease;
            border: 2px solid #e2e8f0;
        }
        .input-field:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }
    </style>
</head>
<body class="min-h-screen p-4 md:p-8">
    <div class="max-w-7xl mx-auto">
        <!-- سرلیک -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-8">
            <div class="flex items-center mb-4 md:mb-0">
                <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-user-md text-white text-xl"></i>
                </div>
                <h1 class="text-2xl md:text-3xl font-bold text-gray-800 ml-4">د ډاکټرانو مدیریت</h1>
            </div>
            <div class="flex space-x-3">
                <button class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-1 transition-all duration-300">
                    <i class="fas fa-sync-alt mr-2"></i> تازه کول
                </button>
            </div>
        </div>

        <!-- خبرتیاوې -->
        <div class="space-y-4 mb-8">
            <div class="bg-green-50 border-l-4 border-green-500 text-green-700 p-4 rounded-lg shadow flex items-start">
                <div class="bg-green-500 rounded-full p-1 mr-3">
                    <i class="fas fa-check-circle text-white"></i>
                </div>
                <div>
                    <p class="font-medium">بریالیتوب!</p>
                    <p class="text-sm">ډاکټر په بریالیتوب سره ثبت شو.</p>
                </div>
            </div>
            
            <div class="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-lg shadow flex items-start">
                <div class="bg-red-500 rounded-full p-1 mr-3">
                    <i class="fas fa-exclamation-circle text-white"></i>
                </div>
                <div>
                    <p class="font-medium">تېروتنه!</p>
                    <ul class="list-disc list-inside text-sm">
                        <li>د ډاکټر نوم اړین دی</li>
                        <li>د تلیفون شمیره باید معتبره وي</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- د ډاکټر فورم -->
        <div class="card bg-white mb-8">
            <div class="gradient-header px-6 py-4">
                <h2 class="text-xl font-bold text-white flex items-center">
                    <i class="fas fa-user-plus mr-2"></i>
                    د ډاکټر ثبتولو فورم
                </h2>
            </div>
            <div class="p-6">
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-blue-600 transition-colors">د ډاکټر نوم</label>
                            <input type="text" class="w-full p-3 input-field rounded-lg focus:ring-2 focus:ring-blue-200 transition" placeholder="د ډاکټر بشپړ نوم"/>
                        </div>
                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-blue-600 transition-colors">د دفتر تلیفون شمیره</label>
                            <input type="text" class="w-full p-3 input-field rounded-lg focus:ring-2 focus:ring-blue-200 transition" placeholder="د دفتر تلیفون شمیره"/>
                        </div>
                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-blue-600 transition-colors">شخصي تلیفون شمیره</label>
                            <input type="text" class="w-full p-3 input-field rounded-lg focus:ring-2 focus:ring-blue-200 transition" placeholder="شخصي تلیفون شمیره"/>
                        </div>
                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-blue-600 transition-colors">هیواد</label>
                            <input type="text" class="w-full p-3 input-field rounded-lg focus:ring-2 focus:ring-blue-200 transition" placeholder="هیواد"/>
                        </div>
                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-blue-600 transition-colors">ولایت</label>
                            <input type="text" class="w-full p-3 input-field rounded-lg focus:ring-2 focus:ring-blue-200 transition" placeholder="ولایت"/>
                        </div>
                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-blue-600 transition-colors">ولسوالی</label>
                            <input type="text" class="w-full p-3 input-field rounded-lg focus:ring-2 focus:ring-blue-200 transition" placeholder="ولسوالی"/>
                        </div>
                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-blue-600 transition-colors">کلي/سیمه</label>
                            <input type="text" class="w-full p-3 input-field rounded-lg focus:ring-2 focus:ring-blue-200 transition" placeholder="کلي یا سیمه"/>
                        </div>
                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-blue-600 transition-colors">د ډاکټر انځور</label>
                            <div class="file-upload rounded-lg p-4 text-center cursor-pointer">
                                <input type="file" id="doctorImage" accept="image/*" class="hidden">
                                <label for="doctorImage" class="flex flex-col items-center justify-center">
                                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                    <span class="text-sm text-gray-500">انځور انتخاب کړئ</span>
                                    <span class="text-xs text-gray-400 mt-1">JPG, PNG یا GIF</span>
                                </label>
                            </div>
                            <div class="mt-4 flex items-center justify-center">
                                <div class="relative">
                                    <img src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=200&h=200&q=80" alt="د ډاکټر انځور" class="w-24 h-24 rounded-full object-cover border-4 border-white shadow">
                                    <button class="absolute bottom-0 right-0 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center shadow action-btn">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-center space-x-4 mt-8">
                        <button type="submit" class="px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:-translate-y-1 shadow-md hover:shadow-lg flex items-center">
                            <i class="fas fa-save mr-2"></i> ثبت کړئ
                        </button>
                        <button type="reset" class="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all duration-300 transform hover:-translate-y-1 shadow-md hover:shadow-lg flex items-center">
                            <i class="fas fa-times mr-2"></i> پاک کړئ
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- د ډاکټر لټون فورم -->
        <div class="card bg-white mb-8">
            <div class="p-4">
                <form class="flex items-center">
                    <div class="relative flex-grow">
                        <input type="text" placeholder="د ډاکټر لټون..." class="w-full p-3 input-field rounded-lg focus:ring-2 focus:ring-blue-200 transition pr-12">
                        <button class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <button type="submit" class="ml-2 px-4 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition flex items-center">
                        <i class="fas fa-filter mr-2"></i> فلټر
                    </button>
                </form>
            </div>
        </div>

        <!-- ثبت شوي ډاکټران -->
        <div class="card bg-white">
            <div class="gradient-header px-6 py-4">
                <h2 class="text-xl font-bold text-white flex items-center">
                    <i class="fas fa-users mr-2"></i>
                    ثبت شوي ډاکټران
                </h2>
            </div>
            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-blue-50">
                            <tr>
                                <th class="p-3 text-right">نوم</th>
                                <th class="p-3">دفتر شمیره</th>
                                <th class="p-3">شخصي شمیره</th>
                                <th class="p-3">هیواد</th>
                                <th class="p-3">ولایت</th>
                                <th class="p-3">ولسوالی</th>
                                <th class="p-3">کلي/سیمه</th>
                                <th class="p-3">انځور</th>
                                <th class="p-3">عملیې</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="table-row border-b border-gray-200">
                                <td class="p-3">ډاکټر احمد شاه</td>
                                <td class="p-3">0780123456</td>
                                <td class="p-3">0701234567</td>
                                <td class="p-3">افغانستان</td>
                                <td class="p-3">کندهار</td>
                                <td class="p-3">ډنډ</td>
                                <td class="p-3">سرهٔ پل</td>
                                <td class="p-3">
                                    <img src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=50&h=50&q=80" alt="ډاکټر احمد شاه" class="w-10 h-10 rounded-full object-cover">
                                </td>
                                <td class="p-3">
                                    <div class="flex space-x-2">
                                        <button class="action-btn text-blue-500 hover:text-blue-700">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn text-red-500 hover:text-red-700">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="table-row border-b border-gray-200">
                                <td class="p-3">ډاکټره زهرا احمدي</td>
                                <td class="p-3">0780223344</td>
                                <td class="p-3">0702233445</td>
                                <td class="p-3">افغانستان</td>
                                <td class="p-3">کابل</td>
                                <td class="p-3">ناحيه ۱۰</td>
                                <td class="p-3">کارته سخی</td>
                                <td class="p-3">
                                    <div class="bg-gray-200 border-2 border-dashed rounded-xl w-10 h-10 flex items-center justify-center text-gray-400">
                                        <i class="fas fa-user"></i>
                                    </div>
                                </td>
                                <td class="p-3">
                                    <div class="flex space-x-2">
                                        <button class="action-btn text-blue-500 hover:text-blue-700">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn text-red-500 hover:text-red-700">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="table-row">
                                <td class="p-3">ډاکټر نادر محمدي</td>
                                <td class="p-3">0780334455</td>
                                <td class="p-3">0703344556</td>
                                <td class="p-3">افغانستان</td>
                                <td class="p-3">هرات</td>
                                <td class="p-3">انډی خیل</td>
                                <td class="p-3">پل مالان</td>
                                <td class="p-3">
                                    <img src="https://images.unsplash.com/photo-1622253692010-333f2da6031d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=50&h=50&q=80" alt="ډاکټر نادر محمدي" class="w-10 h-10 rounded-full object-cover">
                                </td>
                                <td class="p-3">
                                    <div class="flex space-x-2">
                                        <button class="action-btn text-blue-500 hover:text-blue-700">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="action-btn text-red-500 hover:text-red-700">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- پاڼې اړونده کنټرول -->
                <div class="mt-6 flex items-center justify-between">
                    <div class="text-sm text-gray-600">
                        نمایش 1-3 له 12 څخه
                    </div>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded">1</button>
                        <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">2</button>
                        <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">3</button>
                        <span class="px-2 py-1">...</span>
                        <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">5</button>
                        <button class="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // د فایل اپلوډ لپاره تعامل
        document.querySelectorAll('.file-upload').forEach(uploadArea => {
            const input = uploadArea.querySelector('input[type="file"]');
            const label = uploadArea.querySelector('label');
            
            input.addEventListener('change', function(e) {
                if (this.files && this.files.length > 0) {
                    label.innerHTML = `
                        <i class="fas fa-check-circle text-green-500 text-3xl mb-2"></i>
                        <span class="text-sm text-gray-700">${this.files[0].name}</span>
                    `;
                }
            });
            
            // د drag and drop ملاتړ
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('border-blue-500', 'bg-blue-50');
            });
            
            uploadArea.addEventListener('dragleave', function() {
                this.classList.remove('border-blue-500', 'bg-blue-50');
            });
            
            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('border-blue-500', 'bg-blue-50');
                
                if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                    input.files = e.dataTransfer.files;
                    
                    const event = new Event('change', { bubbles: true });
                    input.dispatchEvent(event);
                }
            });
        });
    </script>
</body>
</html>
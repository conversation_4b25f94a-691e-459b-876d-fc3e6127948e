
  function showError(input, message) {
    let error = input.nextElementSibling;
    if (!error || !error.classList.contains('error-msg')) {
      error = document.createElement('div');
      error.classList.add('error-msg');
      input.insertAdjacentElement('afterend', error);
    }
    error.textContent = message;
  }

  function clearErrors() {
    document.querySelectorAll('.error-msg').forEach(e => e.remove());
  }

  document.getElementById("namePhoneForm").addEventListener("submit", function (e) {
    clearErrors();

    const name = document.getElementById("name");
    const phone = document.getElementById("phone");
    let valid = true;

    const nameVal = name.value.trim();
    const phoneVal = phone.value.trim();

    // نوم چک – تر ۲۵ حروفو کم، یوازې حروف (هیڅ عدد یا سمبول نه وي)
    if (nameVal === "" || !/^[\u0600-\u06FF\s]{1,25}$/.test(nameVal)) {
      showError(name, "نوم باید له حروفو جوړ وي او تر ۲۵ حروفو کم وي");
      valid = false;
    }

    // موبایل چک – له 07 شروع او دقیقاً 10 عدد
    if (!/^07\d{8}$/.test(phoneVal)) {
      showError(phone, "موبایل باید له 07 پیل شي او ۱۰ عدده وي");
      valid = false;
    }

    if (!valid) e.preventDefault();
    else alert("معلومات بریالي ولیږل شول!");
  });
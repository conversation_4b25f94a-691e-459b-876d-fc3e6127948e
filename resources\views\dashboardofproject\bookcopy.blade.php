@extends('layouts/Admin')

@section('title','Books Management')

@section('contents')
<!-- Main Content -->
<div class="p-4 md:p-8 min-h-screen transition-all w-full bg-white">



    <!-- Page Header -->
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
        <div class="flex items-center mb-4 md:mb-0">
            <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300">
                <i class="fas fa-book text-gray-700 text-xl"></i>
            </div>
            <h1 class="text-2xl md:text-3xl font-bold text-gray-800 ml-4">{{ translateText('د کتابونو مدیریت') }}</h1>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('bookd.index') }}" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                <i class="fas fa-sync-alt mr-2"></i> {{ translateText('تازه کول') }}
            </a>
            <button type="button" id="filterToggleBtn" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                <i class="fas fa-filter mr-2"></i> {{ translateText('فلټر') }}
            </button>
        </div>
    </div>

    <!-- Filter Card - Hidden by default -->
    <div id="filterCard" class="bg-white rounded-2xl shadow-xl mb-8 border-0 overflow-hidden transform transition-all duration-300 hover:shadow-2xl hidden">
        <div class="bg-gray-200 px-6 py-4">
            <h2 class="text-xl md:text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-filter mr-2"></i> {{ translateText('د کتابونو فلټر کول') }}
            </h2>
        </div>

        <div class="p-6">
            <form action="{{ route('books.filter') }}" method="GET" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د محتوا ډول') }}</label>
                        <select name="content_type" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                            <option value="">{{ translateText('ټول') }}</option>
                            <option value="کتاب" {{ request('content_type') == 'کتاب' ? 'selected' : '' }}>{{ translateText('کتاب') }}</option>
                            <option value="مقالي" {{ request('content_type') == 'مقالي' ? 'selected' : '' }}>{{ translateText('مقالي') }}</option>
                            <option value="پالیسي" {{ request('content_type') == 'پالیسي' ? 'selected' : '' }}>{{ translateText('پالیسي') }}</option>
                        </select>
                    </div>
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د کتاب ډول') }}</label>
                        <select name="book_type" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                            <option value="">{{ translateText('ټول ډولونه') }}</option>
                            <option value="academic" {{ request('book_type') == 'academic' ? 'selected' : '' }}>{{ translateText('علمي') }}</option>
                            <option value="research" {{ request('book_type') == 'research' ? 'selected' : '' }}>{{ translateText('څیړنیز') }}</option>
                            <option value="literature" {{ request('book_type') == 'literature' ? 'selected' : '' }}>{{ translateText('ادبي') }}</option>
                        </select>
                    </div>
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('ژبه') }}</label>
                        <select name="language" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                            <option value="">{{ translateText('ټولې ژبې') }}</option>
                            <option value="pashto" {{ request('language') == 'pashto' ? 'selected' : '' }}>{{ translateText('پښتو') }}</option>
                            <option value="dari" {{ request('language') == 'dari' ? 'selected' : '' }}>{{ translateText('دري') }}</option>
                            <option value="english" {{ request('language') == 'english' ? 'selected' : '' }}>{{ translateText('انګلیسي') }}</option>
                        </select>
                    </div>
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د خپرېدو کال') }}</label>
                        <input type="number" name="publication_year" placeholder="{{ translateText('کال') }}" value="{{ request('publication_year') }}"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                    </div>
                </div>
                <div class="flex space-x-2 pt-4">
                    <button type="submit"
                        class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                        <i class="fas fa-filter mr-2"></i> {{ translateText('فلټر کول') }}
                    </button>
                    <a href="{{ route('bookd.index') }}"
                        class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                        <i class="fas fa-undo mr-2"></i> {{ translateText('بیا تنظیمول') }}
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Success Message -->
    @if(session('success'))
    <div class="bg-green-100 border-r-4 border-green-500 text-green-700 p-4 mb-6 rounded-lg shadow-md flex items-center justify-between" role="alert">
        <div class="flex items-center">
            <div class="bg-green-500 rounded-full p-1 mr-3">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <p class="font-medium">{{ session('success') }}</p>
        </div>
        <button onclick="this.parentElement.remove()" class="text-green-700 hover:text-green-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-lg shadow-md flex items-center justify-between" role="alert">
        <div class="flex items-center">
            <div class="bg-red-500 rounded-full p-1 mr-3">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="font-medium">{{ session('error') }}</p>
        </div>
        <button class="text-red-700 hover:text-red-900">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>
    @endif

    @if($errors->any())
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-lg shadow-md" role="alert">
        <div class="flex items-center mb-2">
            <div class="bg-red-500 rounded-full p-1 mr-3">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <p class="font-medium">{{ translateText('تېروتنې') }}</p>
        </div>
        <ul class="list-disc list-inside pl-6">
            @foreach($errors->all() as $error)
            <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif

    <!-- Form Card -->
    <div class="bg-white rounded-2xl shadow-xl mb-8 border-0 overflow-hidden transform transition-all duration-300 hover:shadow-2xl">
        <div class="bg-gray-200 px-6 py-4">
            <h2 class="text-xl md:text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-plus-circle mr-2"></i> {{ translateText('د کتابونو د ثبتولو فورم') }}
            </h2>
        </div>

        <div class="p-6">
            <form id="contentForm" action="{{ isset($editBook) ? route('bookd.update', $editBook->A_Id) : route('bookd.store') }}" method="POST" enctype="multipart/form-data" class="space-y-4">
                @csrf
                @if(isset($editBook))
                    @method('PUT')
                @endif

                <!-- Add hidden field for type -->
                <input type="hidden" id="hiddenType" name="hidden_type" value="{{ isset($editBook) ? $editBook->A_Type : 'کتاب' }}">

                <!-- Add this hidden field to store the redirect URL -->
                <input type="hidden" id="redirectUrl" name="redirect_url" value="">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('عنوان') }}</label>
                        <input type="text" id="title" name="title" value="{{ isset($editBook) ? $editBook->A_Title : old('title') }}"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300" />
                    </div>
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('لیکوال') }}</label>
                        <input type="text" id="author" name="author" value="{{ isset($editBook) ? $editBook->A_Author : old('author') }}"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300" />
                    </div>
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د خپریدو تاریخ') }}</label>
                        <input type="date" id="publishDate" name="publishdate" value="{{ isset($editBook) ? $editBook->A_Publication_date : old('publishdate') }}"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300" />
                    </div>
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('ژبه') }}</label>
                        <input type="text" id="language" name="language" value="{{ isset($editBook) ? $editBook->A_Language : old('language') }}"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300" />
                    </div>
                    <div class="group relative">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('نوع') }}</label>
                        <select id="contentType" name="type" onchange="updateRedirectUrl()" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                            <option value="کتاب" {{ (isset($editBook) && $editBook->A_Type == 'کتاب') ? 'selected' : '' }}>{{ translateText('کتاب') }}</option>
                            <option value="مقالي" {{ (isset($editBook) && $editBook->A_Type == 'مقالي') ? 'selected' : '' }}>{{ translateText('مقالي') }}</option>
                            <option value="پالیسي" {{ (isset($editBook) && $editBook->A_Type == 'پالیسي') ? 'selected' : '' }}>{{ translateText('پالیسي') }}</option>
                        </select>
                    </div>
                    <div class="group relative">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('کتګوري') }}</label>
                        <select id="category" name="catagory"
                            class="w-full p-3 border border-gray-300 rounded-lg appearance-none bg-white focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                            <option value="رواني روغتیا" {{ (isset($editBook) && $editBook->A_catagory == 'رواني روغتیا') ? 'selected' : '' }}>{{ translateText('رواني روغتیا') }}</option>
                            <option value="درملنه او مشورې" {{ (isset($editBook) && $editBook->A_catagory == 'درملنه او مشورې') ? 'selected' : '' }}>{{ translateText('درملنه او مشورې') }}</option>
                            <option value="ذهني سکون" {{ (isset($editBook) && $editBook->A_catagory == 'ذهني سکون') ? 'selected' : '' }}>{{ translateText('ذهني سکون') }}</option>
                            <option value="فشار او خپګان" {{ (isset($editBook) && $editBook->A_catagory == 'فشار او خپګان') ? 'selected' : '' }}>{{ translateText('فشار او خپګان') }}</option>
                            <option value="مثبت فکر" {{ (isset($editBook) && $editBook->A_catagory == 'مثبت فکر') ? 'selected' : '' }}>{{ translateText('مثبت فکر') }}</option>
                            <option value="اسلامي لارښوونې" {{ (isset($editBook) && $editBook->A_catagory == 'اسلامي لارښوونې') ? 'selected' : '' }}>{{ translateText('اسلامي لارښوونې') }}</option>
                            <option value="اضطراب او اندېښنه" {{ (isset($editBook) && $editBook->A_catagory == 'اضطراب او اندېښنه') ? 'selected' : '' }}>{{ translateText('اضطراب او اندېښنه') }}</option>
                            <option value="احساسات او عواطف" {{ (isset($editBook) && $editBook->A_catagory == 'احساسات او عواطف') ? 'selected' : '' }}>{{ translateText('احساسات او عواطف') }}</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center px-3 pt-6">
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                    </div>

                    <!-- Description -->
                    <div class="group col-span-2">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('تفصیل') }}</label>
                        <textarea name="description" rows="4"
                            class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300"
                            placeholder="{{ translateText('د کتاب تفصیل دلته ولیکئ...') }}">{{ isset($editBook) ? $editBook->A_Description : old('description') }}</textarea>
                    </div>

                    <!-- Book Cover Image Upload -->
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د کتاب عکس') }}</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-500 transition-colors">
                            <input type="file" id="image" name="image" accept="image/*" class="hidden" />
                            <label for="image" class="cursor-pointer flex flex-col items-center justify-center">
                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                <span class="text-sm text-gray-500">{{ translateText('د کتاب عکس انتخاب کړئ') }}</span>
                                <span class="text-xs text-gray-400 mt-1">JPG, PNG یا GIF</span>
                            </label>
                        </div>
                        @if(isset($editBook) && $editBook->A_Image)
                            <div class="mt-2 flex items-center">
                                <img src="{{ asset('imagese/' . $editBook->A_Image) }}" alt="{{ translateText('Current Image') }}" class="h-16 w-16 object-cover rounded-md border border-gray-200">
                                <span class="ml-2 text-sm text-gray-600">{{ $editBook->A_Image }}</span>
                            </div>
                        @endif
                    </div>

                    <!-- Author Image Upload -->
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د لیکوال عکس') }}</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-500 transition-colors">
                            <input type="file" id="author_image" name="author_image" accept="image/*" class="hidden" />
                            <label for="author_image" class="cursor-pointer flex flex-col items-center justify-center">
                                <i class="fas fa-user-circle text-3xl text-gray-400 mb-2"></i>
                                <span class="text-sm text-gray-500">{{ translateText('د لیکوال عکس انتخاب کړئ') }}</span>
                                <span class="text-xs text-gray-400 mt-1">JPG, PNG یا GIF</span>
                            </label>
                        </div>
                        @if(isset($editBook) && $editBook->A_Author_Image)
                            <div class="mt-2 flex items-center">
                                <img src="{{ asset('imagese/' . $editBook->A_Author_Image) }}" alt="{{ translateText('Author Image') }}" class="h-16 w-16 object-cover rounded-full border border-gray-200">
                                <span class="ml-2 text-sm text-gray-600">{{ $editBook->A_Author_Image }}</span>
                            </div>
                        @endif
                    </div>

                    <!-- File Upload -->
                    <div class="group">
                        <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('فایل (PDF یا سند)') }}</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-500 transition-colors">
                            <input type="file" id="file" name="file" accept=".pdf,.doc,.docx" class="hidden" />
                            <label for="file" class="cursor-pointer flex flex-col items-center justify-center">
                                <i class="fas fa-file-upload text-3xl text-gray-400 mb-2"></i>
                                <span class="text-sm text-gray-500">{{ translateText('فایل انتخاب کړئ') }}</span>
                                <span class="text-xs text-gray-400 mt-1">PDF, DOC یا DOCX</span>
                            </label>
                        </div>
                        @if(isset($editBook) && $editBook->A_File)
                            <div class="mt-2 flex items-center">
                                <i class="fas fa-file-pdf text-red-500 text-2xl"></i>
                                <div class="ml-2">
                                    <span class="text-sm text-gray-600">{{ $editBook->A_File }}</span>
                                    <a href="{{ asset('imagese/' . $editBook->A_File) }}" target="_blank" class="block text-xs text-blue-600 hover:underline">{{ translateText('View current file') }}</a>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <div class="flex justify-start space-x-4">
                    <button type="submit" class="px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                        <i class="fas fa-save mr-2"></i> {{ isset($editBook) ? translateText('تازه کول') : translateText('ثبتول') }}
                    </button>

                    @if(isset($editBook))
                        <a href="{{ route('bookd.index') }}" class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg text-center border-0">
                            <i class="fas fa-times mr-2"></i> {{ translateText('لغوه کول') }}
                        </a>
                    @endif
                </div>
            </form>
        </div>
    </div>

    <!-- Content Tables -->
    <div class="space-y-8">
        <!-- کتابونه -->
        <div class="bg-white rounded-2xl shadow-xl p-6 border-0">
            <h3 class="text-xl font-bold mb-4 text-gray-800 border-b-2 border-gray-300 pb-2 flex items-center">
                <i class="fas fa-book mr-2 text-gray-600"></i> {{ translateText('کتابونه') }}
            </h3>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-white text-gray-800 border border-gray-800">
                        <tr>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('عنوان') }}</th>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('لیکوال') }}</th>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('ژبه') }}</th>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('تاریخ') }}</th>
                            <th class="p-3 text-right">{{ translateText('عملیې') }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-800">
                        @if(isset($books) && count($books) > 0)
                        @foreach($books as $book)
                        <tr class="border-b hover:bg-gray-100">
                            <td class="p-3 border-r border-gray-200">{{$book->A_Title}}</td>
                            <td class="p-3 border-r border-gray-200">{{$book->A_Author}}</td>
                            <td class="p-3 border-r border-gray-200">{{$book->A_Language}}</td>
                            <td class="p-3 border-r border-gray-200">{{$book->A_Publication_date}}</td>
                            <td class="p-3">
                                <div class="flex justify-end space-x-2">
                                    <a href="{{ route('book.edit', $book->A_Id) }}"
                                        class="p-1 bg-white text-gray-700 rounded shadow-sm hover:bg-gray-200 hover:text-black transition-all duration-300 mr-2 border-0">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('book.destroy', $book->A_Id) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                            class="p-1 bg-white text-gray-700 rounded shadow-sm hover:bg-gray-200 hover:text-black transition-all duration-300 border-0"
                                            onclick="return confirm('{{ translateText('آیا تاسو ډاډه یاست چې دا کتاب غواړئ لرې کړئ؟') }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                        @else
                        <tr>
                            <td colspan="5" class="p-3 text-center text-gray-500">{{ translateText('No books found') }}</td>
                        </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>

        <!-- مقالي -->
        <div class="bg-white rounded-2xl shadow-xl p-6 border-0">
            <h3 class="text-xl font-bold mb-4 text-gray-800 border-b-2 border-gray-300 pb-2 flex items-center">
                <i class="fas fa-file-alt mr-2 text-gray-600"></i> {{ translateText('مقالي') }}
            </h3>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-white text-gray-800 border border-gray-800">
                        <tr>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('عنوان') }}</th>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('لیکوال') }}</th>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('ژبه') }}</th>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('تاریخ') }}</th>
                            <th class="p-3 text-right">{{ translateText('عملیې') }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-800">
                        @if(isset($papers) && count($papers) > 0)
                        @foreach($papers as $book)
                        <tr class="border-b hover:bg-gray-100">
                            <td class="p-3 border-r border-gray-200">{{$book->A_Title}}</td>
                            <td class="p-3 border-r border-gray-200">{{$book->A_Author}}</td>
                            <td class="p-3 border-r border-gray-200">{{$book->A_Language}}</td>
                            <td class="p-3 border-r border-gray-200">{{$book->A_Publication_date}}</td>
                            <td class="p-3">
                                <div class="flex justify-end space-x-2">
                                    <a href="{{ route('book.edit', $book->A_Id) }}"
                                        class="p-1 bg-white text-gray-700 rounded shadow-sm hover:bg-gray-200 hover:text-black transition-all duration-300 mr-2 border-0">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('book.destroy', $book->A_Id) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                            class="p-1 bg-white text-gray-700 rounded shadow-sm hover:bg-gray-200 hover:text-black transition-all duration-300 border-0"
                                            onclick="return confirm('{{ translateText('آیا تاسو ډاډه یاست چې دا مقالي غواړئ لرې کړئ؟') }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                        @else
                        <tr>
                            <td colspan="5" class="p-3 text-center text-gray-500">{{ translateText('No books found') }}</td>
                        </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>

        <!-- پالیسي -->
        <div class="bg-white rounded-2xl shadow-xl p-6 border-0">
            <h3 class="text-xl font-bold mb-4 text-gray-800 border-b-2 border-gray-300 pb-2 flex items-center">
                <i class="fas fa-clipboard-list mr-2 text-gray-600"></i> {{ translateText('پالیسي') }}
            </h3>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-white text-gray-800 border border-gray-800">
                        <tr>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('عنوان') }}</th>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('لیکوال') }}</th>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('ژبه') }}</th>
                            <th class="p-3 text-right border-r border-gray-800">{{ translateText('تاریخ') }}</th>
                            <th class="p-3 text-right">{{ translateText('عملیې') }}</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-800">
                        @if(isset($policy) && count($policy) > 0)
                        @foreach($policy as $book)
                        <tr class="border-b hover:bg-gray-100">
                            <td class="p-3 border-r border-gray-200">{{$book->A_Title}}</td>
                            <td class="p-3 border-r border-gray-200">{{$book->A_Author}}</td>
                            <td class="p-3 border-r border-gray-200">{{$book->A_Language}}</td>
                            <td class="p-3 border-r border-gray-200">{{$book->A_Publication_date}}</td>
                            <td class="p-3">
                                <div class="flex justify-end space-x-2">
                                    <a href="{{ route('book.edit', $book->A_Id) }}"
                                        class="p-1 bg-white text-gray-700 rounded shadow-sm hover:bg-gray-200 hover:text-black transition-all duration-300 mr-2 border-0">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="{{ route('book.destroy', $book->A_Id) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                            class="p-1 bg-white text-gray-700 rounded shadow-sm hover:bg-gray-200 hover:text-black transition-all duration-300 border-0"
                                            onclick="return confirm('{{ translateText('آیا تاسو ډاډه یاست چې دا پالیسی غواړئ لرې کړئ؟') }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        @endforeach
                        @else
                        <tr>
                            <td colspan="5" class="p-3 text-center text-gray-500">{{ translateText('No books found') }}</td>
                        </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection

<script>
function updateRedirectUrl() {
    const contentType = document.getElementById('contentType').value;

    // Set a hidden input field with the content type
    const typeField = document.getElementById('hiddenType');
    if (typeField) {
        typeField.value = contentType;
    }

    // Update form action based on content type
    const form = document.getElementById('contentForm');
    if (form) {
        if (contentType === 'کتاب') {
            form.action = "{{ isset($editBook) ? route('bookd.update', isset($editBook) ? $editBook->A_Id : '') : route('bookd.store') }}";
        } else if (contentType === 'مقالي') {
            form.action = "{{ isset($editBook) ? route('papers.update', isset($editBook) ? $editBook->A_Id : '') : route('bookd.store') }}";
        } else if (contentType === 'پالیسي') {
            form.action = "{{ isset($editBook) ? route('policy.update', isset($editBook) ? $editBook->A_Id : '') : route('bookd.store') }}";
        }
    }
}

// Run on page load
document.addEventListener('DOMContentLoaded', function() {
    updateRedirectUrl();

    // Filter toggle functionality
    const filterToggleBtn = document.getElementById('filterToggleBtn');
    const filterCard = document.getElementById('filterCard');

    if (filterToggleBtn && filterCard) {
        filterToggleBtn.addEventListener('click', function() {
            if (filterCard.classList.contains('hidden')) {
                filterCard.classList.remove('hidden');
                filterToggleBtn.innerHTML = '<i class="fas fa-times mr-2"></i> {{ translateText("فلټر بندول") }}';
                filterToggleBtn.classList.add('bg-red-100', 'text-red-700');
                filterToggleBtn.classList.remove('bg-white', 'text-gray-700');
            } else {
                filterCard.classList.add('hidden');
                filterToggleBtn.innerHTML = '<i class="fas fa-filter mr-2"></i> {{ translateText("فلټر") }}';
                filterToggleBtn.classList.remove('bg-red-100', 'text-red-700');
                filterToggleBtn.classList.add('bg-white', 'text-gray-700');
            }
        });
    }
});
</script>






























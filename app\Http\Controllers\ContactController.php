<?php

namespace App\Http\Controllers;

use App\Models\ContactUsModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    /**
     * Store a newly created contact message in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validate the request with proper field names and validation rules
        $validator = Validator::make($request->all(), [
            'fname' => [
                'required',
                'string',
                'max:20',
                function ($attribute, $value, $fail) {
                    // Check if the name contains only Pashto/Dari characters and spaces
                    if (!preg_match('/^[\p{Arabic}\s]+$/u', $value)) {
                        $fail('نوم باید یوازې توري وي او تر ۲۰ څخه کم');
                    }
                }
            ],
            'lname' => [
                'required',
                'string',
                'max:20',
                function ($attribute, $value, $fail) {
                    // Check if the last name contains only Pashto/Dari characters and spaces
                    if (!preg_match('/^[\p{Arabic}\s]+$/u', $value)) {
                        $fail('تخلص باید یوازې توري وي او تر ۲۰ څخه کم');
                    }
                }
            ],
            'email' => 'required|email|max:255',
            'phone' => [
                'required',
                'string',
                'regex:/^07\d{8}$/' // Must start with 07 and be exactly 10 digits
            ],
            'message' => 'required|string|max:1000',
        ], [
            'fname.required' => 'نوم لازمي دی',
            'fname.max' => 'نوم باید تر ۲۰ حروفو کم وي',
            'lname.required' => 'تخلص لازمي دی',
            'lname.max' => 'تخلص باید تر ۲۰ حروفو کم وي',
            'email.required' => 'برېښنالیک لازمي دی',
            'email.email' => 'برېښنالیک سم نه دی',
            'phone.required' => 'شمېره لازمي ده',
            'phone.regex' => 'شمېره باید له 07 پیل شي او ۱۰ عدده وي',
            'message.required' => 'پیغام لازمي دی',
            'message.max' => 'پیغام باید تر ۱۰۰۰ حروفو کم وي',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            // Create contact message with all form fields
            $contact = ContactUsModel::create([
                'fname' => $request->fname,
                'lname' => $request->lname,
                'email' => $request->email,
                'phone' => $request->phone,
                'message' => $request->message,
                'User_id' => Auth::check() ? Auth::id() : null
            ]);

            return redirect()->back()->with('success', 'ستاسو پیغام په بریالیتوب سره ولیږل شو!');
        } catch (\Exception $e) {
            \Log::error('Error storing contact message: ' . $e->getMessage());

            return redirect()->back()
                ->with('error', 'تېروتنه: ' . $e->getMessage())
                ->withInput();
        }
    }
}

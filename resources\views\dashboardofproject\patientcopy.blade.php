@extends('layouts/Admin')
@section('title','Patient Copy')
@section('contents')

<div class="p-4 md:p-8 min-h-screen transition-all w-full bg-white">
    <div class="container mx-auto max-w-6xl">
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-8">
            <div class="flex items-center mb-4 md:mb-0">
                <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-user-injured text-gray-700 text-xl"></i>
                </div>
                <h1 class="text-2xl md:text-3xl font-bold text-gray-800 mr-4">{{ translateText('د ناروغانو مدیریت') }}</h1>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('patient.report') }}" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                    <i class="fas fa-chart-bar mr-2"></i>
                    {{ translateText('راپور') }}
                </a>
                <button onclick="window.location.reload()" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                    <i class="fas fa-sync-alt mr-2"></i>
                    {{ translateText('تازه کول') }}
                </button>
               <div class="flex space-x-3">
                    <button type="button" id="filterToggleBtn" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                        <i class="fas fa-filter mr-2"></i> {{ translateText('فلټر') }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Alert Messages -->
        @if(session('success'))
            <div class="bg-green-100 border-r-4 border-green-500 text-green-700 p-4 mb-6 rounded-md">
                <p class="font-bold">بریالیتوب!</p>
                <p>{{ session('success') }}</p>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-md">
                <p class="font-bold">تېروتنه!</p>
                <p>{{ session('error') }}</p>
            </div>
        @endif

        <!-- Patient Update Form (Hidden by default) -->
        <div id="patientUpdateForm" class="mt-8 hidden bg-white rounded-2xl shadow-xl p-6 border-0 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-800 border-b-2 border-gray-300 pb-2 flex items-center">
                    <i class="fas fa-user-edit mr-2 text-gray-600"></i>
                    د ناروغ معلومات تازه کول
                </h3>
                <button type="button" onclick="hideUpdateForm()" class="text-gray-500 hover:text-red-500 border-0">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="updatePatientForm" method="POST">
                @csrf
                @method('PUT')
                <input type="hidden" id="patient_id" name="patient_id">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="mb-4">
                        <label for="patient_name" class="block text-gray-700 font-medium mb-2">نوم</label>
                        <input type="text" id="patient_name" name="Patiet_Name" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500" required>
                    </div>

                    <div class="mb-4">
                        <label for="patient_age" class="block text-gray-700 font-medium mb-2">عمر</label>
                        <input type="number" id="patient_age" name="Patient_Age" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500" required>
                    </div>

                    <div class="mb-4">
                        <label for="patient_gender" class="block text-gray-700 font-medium mb-2">جنسیت</label>
                        <select id="patient_gender" name="Patient_Gender" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500" required>
                            <option value="نارینه">نارینه</option>
                            <option value="ښځینه">ښځینه</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="patient_phone" class="block text-gray-700 font-medium mb-2">د اړیکې شمېره</label>
                        <input type="text" id="patient_phone" name="Patient_phone" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500">
                    </div>

                    <div class="mb-4">
                        <label for="patient_email" class="block text-gray-700 font-medium mb-2">برېښنالیک</label>
                        <input type="email" id="patient_email" name="Patient_email" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500">
                    </div>

                    <div class="mb-4">
                        <label for="patient_province" class="block text-gray-700 font-medium mb-2">ولایت</label>
                        <input type="text" id="patient_province" name="P_Province" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500" required>
                    </div>

                    <div class="mb-4">
                        <label for="patient_district" class="block text-gray-700 font-medium mb-2">ولسوالي</label>
                        <input type="text" id="patient_district" name="P_Distract" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500" required>
                    </div>

                    <div class="mb-4">
                        <label for="patient_village" class="block text-gray-700 font-medium mb-2">کلی</label>
                        <input type="text" id="patient_village" name="P_Village" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500" required>
                    </div>
                </div>
                <div class="mt-6">
                    <button type="submit" class="px-6 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black shadow-md transition-all duration-300 border-0">
                        تازه کول
                    </button>
                </div>
            </form>
        </div>

        <!-- Filter Card - Hidden by default -->
        <div id="filterCard" class="bg-white rounded-2xl shadow-xl mb-8 border-0 overflow-hidden transform transition-all duration-300 hover:shadow-2xl hidden">
            <div class="bg-gray-200 px-6 py-4">
                <h2 class="text-xl md:text-2xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-filter mr-2"></i> {{ translateText('د ناروغانو فلټر کول') }}
                </h2>
            </div>

            <div class="p-6">
                <form action="/patient-filter" method="GET" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('جنسیت') }}</label>
                            <select name="gender" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                                <option value="">{{ translateText('ټول') }}</option>
                                <option value="نارینه" {{ request('gender') == 'نارینه' ? 'selected' : '' }}>{{ translateText('نارینه') }}</option>
                                <option value="ښځینه" {{ request('gender') == 'ښځینه' ? 'selected' : '' }}>{{ translateText('ښځینه') }}</option>
                                <option value="Male" {{ request('gender') == 'Male' ? 'selected' : '' }}>Male</option>
                                <option value="Female" {{ request('gender') == 'Female' ? 'selected' : '' }}>Female</option>
                            </select>
                        </div>

                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د عمر څانګه') }}</label>
                            <select name="age_range" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                                <option value="">{{ translateText('ټول') }}</option>
                                <option value="0-18" {{ request('age_range') == '0-18' ? 'selected' : '' }}>{{ translateText('۰-۱۸ کلن') }}</option>
                                <option value="19-35" {{ request('age_range') == '19-35' ? 'selected' : '' }}>{{ translateText('۱۹-۳۵ کلن') }}</option>
                                <option value="36-50" {{ request('age_range') == '36-50' ? 'selected' : '' }}>{{ translateText('۳۶-۵۰ کلن') }}</option>
                                <option value="51+" {{ request('age_range') == '51+' ? 'selected' : '' }}>{{ translateText('۵۱+ کلن') }}</option>
                            </select>
                        </div>

                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('ولایت') }}</label>
                            <select name="province" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                                <option value="">{{ translateText('ټول ولایتونه') }}</option>
                                @if(isset($provinces) && count($provinces) > 0)
                                    @foreach($provinces as $province)
                                        <option value="{{ $province }}" {{ request('province') == $province ? 'selected' : '' }}>{{ $province }}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>

                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د ثبت کال') }}</label>
                            <select name="year" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                                <option value="">{{ translateText('ټول کلونه') }}</option>
                                @if(isset($years) && count($years) > 0)
                                    @foreach($years as $year)
                                        <option value="{{ $year }}" {{ request('year') == $year ? 'selected' : '' }}>{{ $year }}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                    </div>

                    <div class="flex flex-col md:flex-row gap-4 pt-4">
                        <button type="submit"
                            class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                            <i class="fas fa-filter mr-2"></i> {{ translateText('فلټر کول') }}
                        </button>
                        <a href="{{ route('patient.index') }}"
                            class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                            <i class="fas fa-undo mr-2"></i> {{ translateText('بیا تنظیمول') }}
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- د ناروغانو لیست -->
        <div class="bg-white rounded-2xl shadow-xl p-4 border-0">
            <h3 class="text-xl font-bold mb-4 text-gray-800 pb-2 flex items-center border-0">
                <i class="fas fa-user-injured mr-2 text-gray-600"></i>
                ثبت شوي ناروغان
            </h3>

            <div class="overflow-x-auto">
                <table class="table-fixed w-full text-xs">
                    <thead>
                        <tr class="bg-white text-gray-800 border border-gray-800">
                            <th class="py-2 px-1 text-center w-8 border-r border-gray-800">شمېره</th>
                            <th class="py-2 px-1 text-center w-20 border-r border-gray-800">نوم</th>
                            <th class="py-2 px-1 text-center w-8 border-r border-gray-800">عمر</th>
                            <th class="py-2 px-1 text-center w-12 border-r border-gray-800">جنسیت</th>
                            <th class="py-2 px-1 text-center w-16 border-r border-gray-800">اړیکه</th>
                            <th class="py-2 px-1 text-center w-20 border-r border-gray-800">برېښنالیک</th>
                            <th class="py-2 px-1 text-center w-16 border-r border-gray-800">هېواد</th>
                            <th class="py-2 px-1 text-center w-16 border-r border-gray-800">ولایت</th>
                            <th class="py-2 px-1 text-center w-16 border-r border-gray-800">ولسوالي</th>
                            <th class="py-2 px-1 text-center w-16 border-r border-gray-800">کلی</th>
                            <th class="py-2 px-1 text-center w-12">کړنې</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if(isset($patients) && count($patients) > 0)
                            @foreach($patients as $p)
                                <tr class="border-b hover:bg-gray-100">
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $loop->iteration }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->Patiet_Name }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->Patient_Age }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->Patient_Gender }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->Patient_phone ?? 'نشته' }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->Patient_email ?? 'نشته' }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">افغانستان</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->address->P_Province ?? 'نشته' }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->address->P_Distract ?? 'نشته' }}</td>
                                    <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->address->P_Village ?? 'نشته' }}</td>
                                    <td class="py-2 px-1 text-center">
                                        <div class="flex justify-center">
                                            <button type="button" onclick="showUpdateForm(
                                                '{{ $p->Patient_id }}',
                                                '{{ $p->Patiet_Name }}',
                                                '{{ $p->Patient_Age }}',
                                                '{{ $p->Patient_Gender }}',
                                                '{{ $p->Patient_phone }}',
                                                '{{ $p->Patient_email }}',
                                                'افغانستان',
                                                '{{ $p->address->P_Province ?? '' }}',
                                                '{{ $p->address->P_Distract ?? '' }}',
                                                '{{ $p->address->P_Village ?? '' }}'
                                            )" 
                                            
                                            class="p-1 bg-white text-gray-700 rounded shadow-sm hover:bg-gray-200 hover:text-black transition-all duration-300 mx-0.5 border-0">
                                                <!-- <i class="fas fa-edit text-xs"></i> -->
                                            </button>
                                            <form action="{{ route('patient.destroy', $p->Patient_id) }}" method="POST" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="p-1 bg-white text-gray-700 rounded shadow-sm hover:bg-gray-200 hover:text-black transition-all duration-300 mx-0.5 border-0" onclick="return confirm('آیا ډاډه یاست چې دا ناروغ له منځه یوسئ؟')">
                                                    <i class="fas fa-trash text-xs"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="11" class="py-6 text-center text-gray-500">هیڅ ناروغ شتون نه لري</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4 flex justify-center">
                @if(isset($patients))
                    {{ $patients->links() }}
                @endif
            </div>
        </div>
    </div>
</div>

<script>
function showUpdateForm(patientId, name, age, gender, phone, email, country, province, district, village) {
    // Set form action
    document.getElementById('updatePatientForm').action = "{{ route('patient.update', '') }}/" + patientId;

    // Store patient ID in hidden field
    document.getElementById('patient_id').value = patientId;

    // Fill form fields with patient data
    document.getElementById('patient_name').value = name;
    document.getElementById('patient_age').value = age;
    document.getElementById('patient_gender').value = gender;
    document.getElementById('patient_phone').value = phone || '';
    document.getElementById('patient_email').value = email || '';
    document.getElementById('patient_country').value = country || '';
    document.getElementById('patient_province').value = province || '';
    document.getElementById('patient_district').value = district || '';
    document.getElementById('patient_village').value = village || '';

    // Show the form
    document.getElementById('patientUpdateForm').style.display = 'block';

    // Scroll to form
    document.getElementById('patientUpdateForm').scrollIntoView({ behavior: 'smooth' });
    
    // Log form data for debugging
    console.log('Patient ID:', patientId);
    console.log('Name:', name);
    console.log('Age:', age);
    console.log('Gender:', gender);
    console.log('Form action:', document.getElementById('updatePatientForm').action);
}

function hideUpdateForm() {
    document.getElementById('patientUpdateForm').style.display = 'none';
}

// Add event listener to prevent default form submission and use AJAX instead
document.addEventListener('DOMContentLoaded', function() {
    const updateForm = document.getElementById('updatePatientForm');
    
    if (updateForm) {
        updateForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const patientId = document.getElementById('patient_id').value;
            const url = this.action;
            
            // Log form data for debugging
            console.log('Submitting form to:', url);
            console.log('Patient ID:', patientId);
            for (let pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }

            fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('Response:', data);
                if (data.success) {
                    // Show success message
                    alert('د ناروغ معلومات په بریالیتوب سره تازه شول!');

                    // Hide the form
                    hideUpdateForm();

                    // Reload the page to show updated data
                    window.location.reload();
                } else {
                    // Show error message with details
                    let errorMessage = 'تېروتنه: ';
                    if (data.errors) {
                        errorMessage += Object.values(data.errors).flat().join('\n');
                    } else {
                        errorMessage += (data.message || 'د ناروغ معلومات تازه نه شول!');
                    }
                    alert(errorMessage);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('تېروتنه: د ناروغ معلومات تازه نه شول!');
            });
        });
    } else {
        console.error('Update form not found!');
    }

    // Filter toggle functionality
    const filterToggleBtn = document.getElementById('filterToggleBtn');
    const filterCard = document.getElementById('filterCard');

    if (filterToggleBtn && filterCard) {
        filterToggleBtn.addEventListener('click', function() {
            if (filterCard.classList.contains('hidden')) {
                filterCard.classList.remove('hidden');
                filterToggleBtn.innerHTML = '<i class="fas fa-times mr-2"></i> {{ translateText("فلټر بندول") }}';
                filterToggleBtn.classList.add('bg-red-100', 'text-red-700');
                filterToggleBtn.classList.remove('bg-white', 'text-gray-700');
            } else {
                filterCard.classList.add('hidden');
                filterToggleBtn.innerHTML = '<i class="fas fa-filter mr-2"></i> {{ translateText("فلټر") }}';
                filterToggleBtn.classList.remove('bg-red-100', 'text-red-700');
                filterToggleBtn.classList.add('bg-white', 'text-gray-700');
            }
        });
    }
});
</script>

@endsection
























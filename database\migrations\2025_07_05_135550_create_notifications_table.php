<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // questionnaire_completed, patient_registered, etc.
            $table->string('title');
            $table->text('message');
            $table->unsignedBigInteger('patient_id');
            $table->string('patient_name');
            $table->json('patient_data')->nullable(); // Patient details
            $table->json('questionnaire_data')->nullable(); // Questionnaire responses
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['is_read', 'created_at']);
            $table->index(['patient_id']);
            $table->index(['type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};

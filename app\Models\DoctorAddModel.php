<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DoctorAddModel extends Model
{
    use HasFactory;
    
    protected $table = 'doctor_add_models';
    
    protected $primaryKey = 'Dr_Add_Id';
    
    protected $fillable = [
        'Dr_Add_Id',
        'Dr_Id',
        'Dr_Country',
        'Dr_Province',
        'Dr_Distract',
        'Dr_Village',
    ];
    
    /**
     * Get the doctor that owns this address.
     */
    public function doctor()
    {
        return $this->belongsTo(DoctorModel::class, 'Dr_Id', 'Dr_Id');
    }
}







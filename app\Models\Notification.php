<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'title',
        'message',
        'patient_id',
        'patient_name',
        'patient_data',
        'questionnaire_data',
        'is_read',
        'read_at'
    ];

    protected $casts = [
        'patient_data' => 'array',
        'questionnaire_data' => 'array',
        'is_read' => 'boolean',
        'read_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the patient associated with this notification (optional relationship)
     */
    public function patient(): BelongsTo
    {
        return $this->belongsTo(PatientModel::class, 'patient_id', 'Patient_id');
    }

    /**
     * Get patient safely (returns null if patient doesn't exist)
     */
    public function getPatientSafely()
    {
        try {
            return $this->patient;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(): void
    {
        $this->update([
            'is_read' => true,
            'read_at' => now()
        ]);
    }

    /**
     * Check if notification is unread
     */
    public function isUnread(): bool
    {
        return !$this->is_read;
    }

    /**
     * Scope for unread notifications
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope for read notifications
     */
    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    /**
     * Create a new notification for patient questionnaire completion
     */
    public static function createPatientNotification(
        string $type,
        string $title,
        string $message,
        int $patientId,
        string $patientName,
        array $patientData = [],
        array $questionnaireData = []
    ): self {
        return self::create([
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'patient_id' => $patientId,
            'patient_name' => $patientName,
            'patient_data' => $patientData,
            'questionnaire_data' => $questionnaireData,
            'is_read' => false
        ]);
    }

    /**
     * Get formatted time ago
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get notification icon based on type
     */
    public function getIconAttribute(): string
    {
        return match($this->type) {
            'questionnaire_completed' => 'fas fa-clipboard-check',
            'patient_registered' => 'fas fa-user-plus',
            'high_score_alert' => 'fas fa-exclamation-triangle',
            default => 'fas fa-bell'
        };
    }

    /**
     * Get notification color based on type
     */
    public function getColorAttribute(): string
    {
        return match($this->type) {
            'questionnaire_completed' => 'blue',
            'patient_registered' => 'green',
            'high_score_alert' => 'red',
            default => 'gray'
        };
    }
}

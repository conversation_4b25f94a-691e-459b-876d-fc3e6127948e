 const loginForm = document.getElementById('loginForm');
 if (loginForm) {
   loginForm.addEventListener('submit', function(e) {
    e.preventDefault(); // اول فورم ودروه
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value.trim();
    const emailError = document.getElementById('emailError');
    const passError = document.getElementById('passError');

    let isValid = true;

    // ایمېل تایید
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(email)) {
      emailError.style.display = 'block';
      isValid = false;
    } else {
      emailError.style.display = 'none';
    }

    // پټ نوم تایید
    const onlyDigits = /^\d+$/;
    if (password.length <= 4 || password.length >= 8 || !onlyDigits.test(password)) {
      passError.style.display = 'block';
      isValid = false;
    } else {
      passError.style.display = 'none';
    }

    // که دواړه سم وي، فورم submit شي
    if (isValid) {
      this.submit(); // دلته submit بیا چالو کېږي
    }
  });
 }
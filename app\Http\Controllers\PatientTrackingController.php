<?php

namespace App\Http\Controllers;

use App\Models\PatientModel;
use App\Models\PatientProgress;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PatientTrackingController extends Controller
{
    public function index()
    {
        $patients = PatientModel::with('progress')->paginate(10);
        return view('patient.tracking.index', compact('patients'));
    }

    public function show($id)
    {
        $patient = PatientModel::with(['progress' => function($query) {
            $query->orderBy('date', 'desc');
        }])->findOrFail($id);

        return view('patient.tracking.show', compact('patient'));
    }

    public function addProgress(Request $request, $id)
    {
        $validated = $request->validate([
            'date' => 'required|date',
            'notes' => 'required|string',
            'status' => 'required|string|in:improving,stable,worsening',
            'treatment_changes' => 'nullable|string',
        ]);

        $patient = PatientModel::findOrFail($id);

        $progress = new PatientProgress();
        $progress->patient_id = $patient->id;
        $progress->doctor_id = Auth::id();
        $progress->date = $request->date;
        $progress->notes = $request->notes;
        $progress->status = $request->status;
        $progress->treatment_changes = $request->treatment_changes;
        $progress->save();

        return redirect()->route('patient.tracking.show', $id)
            ->with('success', 'د ناروغ پرمختګ په بریالیتوب سره اضافه شو');
    }

    public function exportProgress($id)
    {
        $patient = PatientModel::with(['progress' => function($query) {
            $query->orderBy('date', 'desc');
        }])->findOrFail($id);

        // Set CSV file name
        $filename = 'patient_progress_'.$id.'.csv';

        // Set headers for CSV download
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        // Create a callback function that will be used to stream the CSV content
        $callback = function() use ($patient) {
            // Open output stream
            $file = fopen('php://output', 'w');

            // Add UTF-8 BOM to fix Excel encoding issues
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // Add headers
            fputcsv($file, ['نېټه', 'یادداشتونه', 'حالت', 'د درملنې بدلونونه']);

            // Add data rows
            foreach ($patient->progress as $progress) {
                fputcsv($file, [
                    $progress->date,
                    $progress->notes,
                    $progress->status,
                    $progress->treatment_changes ?? 'نشته',
                ]);
            }

            // Close the file
            fclose($file);
        };

        // Stream the CSV file to the browser
        return response()->stream($callback, 200, $headers);
    }
}






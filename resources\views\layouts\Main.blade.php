<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title')</title>

    <!-- FontAwesome for icons -->
        <link rel="stylesheet" href="{{url('public/../Css_filse/home.css')}}">
        <link rel="stylesheet" href="{{url('public/../Css_filse/about_us.css')}}">
        <link rel="stylesheet" href="{{url('public/../Css_filse/news.css')}}">
        <link rel="stylesheet" href="{{url('public/../Css_filse/Question_bank.css')}}">
        <link rel="stylesheet" href="{{url('public/../Css_filse/types-of-mental-health.css')}}">
        <link rel="stylesheet" href="{{url('public/../Css_filse/books.css')}}">
        <link rel="stylesheet" href="{{url('public/../Css_filse/doctor-section.css')}}">
        <link rel="stylesheet" href="{{url('public/../Css_filse/health-section.css')}}">
         <link rel="stylesheet" href="{{url('public/../Css_filse/language.css')}}">
         
        <link href="https://fonts.googleapis.com/css2?family=Amiri&display=swap" rel="stylesheet">
       <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" integrity="sha512-Avb2QiuDEEvB4bZJYdft2mNjVShBftLdPG8FJ0V7irTLQ8Uo0qcPxh4Plq7G5tGm0rU+1SPhVotteLpBERwTkw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

       <!-- Custom Styles Stack -->
       @stack('styles')

</head>

<body>

<!-- ========nav bar section=================== -->
<nav class="navbar">

  <div class="logo">
    <!-- Update the logo link to point to the home page -->
    <a href="{{ url('/') }}">
      <img src="{{ url('public/../imagese/logo.PNG') }}" alt="Logo">
    </a>
  </div>

 <button class="menu-toggle" id="menu-toggle">
    <i class="fas fa-bars"></i>
  </button>

  <ul class="nav-links" id="nav-links">
    <!-- ========== login section ============ -->
    <li>
      <a onclick="toggleLoginMenu()" class="login-btn"> <i class="fas fa-sign-in-alt"></i>   {{ trans_static('ننوتل') }}<i class="fas fa-caret-down"></i>
      </a>

      <!-- dropdown menu -->
      <div class="dropdown-menu">
        @if(Auth::check())
          <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
            @csrf
          </form>
          <a  onclick="logoutUser()">
            {{ trans_static('خارجیدل') }}
          </a>
        @else
          <a href="{{ url('/login') }}"> {{ trans_static('ننوتل') }} </a>
        @endif
      </div>
    </li>
      <li>
     
     

    <!-- ========== language dropdown ============ -->
    <li>
      <a onclick="toggleLangMenu()">
        <i class="fas fa-language"></i>
        {{ trans_static('ژبه') }}<i class="fas fa-caret-down"></i>
      </a>
      <!-- dropdown menu -->
          <div class="dropdown-menu">
        <a href="{{ route('set.language', ['lang' => 'ps']) }}">پښتو</a>
        <a href="{{ route('set.language', ['lang' => 'fa']) }}">دري</a>
      </div>
    </li>
    <!-- ========== page links ============ -->
    <li>
      <a href="#footer-section"><i class="fas fa-phone-alt"></i> {{ trans_static('اړیکه ونیسئ') }}</a>
    </li>
    <li>
      <a href="{{ url('/aboutUs') }}"><i class="fas fa-info-circle"></i> {{ trans_static('زموږ په اړه') }}</a>
    </li>
    <li>
      <a href="{{ url('/news') }}"><i class="fas fa-newspaper"></i> {{ trans_static('خبرونه') }}</a>
    </li>
    <li>
      <a href="{{ url('/question') }}"><i class="fas fa-question-circle"></i> {{ trans_static('تشخیص کول ') }}</a>
    </li>
    <li>
      <a href="{{ url('/ketabs') }}"><i class="fas fa-book"></i> {{ trans_static('کتابونو ') }}</a>
    </li>
    <li>
      <a href="{{ url('/doctor') }}"><i class="fas fa-user-md"></i> {{ trans_static('داکتران ') }}</a>
    </li>
    <li>
      <a href="{{ url('/') }}"><i class="fas fa-home"></i> {{ trans_static('اصلی صفحه') }}</a>
    </li>
  </ul>

</nav>


    @yield('contents')
 <div id="kjkj"></div>

<!-- ====contact_us section=============== -->

<div id="contact_us">
  <h1>{{ trans_static('زموږ سره په اړیکه کی شی') }}</h1>
  <div class="container">
    <div class="form-box">
      <h2>{{ trans_static('یواځې سلام ووایئ!') }}</h2>
      <p>{{ trans_static('راځئ چې له تاسولا زیات معلومات واخلو.') }}</p>

      <!-- Success/Error Messages -->
      @if(session('success'))
        <div class="alert alert-success" style="background-color: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin-bottom: 15px; border: 1px solid #c3e6cb;">
          {{ session('success') }}
        </div>
      @endif

      @if(session('error'))
        <div class="alert alert-error" style="background-color: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin-bottom: 15px; border: 1px solid #f5c6cb;">
          {{ session('error') }}
        </div>
      @endif

      <form id="contactForm" method="POST" action="{{ route('Sroute') }}" novalidate>
        @csrf
        <div class="input-group">
          <div class="form-group">
            <input type="text" id="fname" name="fname" placeholder="{{ trans_static('ستاسو نوم') }}" value="{{ old('fname') }}">
            @if($errors->has('fname'))
              <div class="error-message">{{ $errors->first('fname') }}</div>
            @endif
            <div id="fname-error" class="error-message"></div>
          </div>
          <div class="form-group">
            <input type="text" id="lname" name="lname" placeholder="{{ trans_static('ستاسو تخلص') }}" value="{{ old('lname') }}">
            @if($errors->has('lname'))
              <div class="error-message">{{ $errors->first('lname') }}</div>
            @endif
            <div id="lname-error" class="error-message"></div>
          </div>
        </div>

        <div class="input-group">
          <div class="form-group">
            <input type="email" id="email" name="email" placeholder="{{ trans_static('ستاسو ایمیل') }}" value="{{ old('email') }}">
            @if($errors->has('email'))
              <div class="error-message">{{ $errors->first('email') }}</div>
            @endif
            <div id="email-error" class="error-message"></div>
          </div>
          <div class="form-group">
            <input type="tel" id="phone" name="phone" placeholder="07XXXXXXXX" value="{{ old('phone') }}">
            @if($errors->has('phone'))
              <div class="error-message">{{ $errors->first('phone') }}</div>
            @endif
            <div id="phone-error" class="error-message"></div>
          </div>
        </div>

        <div class="form-group">
          <textarea id="message" name="message" rows="4" placeholder="{{ trans_static('خپل پیغام ولیکئ...') }}">{{ old('message') }}</textarea>
          @if($errors->has('message'))
            <div class="error-message">{{ $errors->first('message') }}</div>
          @endif
          <div id="message-error" class="error-message"></div>
        </div>

        <button type="submit">{{ trans_static('ولیږئ') }}</button>
      </form>
    </div>

    <div class="info-box">
      <h3>{{ trans_static('د اړیکې معلومات') }}</h3>
      <p>{{ trans_static('کندهار، افغانستان') }}<br>{{ trans_static('د عیدګاه څنګ ته نهمه ناحیه') }}</p>
      <p>{{ trans_static('شمیره') }}: ۰۷۰۶۰۰۹۸۶۳</p>
      <p>{{ trans_static('کاري وختونه') }}: {{ trans_static('ټولی رسمی ورځی') }}</p>

      <h3>{{ trans_static('موږ تعقیب کړئ') }}</h3>
      <div class="social-links">
        <a href="https://www.facebook.com/share/v/191MszWPsK/" target="_blank" aria-label="Facebook">
          <i class="fab fa-facebook-f"></i>
        </a>
        <a href="https://youtu.be/z2PqTt7xvM4?si=D0xvy8WZr8eFY-nS" target="_blank" aria-label="YouTube">
          <i class="fab fa-youtube"></i>
        </a>
        <a href="https://t.me/Psychology786" target="_blank" aria-label="Telegram">
          <i class="fab fa-telegram-plane"></i>
        </a>
      </div>
    </div>
  </div>
</div>

<!-- =====footer-section================== -->
<div id="dsfhgfdg">
  <div id="dfdfdf"></div>

  <footer id="footer-section">
    <div class="aboutUs-section">
      <h3 class="headings">{{ trans_static('موږ تعقیب کړئ') }}</h3>
      <div class="hgfd">
        <a href="https://www.facebook.com/share/v/191MszWPsK/" target="_blank" aria-label="Facebook">
          <i class="fab fa-facebook-f"></i>
        </a>
        <a href="https://youtu.be/z2PqTt7xvM4?si=D0xvy8WZr8eFY-nS" target="_blank" aria-label="YouTube">
          <i class="fab fa-youtube"></i>
        </a>
        <a href="https://t.me/Psychology786" target="_blank" aria-label="Telegram">
          <i class="fab fa-telegram"></i>
        </a>
        <a href="https://wa.me/0706009863" target="_blank" aria-label="WhatsApp">
          <i class="fab fa-whatsapp"></i>
        </a>
      </div>
    </div>

    <div class="aboutUs-section">
      <h3 class="headings">{{ trans_static('زموږ په اړه') }}</h3>
      <a href="{{ url('/aboutUs') }}" class="mylinks"><i class="fas fa-info-circle"></i> {{ trans_static('زموږ په اړه') }}</a>
    </div>

    <div class="aboutUs-section">
      <h3 class="headings">{{ trans_static('موبایل افلیکیشن') }}</h3>
      <a href="#" class="mylinks">
        <i class="fas fa-mobile-alt"></i> {{ trans_static('موبایل') }}
      </a>
    </div>

    <div class="aboutUs-section">
      <h3 class="headings">{{ trans_static('نور وپوهیږی') }}</h3>
      <a href="{{ url('/typesofMental') }}" class="mylinks">
        <i class="fas fa-list-alt"></i> {{ trans_static('د رواني ناروغیو ډولونه او د هغی حل لاری') }}
      </a>
      <a href="{{ url('/healtsection') }}" class="mylinks">
        <i class="fas fa-shield-alt"></i> {{ trans_static('د رواني ناروغیو څخه د ځان ساتنه') }}
      </a>
    </div>
  </footer>
</div>

<!-- CSS styles for error messages -->
<style>

</style>

<!-- Success and error flash messages -->
@if(session('success'))
  <div class="success-message">
    {{ session('success') }}
  </div>
@endif

@if(session('error'))
  <div class="error-message">
    {{ session('error') }}
  </div>
@endif

        <script src="{{url('public/../Js_filse/home.js')}}"></script>
       <script src="{{url('public/../Js_filse/testemomnial-section.js')}}"></script>
        <script src="{{url('public/../Js_filse/health_sction.js')}}"></script>
        <script src="{{url('public/../Js_filse/types_of_mental_health.js')}}"></script>
       <script src="{{url('public/../Js_filse/setting.js')}}"></script>
      <script src="{{url('public/../Js_filse/Questions.js')}}"></script>
      
         <script src="{{url('public/../Js_filse/contact_us.js')}}"></script>
           <script src="{{url('public/../Js_filse/book.js')}}"></script>
             <script src="{{url('public/../Js_filse/login-p.js')}}"></script>
         
          <script src="{{url('public/../Js_filse/doctor-section.js')}}"></script>
    

  </body>

  </html>







<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ResetDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:reset {--force : Force the operation to run without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset the database and run all migrations and seeders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('force') || $this->confirm('Are you sure you want to reset the database? All data will be lost.')) {
            $this->info('Clearing Laravel cache...');
            $this->call('optimize:clear');
            $this->call('config:clear');
            $this->call('cache:clear');
            
            $this->info('Refreshing database...');
            $this->call('migrate:fresh', ['--seed' => true, '--force' => true]);
            
            $this->info('Database reset successfully!');
        }
    }
}
@extends('layouts.Admin')
@section('title','Video Copy')

@section('contents')
<!-- Main Content -->
<div class="p-4 md:p-8 min-h-screen transition-all w-full bg-white">
    <div class="container mx-auto max-w-6xl">
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-8">
            <div class="flex items-center mb-4 md:mb-0">
                <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-video text-gray-700 text-xl"></i>
                </div>
                <h1 class="text-2xl md:text-3xl font-bold text-gray-800 ml-4">{{ translateText('د ویډیو مدیریت') }}</h1>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('videos.index') }}" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                    <i class="fas fa-sync-alt mr-2"></i> {{ translateText('تازه کول') }}
                </a>
                <button id="filterToggleBtn" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                    <i class="fas fa-filter mr-2"></i> {{ translateText('فلټر') }}
                </button>
            </div>
        </div>

        <!-- Alert Messages -->
        @if(session('success'))
            <div class="bg-green-100 border-r-4 border-green-500 text-green-700 p-4 mb-6 rounded-lg shadow-md alert-dismissible fade show" role="alert" id="successAlert">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-500 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="font-bold">{{ translateText('بریالیتوب!') }}</p>
                        <p>{{ session('success') }}</p>
                    </div>
                    <button type="button" class="ml-auto text-green-700 hover:text-green-900 focus:outline-none" onclick="document.getElementById('successAlert').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        @endif

        @if(session('error'))
            <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-lg shadow-md alert-dismissible fade show" role="alert" id="errorAlert">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="font-bold">{{ translateText('تېروتنه!') }}</p>
                        <p>{{ session('error') }}</p>
                    </div>
                    <button type="button" class="ml-auto text-red-700 hover:text-red-900 focus:outline-none" onclick="document.getElementById('errorAlert').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        @endif

        @if($errors->any())
            <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-lg shadow-md" role="alert">
                <div class="flex items-center mb-2">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <p class="font-bold">{{ translateText('تېروتنې') }}</p>
                    </div>
                </div>
                <ul class="list-disc list-inside pl-10">
                    @foreach($errors->all() as $error)
                        <li>{{ translateText($error) }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <!-- Filter Card - Hidden by default -->
        <div id="filterCard" class="bg-white rounded-2xl shadow-xl mb-8 border-0 overflow-hidden transform transition-all duration-300 hover:shadow-2xl hidden">
            <div class="bg-gray-200 px-6 py-4">
                <h2 class="text-xl md:text-2xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-filter mr-2"></i> {{ translateText('د ویډیوګانو فلټر کول') }}
                </h2>
            </div>

            <div class="p-6">
                <form action="/videod" method="GET" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د اړوند مقالې له مخې پلټنه') }}</label>
                            <input type="text" name="search" value="{{ request('search') }}" placeholder="{{ translateText('د مقالې نوم ولیکئ...') }}"
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                        </div>

                        <div class="group">
                            <label class="block text-sm font-medium mb-2 text-gray-700 group-hover:text-gray-800 transition-colors">{{ translateText('د ثبت کال') }}</label>
                            <select name="year" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500 transition-all duration-300">
                                <option value="">{{ translateText('ټول کلونه') }}</option>
                                @if(isset($years) && count($years) > 0)
                                    @foreach($years as $year)
                                        <option value="{{ $year }}" {{ request('year') == $year ? 'selected' : '' }}>{{ $year }}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                    </div>

                    <div class="flex flex-col md:flex-row gap-4 pt-4">
                        <button type="submit"
                            class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                            <i class="fas fa-filter mr-2"></i> {{ translateText('فلټر کول') }}
                        </button>
                        <a href="/videod"
                            class="w-full md:w-auto px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                            <i class="fas fa-undo mr-2"></i> {{ translateText('بیا تنظیمول') }}
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- د ویډیو فورم -->
        <div class="bg-white rounded-lg shadow-md mb-8 overflow-hidden">
            <div class="bg-gray-100 px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-bold text-gray-800 flex items-center">
                        <i class="fas fa-plus-circle mr-2 text-gray-600"></i>
                        {{ isset($editVideo) ? translateText('د ویډیو معلومات تعدیل کړئ') : translateText('ویډیو لینک اضافه کړی') }}
                    </h2>
                </div>
            </div>

            <div class="p-6">
                <form action="{{ isset($editVideo) ? route('videos.update', $editVideo->Video_Id) : route('videos.store') }}"
                    method="POST" class="space-y-6">
                    @csrf
                    @if(isset($editVideo))
                        @method('PUT')
                    @endif

                    <div class="mb-4">
                        <label for="video_url" class="block text-gray-700 text-sm font-bold mb-2">
                            {{ translateText('د ویډیو لینک') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="video_url" id="video_url"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                            value="{{ isset($editVideo) ? $editVideo->Video_URL : old('video_url') }}" required>
                        @error('video_url')
                            <p class="text-red-500 text-xs mt-1">{{ translateText('د ویډیو لینک سم نه دی. مهرباني وکړئ د یوټیوب سم لینک ولیکئ.') }}</p>
                        @enderror
                    </div>

                    <div class="mb-4">
                        <label for="article_id" class="block text-gray-700 text-sm font-bold mb-2">
                            {{ translateText('اړوند مقاله') }}
                        </label>
                        <select name="article_id" id="article_id"
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            <option value="">{{ translateText('مقاله وټاکئ') }}</option>
                            @foreach(\App\Models\ArticalModel::all() as $article)
                                <option value="{{ $article->A_Id }}"
                                    {{ (isset($editVideo) && $editVideo->A_id == $article->A_Id) ? 'selected' : '' }}>
                                    {{ $article->A_Title }}
                                </option>
                            @endforeach
                        </select>
                        @error('article_id')
                            <p class="text-red-500 text-xs mt-1">{{ translateText('د مقالې په ټاکلو کې تېروتنه رامنځته شوه.') }}</p>
                        @enderror
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="px-6 py-3 bg-white text-gray-700 rounded-lg hover:bg-gray-200 hover:text-black transition-all duration-300 shadow-md hover:shadow-lg flex items-center justify-center border-0">
                            <i class="fas fa-{{ isset($editVideo) ? 'edit' : 'plus' }} mr-2"></i>
                            {{ isset($editVideo) ? translateText('تعدیل کړئ') : translateText('اضافه کړئ') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- د ویډیوګانو لیست -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-gray-100 px-6 py-4 border-b border-gray-200">
                <h3 class="text-xl font-bold text-gray-800 flex items-center">
                    <i class="fas fa-film mr-2 text-gray-600"></i>
                    {{ translateText('ثبت شوي ویډیوګانې') }}
                </h3>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ translateText('عکس') }}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ translateText('لینک') }}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ translateText('عملیې') }}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @if(isset($videos) && count($videos) > 0)
                            @foreach($videos as $video)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @if($video->youtube_thumbnail)
                                            <img src="{{ $video->youtube_thumbnail }}" alt="Video Thumbnail" class="w-24 h-16 object-cover rounded">
                                        @else
                                            <div class="w-24 h-16 bg-gray-200 rounded flex items-center justify-center">
                                                <i class="fas fa-film text-gray-400"></i>
                                            </div>
                                        @endif
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <a href="{{ $video->Video_URL }}" target="_blank"
                                            class="text-blue-600 hover:text-blue-800 hover:underline break-all flex items-center">
                                            <i class="fas fa-external-link-alt mr-2 text-blue-500"></i>
                                            {{ Str::limit($video->Video_URL, 50) }}
                                        </a>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex gap-2">
                                            <a href="{{ $video->Video_URL }}" target="_blank"
                                                class="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors flex items-center">
                                                <i class="fas fa-play"></i>
                                            </a>
                                            {{-- <a href="{{ route('videos.edit', $video->Video_Id) }}"
                                                class="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors flex items-center">
                                                <i class="fas fa-edit"></i>
                                            </a> --}}
                                            <form action="{{ route('videos.destroy', $video->Video_Id) }}" method="POST"
                                                class="inline"
                                                onsubmit="return confirm('{{ translateText('آیا تاسو ډاډه یاست چې دا ویډیو غواړئ لرې کړئ؟') }}');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                    class="px-3 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors flex items-center">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                                    {{ translateText('هیڅ ویډیو نشته') }}
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter toggle functionality
    const filterToggleBtn = document.getElementById('filterToggleBtn');
    const filterCard = document.getElementById('filterCard');

    if (filterToggleBtn && filterCard) {
        filterToggleBtn.addEventListener('click', function() {
            if (filterCard.classList.contains('hidden')) {
                filterCard.classList.remove('hidden');
                filterToggleBtn.innerHTML = '<i class="fas fa-times mr-2"></i> {{ translateText("فلټر بندول") }}';
                filterToggleBtn.classList.add('bg-red-100', 'text-red-700');
                filterToggleBtn.classList.remove('bg-white', 'text-gray-700');
            } else {
                filterCard.classList.add('hidden');
                filterToggleBtn.innerHTML = '<i class="fas fa-filter mr-2"></i> {{ translateText("فلټر") }}';
                filterToggleBtn.classList.remove('bg-red-100', 'text-red-700');
                filterToggleBtn.classList.add('bg-white', 'text-gray-700');
            }
        });
    }
});
</script>

@endsection












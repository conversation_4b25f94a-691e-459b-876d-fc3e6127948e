<?php

namespace App\Http\Controllers;

use App\Models\Video;
use App\Models\VideoModel;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;

class VideoController extends Controller
{
    /**
     * Display a listing of the videos.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request): View
    {
        try {
            // Start with base query
            $query = VideoModel::query();

            // Apply search filter (for related article)
            if ($request->filled('search')) {
                $query->where('title', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%');
            }

            // Apply year filter
            if ($request->filled('year')) {
                $query->whereYear('created_at', $request->year);
            }

            // Get filtered videos
            $videos = $query->orderBy('created_at', 'desc')->get();

            // Get years for filter dropdown
            $years = VideoModel::selectRaw('YEAR(created_at) as year')
                ->distinct()
                ->orderBy('year', 'desc')
                ->pluck('year')
                ->filter();

            // Flash success message if filters are applied
            if ($request->hasAny(['search', 'year'])) {
                session()->flash('success', translateText('فلټر په بریالیتوب سره تطبیق شو!'));
            }

            return view('dashboardofproject.videocopy', compact('videos', 'years'));
        } catch (\Exception $e) {
            \Log::error('Error in video index: ' . $e->getMessage());
            return view('dashboardofproject.videocopy', [
                'videos' => collect(),
                'years' => collect()
            ]);
        }
    }

    /**
     * Store a newly created video in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validate the request
        $request->validate([
            'video_url' => 'required|url',
            'title' => 'nullable|string|max:255',
            'category' => 'nullable|string|max:50',
            'description' => 'nullable|string|max:1000',
        ], [
            'video_url.required' => translateText('د ویډیو لینک اړین دی'),
            'video_url.url' => translateText('د ویډیو لینک باید سم URL وي. مهرباني وکړئ د یوټیوب سم لینک ولیکئ.'),
            'title.max' => translateText('د عنوان اوږدوالی باید له 255 توریو څخه زیات نه وي'),
            'description.max' => translateText('د تشریح اوږدوالی باید له 1000 توریو څخه زیات نه وي'),
        ]);

        try {
            // Create a new record directly with create method
            $video = VideoModel::create([
                'Video_URL' => $request->video_url,
                'title' => $request->title,
                'category' => $request->category,
                'description' => $request->description,
                'A_id' => $request->article_id ?? 1, // Use article_id if provided, otherwise default to 1
            ]);

            return redirect()->back()->with('success', translateText('ویډیو په بریالیتوب سره اضافه شوه!'));
        } catch (\Exception $e) {
            // Log the error - Fixed: Use the fully qualified facade
            Log::error('Video creation error: ' . $e->getMessage());

            // Return with error message
            return redirect()->back()->with('error', translateText('تیروتنه: د ویډیو په اضافه کولو کې ستونزه رامنځته شوه.'))->withInput();
        }
    }

    /**
     * Update the specified video in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'video_url' => 'required|string|url',
            'title' => 'nullable|string|max:255',
            'category' => 'nullable|string|max:50',
            'description' => 'nullable|string|max:1000',
        ], [
            'video_url.required' => translateText('د ویډیو لینک اړین دی'),
            'video_url.url' => translateText('د ویډیو لینک باید سم URL وي. مهرباني وکړئ د یوټیوب سم لینک ولیکئ.'),
            'title.max' => translateText('د عنوان اوږدوالی باید له 255 توریو څخه زیات نه وي'),
            'description.max' => translateText('د تشریح اوږدوالی باید له 1000 توریو څخه زیات نه وي'),
        ]);

        try {
            $video = VideoModel::findOrFail($id);
            $video->Video_URL = $request->video_url;
            $video->title = $request->title;
            $video->category = $request->category;
            $video->description = $request->description;

            // If you need to update article association
            if ($request->has('article_id')) {
                $video->A_id = $request->article_id;
            }

            $video->save();

            return redirect()->route('videos.index')->with('success', translateText('ویډیو په بریالیتوب سره تازه شوه!'));
        } catch (\Exception $e) {
            // Log the error for debugging - Fixed: Use the fully qualified facade
            Log::error('Video update error: ' . $e->getMessage());

            return redirect()->back()->with('error', translateText('تیروتنه: د ویډیو په تازه کولو کې ستونزه رامنځته شوه.'))->withInput();
        }
    }

    /**
     * Remove the specified video from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id): RedirectResponse
    {
        try {
            $video = VideoModel::findOrFail($id);

            // Delete thumbnail if exists
            if ($video->thumbnail && file_exists(public_path($video->thumbnail))) {
                unlink(public_path($video->thumbnail));
            }

            $video->delete();

            return redirect()->route('videos.index')->with('success', translateText('ویډیو په بریالیتوب سره ړنګه شوه!'));
        } catch (\Exception $e) {
            // Log the error for debugging - Fixed: Use the fully qualified facade
            Log::error('Video deletion error: ' . $e->getMessage());

            return redirect()->back()->with('error', translateText('تیروتنه: ') . $e->getMessage());
        }
    }

    /**
     * Display videos on the frontend.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function showVideos(Request $request): View
    {
        $query = VideoModel::query();

        // Category filter if needed
        if ($request->has('category') && !empty($request->category)) {
            $query->where('category', $request->category);
        }

        // Get videos with pagination
        $videos = $query->latest()->paginate(12);

        return view('videos', compact('videos'));
    }

    /**
     * Display the specified video in admin dashboard.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id): View
    {
        $video = VideoModel::findOrFail($id);
        return view('dashboardofproject.video-show', compact('video'));
    }

    /**
     * Display the specified video for public view.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function showVideo($id): View
    {
        $video = VideoModel::findOrFail($id);
        return view('video_detail', compact('video'));
    }

    /**
     * Show the form for editing the specified video.
     *
     * @param  int  $id
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function edit($id)
    {
        try {
            // Find the video to edit
            $editVideo = VideoModel::findOrFail($id);

            // Get all videos for the table
            $videos = VideoModel::latest()->paginate(10);

            return view('dashboardofproject.videocopy', compact('editVideo', 'videos'));
        } catch (\Exception $e) {
            // Log the error - Fixed: Use the fully qualified facade
            Log::error('Video edit error: ' . $e->getMessage());

            // Redirect with error message
            return redirect()->route('videos.index')
                ->with('error', translateText('ویډیو ونه موندل شوه: ') . $e->getMessage());
        }
    }
}
























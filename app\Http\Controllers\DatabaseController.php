<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class DatabaseController extends Controller
{
    /**
     * Reset the database.
     */
    public function reset()
    {
        try {
            // Clear Laravel cache
            Artisan::call('optimize:clear');
            Artisan::call('config:clear');
            Artisan::call('cache:clear');
            
            // Refresh database
            Artisan::call('migrate:fresh', ['--seed' => true, '--force' => true]);
            
            return response()->json([
                'success' => true,
                'message' => 'Database reset successfully!',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage(),
            ], 500);
        }
    }
}
@extends('layouts/Admin')
@section('title','د ناروغانو راپور')
@section('contents')

<div class="p-4 md:p-8 min-h-screen transition-all w-full bg-white">
    <div class="container mx-auto max-w-6xl">
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-8">
            <div class="flex items-center mb-4 md:mb-0">
                <div class="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center shadow-lg transform hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-file-medical-alt text-gray-700 text-xl"></i>
                </div>
                <h1 class="text-2xl md:text-3xl font-bold text-gray-800 mr-4">د ناروغانو راپور</h1>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('patient.index') }}" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                    <i class="fas fa-arrow-right mr-2"></i>
                    بېرته
                </a>
            </div>
        </div>

        <!-- Filter Section -->
        <div class="bg-white rounded-2xl shadow-xl p-4 mb-6 border-0">
            <h3 class="text-xl font-bold text-gray-800 pb-2 mb-4 flex items-center border-0">
                <i class="fas fa-filter mr-2 text-gray-600"></i>
                فلټر کول
            </h3>
            
            <form action="{{ route('patient.report') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="group">
                    <label class="block text-sm font-medium mb-2 text-gray-700">کال</label>
                    <select name="year" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500">
                        <option value="">ټول کلونه</option>
                        @foreach($years as $y)
                            <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div class="group">
                    <label class="block text-sm font-medium mb-2 text-gray-700">میاشت</label>
                    <select name="month" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500">
                        <option value="">ټولې میاشتې</option>
                        @php
                            $months = ['جنوري', 'فبروري', 'مارچ', 'اپریل', 'می', 'جون', 'جولای', 'اګست', 'سپتمبر', 'اکتوبر', 'نومبر', 'دسمبر'];
                        @endphp
                        @foreach($months as $key => $m)
                            <option value="{{ $key + 1 }}" {{ $month == $key + 1 ? 'selected' : '' }}>{{ $m }}</option>
                        @endforeach
                    </select>
                </div>
                
                <div class="group">
                    <label class="block text-sm font-medium mb-2 text-gray-700">جنسیت</label>
                    <select name="gender" class="w-full p-2 border border-gray-300 rounded-lg focus:ring-gray-500 focus:border-gray-500">
                        <option value="">ټول</option>
                        <option value="نارینه" {{ $gender == 'نارینه' ? 'selected' : '' }}>نارینه</option>
                        <option value="ښځینه" {{ $gender == 'ښځینه' ? 'selected' : '' }}>ښځینه</option>
                    </select>
                </div>
                
                <div class="flex items-end space-x-2">
                    <button type="submit" class="bg-white text-gray-700 px-4 py-2 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
                        <i class="fas fa-filter mr-2"></i>
                        فلټر کول
                    </button>
                    
                 
                </div>
            </form>
        </div>
        
        <!-- Export Section -->
        <div class="bg-white rounded-2xl shadow-xl p-4 mb-6 border-0">
            <h3 class="text-xl font-bold text-gray-800 pb-2 mb-4 flex items-center border-0">
                <i class="fas fa-file-export mr-2 text-gray-600"></i>
                تبدیلول
            </h3>
            
            <div class="flex space-x-4">
                <a href="{{ route('patient.report', array_merge(request()->all(), ['export' => 'csv'])) }}" class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium flex items-center shadow-md hover:bg-green-700 transition-all duration-300 border-0">
                    <i class="fas fa-file-csv mr-2"></i>
                    CSVتبدیلول                 </a>
                
                <!-- <a href="{{ route('patient.report', array_merge(request()->all(), ['export' => 'pdf'])) }}" class="bg-red-600 text-white px-4 py-2 rounded-lg font-medium flex items-center shadow-md hover:bg-red-700 transition-all duration-300 border-0">
                    <i class="fas fa-file-pdf mr-2"></i>
                    PDF ته صادرول
                </a> -->
            </div>
        </div>

        <!-- Report Content -->
        <div class="bg-white rounded-2xl shadow-xl p-4 border-0">
            <!-- Monthly Report -->
            <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800 pb-2 flex items-center border-0">
                        <i class="fas fa-calendar-alt mr-2 text-gray-600"></i>
                        میاشتنی راپور
                    </h3>
                    <div class="text-gray-600">
                        <span>ټول ناروغان: {{ $yearlyStats['total'] }}</span>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="table-fixed w-full text-sm">
                        <thead>
                            <tr class="bg-white text-gray-800 border border-gray-800">
                                <th class="py-2 px-4 text-center border-r border-gray-800">میاشت</th>
                                <th class="py-2 px-4 text-center border-r border-gray-800">نارینه</th>
                                <th class="py-2 px-4 text-center border-r border-gray-800">ښځینه</th>
                                <th class="py-2 px-4 text-center">ټول</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $months = ['جنوري', 'فبروري', 'مارچ', 'اپریل', 'می', 'جون', 'جولای', 'اګست', 'سپتمبر', 'اکتوبر', 'نومبر', 'دسمبر'];
                            @endphp
                            
                            @foreach($monthlyStats as $month => $stats)
                                <tr class="border-b hover:bg-gray-100">
                                    <td class="py-2 px-4 text-center border-r border-gray-200">{{ $months[$month-1] }}</td>
                                    <td class="py-2 px-4 text-center border-r border-gray-200">{{ $stats['male'] }}</td>
                                    <td class="py-2 px-4 text-center border-r border-gray-200">{{ $stats['female'] }}</td>
                                    <td class="py-2 px-4 text-center">{{ $stats['total'] }}</td>
                                </tr>
                            @endforeach
                            
                            <tr class="bg-gray-100 font-bold">
                                <td class="py-2 px-4 text-center border-r border-gray-200">ټول کال</td>
                                <td class="py-2 px-4 text-center border-r border-gray-200">{{ $yearlyStats['male'] }}</td>
                                <td class="py-2 px-4 text-center border-r border-gray-200">{{ $yearlyStats['female'] }}</td>
                                <td class="py-2 px-4 text-center">{{ $yearlyStats['total'] }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Patient List -->
            <div>
                <h3 class="text-xl font-bold text-gray-800 pb-2 mb-4 flex items-center border-0">
                    <i class="fas fa-users mr-2 text-gray-600"></i>
                    د ناروغانو لیست
                </h3>

                <div class="overflow-x-auto">
                    <table class="table-fixed w-full text-xs">
                        <thead>
                            <tr class="bg-white text-gray-800 border border-gray-800">
                                <th class="py-2 px-1 text-center w-8 border-r border-gray-800">شمېره</th>
                                <th class="py-2 px-1 text-center w-20 border-r border-gray-800">نوم</th>
                                <th class="py-2 px-1 text-center w-8 border-r border-gray-800">عمر</th>
                                <th class="py-2 px-1 text-center w-12 border-r border-gray-800">جنسیت</th>
                                <th class="py-2 px-1 text-center w-16 border-r border-gray-800">اړیکه</th>
                                <th class="py-2 px-1 text-center w-16 border-r border-gray-800">ولایت</th>
                                <th class="py-2 px-1 text-center w-16 border-r border-gray-800">ولسوالي</th>
                                <th class="py-2 px-1 text-center w-16">کلی</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if(count($patients) > 0)
                                @foreach($patients as $p)
                                    <tr class="border-b hover:bg-gray-100">
                                        <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $loop->iteration }}</td>
                                        <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->Patiet_Name }}</td>
                                        <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->Patient_Age }}</td>
                                        <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->Patient_Gender }}</td>
                                        <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->Patient_phone ?? 'نشته' }}</td>
                                        <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->address->P_Province ?? 'نشته' }}</td>
                                        <td class="py-2 px-1 text-center truncate border-r border-gray-200">{{ $p->address->P_Distract ?? 'نشته' }}</td>
                                        <td class="py-2 px-1 text-center truncate">{{ $p->address->P_Village ?? 'نشته' }}</td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td colspan="8" class="py-6 text-center text-gray-500">هیڅ ناروغ شتون نه لري</td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection





@extends('layouts.admin')

@section('title', 'د اړیکې پیغامونه')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">د اړیکې پیغامونه</h1>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.contacts.export') }}" class="btn btn-success">
                <i class="fas fa-download"></i> ایکسپورټ
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                ټول پیغامونه
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $totalContacts }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-envelope fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">فلټرونه او لټون</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.contacts.index') }}" class="row">
                <div class="col-md-4 mb-3">
                    <label for="search" class="form-label">لټون</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ request('search') }}" placeholder="نوم، ایمیل، شمیره یا پیغام...">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_from" class="form-label">د نیټې څخه</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="{{ request('date_from') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_to" class="form-label">د نیټې پورې</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="{{ request('date_to') }}">
                </div>
                <div class="col-md-2 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> لټون
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Messages Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">د اړیکې پیغامونه</h6>
        </div>
        <div class="card-body">
            @if(session('success'))
                <div class="alert alert-success">{{ session('success') }}</div>
            @endif
            
            @if(session('error'))
                <div class="alert alert-danger">{{ session('error') }}</div>
            @endif

            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>نوم</th>
                            <th>تخلص</th>
                            <th>ایمیل</th>
                            <th>شمیره</th>
                            <th>پیغام</th>
                            <th>یوزر</th>
                            <th>نیټه</th>
                            <th>عملیات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($contacts as $index => $contact)
                            <tr>
                                <td>{{ $contacts->firstItem() + $index }}</td>
                                <td>{{ $contact->fname }}</td>
                                <td>{{ $contact->lname }}</td>
                                <td>{{ $contact->email }}</td>
                                <td>{{ $contact->phone }}</td>
                                <td>{{ Str::limit($contact->message, 50) }}</td>
                                <td>{{ $contact->user ? $contact->user->name : 'میلمه' }}</td>
                                <td>{{ $contact->created_at->format('Y-m-d H:i') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ route('admin.contacts.show', $contact->contact_id) }}" 
                                           class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <form method="POST" 
                                              action="{{ route('admin.contacts.destroy', $contact->contact_id) }}" 
                                              style="display: inline;"
                                              onsubmit="return confirm('ایا تاسو ډاډه یاست چې غواړئ دا پیغام ړنګ کړئ؟')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-sm">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center">هیڅ پیغام ونه موندل شو</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center">
                {{ $contacts->appends(request()->query())->links() }}
            </div>
        </div>
    </div>
</div>

<style>
.table th, .table td {
    text-align: right;
    direction: rtl;
}
.btn-group .btn {
    margin: 0 2px;
}
</style>
@endsection

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class PatientAddModel extends Model
{
    use HasFactory;

    protected $table = 'patient_add_models';

    protected $primaryKey = 'P_Add_Id';

    // Make sure fillable properties match EXACTLY with database column names
    protected $fillable = [
        'Patient_Id',
        'Patient_Country',
        'Patient_Province',
        'Patient_Distract',
        'Patient_Village',
    ];

    // Add logging to help debug
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            Log::info('PatientAddModel saving:', $model->toArray());
        });
        
        static::created(function ($model) {
            Log::info('Created PatientAddModel with ID: ' . $model->P_Add_Id);
        });
    }

    /**
     * Get the patient that owns the address.
     */
    public function patient()
    {
        return $this->belongsTo(PatientModel::class, 'Patient_Id', 'Patient_id');
    }
}











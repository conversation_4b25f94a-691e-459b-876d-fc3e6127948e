{
	"$schema": "vscode://schemas/color-theme",
	"type": "dark",
	"colors": {
		"actionBar.toggledBackground": "#383a49",
		"activityBarBadge.background": "#007acc",
		"checkbox.border": "#6b6b6b",
		"editor.background": "#1e1e1e",
		"editor.foreground": "#d4d4d4",
		"editor.inactiveSelectionBackground": "#3a3d41",
		"editor.selectionHighlightBackground": "#add6ff26",
		"editorIndentGuide.activeBackground1": "#707070",
		"editorIndentGuide.background1": "#404040",
		"input.placeholderForeground": "#a6a6a6",
		"list.activeSelectionIconForeground": "#ffffff",
		"list.dropBackground": "#383b3d",
		"menu.background": "#252526",
		"menu.border": "#454545",
		"menu.foreground": "#cccccc",
		"menu.selectionBackground": "#0078d4",
		"menu.separatorBackground": "#454545",
		"ports.iconRunningProcessForeground": "#369432",
		"sideBarSectionHeader.background": "#00000000",
		"sideBarSectionHeader.border": "#cccccc33",
		"sideBarTitle.foreground": "#bbbbbb",
		"statusBarItem.remoteBackground": "#16825d",
		"statusBarItem.remoteForeground": "#ffffff",
		"tab.lastPinnedBorder": "#cccccc33",
		"tab.selectedBackground": "#222222",
		"tab.selectedForeground": "#ffffffa0",
		"terminal.inactiveSelectionBackground": "#3a3d41",
		"widget.border": "#303031",
		//"activityBar.activeBorder": "#ffffff",
		//"activityBar.background": "#333333",
		//"activityBar.dropBorder": "#ffffff",
		//"activityBar.foreground": "#ffffff",
		//"activityBar.inactiveForeground": "#ffffff66",
		//"activityBarBadge.foreground": "#ffffff",
		//"activityBarTop.activeBorder": "#e7e7e7",
		//"activityBarTop.dropBorder": "#e7e7e7",
		//"activityBarTop.foreground": "#e7e7e7",
		//"activityBarTop.inactiveForeground": "#e7e7e799",
		//"activityErrorBadge.background": "#f14c4c",
		//"activityErrorBadge.foreground": "#000000",
		//"activityWarningBadge.background": "#cca700",
		//"activityWarningBadge.foreground": "#000000",
		//"badge.background": "#4d4d4d",
		//"badge.foreground": "#ffffff",
		//"banner.background": "#04395e",
		//"banner.foreground": "#ffffff",
		//"banner.iconForeground": "#3794ff",
		//"breadcrumb.activeSelectionForeground": "#e0e0e0",
		//"breadcrumb.background": "#1e1e1e",
		//"breadcrumb.focusForeground": "#e0e0e0",
		//"breadcrumb.foreground": "#cccccccc",
		//"breadcrumbPicker.background": "#252526",
		//"button.background": "#0e639c",
		//"button.foreground": "#ffffff",
		//"button.hoverBackground": "#1177bb",
		//"button.secondaryBackground": "#3a3d41",
		//"button.secondaryForeground": "#ffffff",
		//"button.secondaryHoverBackground": "#45494e",
		//"button.separator": "#ffffff66",
		//"chart.axis": "#bfbfbf66",
		//"chart.guide": "#bfbfbf33",
		//"chart.line": "#236b8e",
		//"charts.blue": "#3794ff",
		//"charts.foreground": "#cccccc",
		//"charts.green": "#89d185",
		//"charts.lines": "#cccccc80",
		//"charts.orange": "#d18616",
		//"charts.purple": "#b180d7",
		//"charts.red": "#f14c4c",
		//"charts.yellow": "#cca700",
		//"chat.avatarBackground": "#1f1f1f",
		//"chat.avatarForeground": "#cccccc",
		//"chat.editedFileForeground": "#e2c08d",
		//"chat.linesAddedForeground": "#54b054",
		//"chat.linesRemovedForeground": "#fc6a6a",
		//"chat.requestBackground": "#1e1e1e9e",
		//"chat.requestBorder": "#ffffff1a",
		//"chat.requestBubbleBackground": "#264f784d",
		//"chat.requestCodeBorder": "#004972b8",
		//"chat.slashCommandBackground": "#26477866",
		//"chat.slashCommandForeground": "#85b6ff",
		//"checkbox.background": "#3c3c3c",
		//"checkbox.disabled.background": "#777777",
		//"checkbox.disabled.foreground": "#b4b4b4",
		//"checkbox.foreground": "#f0f0f0",
		//"checkbox.selectBackground": "#252526",
		//"checkbox.selectBorder": "#c5c5c5",
		//"commandCenter.activeBackground": "#ffffff14",
		//"commandCenter.activeBorder": "#cccccc4d",
		//"commandCenter.activeForeground": "#cccccc",
		//"commandCenter.background": "#ffffff0d",
		//"commandCenter.border": "#cccccc33",
		//"commandCenter.debuggingBackground": "#cc663342",
		//"commandCenter.foreground": "#cccccc",
		//"commandCenter.inactiveBorder": "#cccccc26",
		//"commandCenter.inactiveForeground": "#cccccc99",
		//"commentsView.resolvedIcon": "#cccccc80",
		//"commentsView.unresolvedIcon": "#007fd4",
		//"debugConsole.errorForeground": "#f48771",
		//"debugConsole.infoForeground": "#3794ff",
		//"debugConsole.sourceForeground": "#cccccc",
		//"debugConsole.warningForeground": "#cca700",
		//"debugConsoleInputIcon.foreground": "#cccccc",
		//"debugExceptionWidget.background": "#420b0d",
		//"debugExceptionWidget.border": "#a31515",
		//"debugIcon.breakpointCurrentStackframeForeground": "#ffcc00",
		//"debugIcon.breakpointDisabledForeground": "#848484",
		//"debugIcon.breakpointForeground": "#e51400",
		//"debugIcon.breakpointStackframeForeground": "#89d185",
		//"debugIcon.breakpointUnverifiedForeground": "#848484",
		//"debugIcon.continueForeground": "#75beff",
		//"debugIcon.disconnectForeground": "#f48771",
		//"debugIcon.pauseForeground": "#75beff",
		//"debugIcon.restartForeground": "#89d185",
		//"debugIcon.startForeground": "#89d185",
		//"debugIcon.stepBackForeground": "#75beff",
		//"debugIcon.stepIntoForeground": "#75beff",
		//"debugIcon.stepOutForeground": "#75beff",
		//"debugIcon.stepOverForeground": "#75beff",
		//"debugIcon.stopForeground": "#f48771",
		//"debugTokenExpression.boolean": "#4e94ce",
		//"debugTokenExpression.error": "#f48771",
		//"debugTokenExpression.name": "#c586c0",
		//"debugTokenExpression.number": "#b5cea8",
		//"debugTokenExpression.string": "#ce9178",
		//"debugTokenExpression.type": "#4a90e2",
		//"debugTokenExpression.value": "#cccccc99",
		//"debugToolBar.background": "#333333",
		//"debugView.exceptionLabelBackground": "#6c2022",
		//"debugView.exceptionLabelForeground": "#cccccc",
		//"debugView.stateLabelBackground": "#88888844",
		//"debugView.stateLabelForeground": "#cccccc",
		//"debugView.valueChangedHighlight": "#569cd6",
		//"descriptionForeground": "#ccccccb3",
		//"diffEditor.diagonalFill": "#cccccc33",
		//"diffEditor.insertedLineBackground": "#9bb95533",
		//"diffEditor.insertedTextBackground": "#9ccc2c33",
		//"diffEditor.move.border": "#8b8b8b9c",
		//"diffEditor.moveActive.border": "#ffa500",
		//"diffEditor.removedLineBackground": "#ff000033",
		//"diffEditor.removedTextBackground": "#ff000033",
		//"diffEditor.unchangedCodeBackground": "#74747429",
		//"diffEditor.unchangedRegionBackground": "#252526",
		//"diffEditor.unchangedRegionForeground": "#cccccc",
		//"diffEditor.unchangedRegionShadow": "#000000",
		//"disabledForeground": "#cccccc80",
		//"dropdown.background": "#3c3c3c",
		//"dropdown.border": "#3c3c3c",
		//"dropdown.foreground": "#f0f0f0",
		//"editor.compositionBorder": "#ffffff",
		//"editor.findMatchBackground": "#515c6a",
		//"editor.findMatchHighlightBackground": "#ea5c0055",
		//"editor.findRangeHighlightBackground": "#3a3d4166",
		//"editor.focusedStackFrameHighlightBackground": "#7abd7a4d",
		//"editor.foldBackground": "#264f784d",
		//"editor.foldPlaceholderForeground": "#808080",
		//"editor.hoverHighlightBackground": "#264f7840",
		//"editor.inlineValuesBackground": "#ffc80033",
		//"editor.inlineValuesForeground": "#ffffff80",
		//"editor.lineHighlightBorder": "#282828",
		//"editor.linkedEditingBackground": "#ff00004d",
		//"editor.placeholder.foreground": "#ffffff56",
		//"editor.rangeHighlightBackground": "#ffffff0b",
		//"editor.selectionBackground": "#264f78",
		//"editor.snippetFinalTabstopHighlightBorder": "#525252",
		//"editor.snippetTabstopHighlightBackground": "#7c7c7c4d",
		//"editor.stackFrameHighlightBackground": "#ffff0033",
		//"editor.symbolHighlightBackground": "#ea5c0055",
		//"editor.wordHighlightBackground": "#575757b8",
		//"editor.wordHighlightStrongBackground": "#004972b8",
		//"editor.wordHighlightTextBackground": "#575757b8",
		//"editorActionList.background": "#252526",
		//"editorActionList.focusBackground": "#04395e",
		//"editorActionList.focusForeground": "#ffffff",
		//"editorActionList.foreground": "#cccccc",
		//"editorBracketHighlight.foreground1": "#ffd700",
		//"editorBracketHighlight.foreground2": "#da70d6",
		//"editorBracketHighlight.foreground3": "#179fff",
		//"editorBracketHighlight.foreground4": "#00000000",
		//"editorBracketHighlight.foreground5": "#00000000",
		//"editorBracketHighlight.foreground6": "#00000000",
		//"editorBracketHighlight.unexpectedBracket.foreground": "#ff1212cc",
		//"editorBracketMatch.background": "#0064001a",
		//"editorBracketMatch.border": "#888888",
		//"editorBracketPairGuide.activeBackground1": "#00000000",
		//"editorBracketPairGuide.activeBackground2": "#00000000",
		//"editorBracketPairGuide.activeBackground3": "#00000000",
		//"editorBracketPairGuide.activeBackground4": "#00000000",
		//"editorBracketPairGuide.activeBackground5": "#00000000",
		//"editorBracketPairGuide.activeBackground6": "#00000000",
		//"editorBracketPairGuide.background1": "#00000000",
		//"editorBracketPairGuide.background2": "#00000000",
		//"editorBracketPairGuide.background3": "#00000000",
		//"editorBracketPairGuide.background4": "#00000000",
		//"editorBracketPairGuide.background5": "#00000000",
		//"editorBracketPairGuide.background6": "#00000000",
		//"editorCodeLens.foreground": "#999999",
		//"editorCommentsWidget.rangeActiveBackground": "#007fd41a",
		//"editorCommentsWidget.rangeBackground": "#007fd41a",
		//"editorCommentsWidget.replyInputBackground": "#252526",
		//"editorCommentsWidget.resolvedBorder": "#cccccc80",
		//"editorCommentsWidget.unresolvedBorder": "#007fd4",
		//"editorCursor.foreground": "#aeafad",
		//"editorError.foreground": "#f14c4c",
		//"editorGhostText.foreground": "#ffffff56",
		//"editorGroup.border": "#444444",
		//"editorGroup.dropBackground": "#53595d80",
		//"editorGroup.dropIntoPromptBackground": "#252526",
		//"editorGroup.dropIntoPromptForeground": "#cccccc",
		//"editorGroupHeader.noTabsBackground": "#1e1e1e",
		//"editorGroupHeader.tabsBackground": "#252526",
		//"editorGutter.addedBackground": "#487e02",
		//"editorGutter.addedSecondaryBackground": "#243f01",
		//"editorGutter.background": "#1e1e1e",
		//"editorGutter.commentGlyphForeground": "#d4d4d4",
		//"editorGutter.commentRangeForeground": "#37373d",
		//"editorGutter.commentUnresolvedGlyphForeground": "#d4d4d4",
		//"editorGutter.deletedBackground": "#f14c4c",
		//"editorGutter.deletedSecondaryBackground": "#b00e0e",
		//"editorGutter.foldingControlForeground": "#c5c5c5",
		//"editorGutter.itemBackground": "#37373d",
		//"editorGutter.itemGlyphForeground": "#d4d4d4",
		//"editorGutter.modifiedBackground": "#1b81a8",
		//"editorGutter.modifiedSecondaryBackground": "#0d4054",
		//"editorHint.foreground": "#eeeeeeb3",
		//"editorHoverWidget.background": "#252526",
		//"editorHoverWidget.border": "#454545",
		//"editorHoverWidget.foreground": "#cccccc",
		//"editorHoverWidget.highlightForeground": "#2aaaff",
		//"editorHoverWidget.statusBarBackground": "#2c2c2d",
		//"editorIndentGuide.activeBackground2": "#00000000",
		//"editorIndentGuide.activeBackground3": "#00000000",
		//"editorIndentGuide.activeBackground4": "#00000000",
		//"editorIndentGuide.activeBackground5": "#00000000",
		//"editorIndentGuide.activeBackground6": "#00000000",
		//"editorIndentGuide.background2": "#00000000",
		//"editorIndentGuide.background3": "#00000000",
		//"editorIndentGuide.background4": "#00000000",
		//"editorIndentGuide.background5": "#00000000",
		//"editorIndentGuide.background6": "#00000000",
		//"editorInfo.foreground": "#3794ff",
		//"editorInlayHint.background": "#4d4d4d1a",
		//"editorInlayHint.foreground": "#969696",
		//"editorInlayHint.parameterBackground": "#4d4d4d1a",
		//"editorInlayHint.parameterForeground": "#969696",
		//"editorInlayHint.typeBackground": "#4d4d4d1a",
		//"editorInlayHint.typeForeground": "#969696",
		//"editorLightBulb.foreground": "#ffcc00",
		//"editorLightBulbAi.foreground": "#ffcc00",
		//"editorLightBulbAutoFix.foreground": "#75beff",
		//"editorLineNumber.activeForeground": "#c6c6c6",
		//"editorLineNumber.foreground": "#858585",
		//"editorLink.activeForeground": "#4e94ce",
		//"editorMarkerNavigation.background": "#1e1e1e",
		//"editorMarkerNavigationError.background": "#f14c4c",
		//"editorMarkerNavigationError.headerBackground": "#f14c4c1a",
		//"editorMarkerNavigationInfo.background": "#3794ff",
		//"editorMarkerNavigationInfo.headerBackground": "#3794ff1a",
		//"editorMarkerNavigationWarning.background": "#cca700",
		//"editorMarkerNavigationWarning.headerBackground": "#cca7001a",
		//"editorMinimap.inlineChatInserted": "#9ccc2c1f",
		//"editorMultiCursor.primary.foreground": "#aeafad",
		//"editorMultiCursor.secondary.foreground": "#aeafad",
		//"editorOverviewRuler.addedForeground": "#487e0299",
		//"editorOverviewRuler.border": "#7f7f7f4d",
		//"editorOverviewRuler.bracketMatchForeground": "#a0a0a0",
		//"editorOverviewRuler.commentForeground": "#37373d",
		//"editorOverviewRuler.commentUnresolvedForeground": "#37373d",
		//"editorOverviewRuler.commonContentForeground": "#60606066",
		//"editorOverviewRuler.currentContentForeground": "#40c8ae80",
		//"editorOverviewRuler.deletedForeground": "#f14c4c99",
		//"editorOverviewRuler.errorForeground": "#ff1212b3",
		//"editorOverviewRuler.findMatchForeground": "#d186167e",
		//"editorOverviewRuler.incomingContentForeground": "#40a6ff80",
		//"editorOverviewRuler.infoForeground": "#3794ff",
		//"editorOverviewRuler.inlineChatInserted": "#9ccc2c1f",
		//"editorOverviewRuler.inlineChatRemoved": "#ff00001f",
		//"editorOverviewRuler.modifiedForeground": "#1b81a899",
		//"editorOverviewRuler.rangeHighlightForeground": "#007acc99",
		//"editorOverviewRuler.selectionHighlightForeground": "#a0a0a0cc",
		//"editorOverviewRuler.warningForeground": "#cca700",
		//"editorOverviewRuler.wordHighlightForeground": "#a0a0a0cc",
		//"editorOverviewRuler.wordHighlightStrongForeground": "#c0a0c0cc",
		//"editorOverviewRuler.wordHighlightTextForeground": "#a0a0a0cc",
		//"editorPane.background": "#1e1e1e",
		//"editorRuler.foreground": "#5a5a5a",
		//"editorStickyScroll.background": "#1e1e1e",
		//"editorStickyScroll.shadow": "#000000",
		//"editorStickyScrollHover.background": "#2a2d2e",
		//"editorSuggestWidget.background": "#252526",
		//"editorSuggestWidget.border": "#454545",
		//"editorSuggestWidget.focusHighlightForeground": "#2aaaff",
		//"editorSuggestWidget.foreground": "#d4d4d4",
		//"editorSuggestWidget.highlightForeground": "#2aaaff",
		//"editorSuggestWidget.selectedBackground": "#04395e",
		//"editorSuggestWidget.selectedForeground": "#ffffff",
		//"editorSuggestWidget.selectedIconForeground": "#ffffff",
		//"editorSuggestWidgetStatus.foreground": "#d4d4d480",
		//"editorUnicodeHighlight.border": "#cca700",
		//"editorUnnecessaryCode.opacity": "#000000aa",
		//"editorWarning.foreground": "#cca700",
		//"editorWatermark.foreground": "#d4d4d499",
		//"editorWhitespace.foreground": "#e3e4e229",
		//"editorWidget.background": "#252526",
		//"editorWidget.border": "#454545",
		//"editorWidget.foreground": "#cccccc",
		//"errorForeground": "#f48771",
		//"extensionBadge.remoteBackground": "#007acc",
		//"extensionBadge.remoteForeground": "#ffffff",
		//"extensionButton.background": "#0e639c",
		//"extensionButton.foreground": "#ffffff",
		//"extensionButton.hoverBackground": "#1177bb",
		//"extensionButton.prominentBackground": "#0e639c",
		//"extensionButton.prominentForeground": "#ffffff",
		//"extensionButton.prominentHoverBackground": "#1177bb",
		//"extensionButton.separator": "#ffffff66",
		//"extensionIcon.preReleaseForeground": "#1d9271",
		//"extensionIcon.privateForeground": "#ffffff60",
		//"extensionIcon.sponsorForeground": "#d758b3",
		//"extensionIcon.starForeground": "#ff8e00",
		//"extensionIcon.verifiedForeground": "#3794ff",
		//"focusBorder": "#007fd4",
		//"foreground": "#cccccc",
		//"gauge.background": "#007acc",
		//"gauge.errorBackground": "#be1100",
		//"gauge.errorForeground": "#be11004d",
		//"gauge.foreground": "#007acc4d",
		//"gauge.warningBackground": "#b89500",
		//"gauge.warningForeground": "#b895004d",
		//"git.blame.editorDecorationForeground": "#969696",
		//"gitDecoration.addedResourceForeground": "#81b88b",
		//"gitDecoration.conflictingResourceForeground": "#e4676b",
		//"gitDecoration.deletedResourceForeground": "#c74e39",
		//"gitDecoration.ignoredResourceForeground": "#8c8c8c",
		//"gitDecoration.modifiedResourceForeground": "#e2c08d",
		//"gitDecoration.renamedResourceForeground": "#73c991",
		//"gitDecoration.stageDeletedResourceForeground": "#c74e39",
		//"gitDecoration.stageModifiedResourceForeground": "#e2c08d",
		//"gitDecoration.submoduleResourceForeground": "#8db9e2",
		//"gitDecoration.untrackedResourceForeground": "#73c991",
		//"icon.foreground": "#c5c5c5",
		//"inlineChat.background": "#252526",
		//"inlineChat.border": "#454545",
		//"inlineChat.foreground": "#cccccc",
		//"inlineChat.shadow": "#0000005c",
		//"inlineChatDiff.inserted": "#9ccc2c1a",
		//"inlineChatDiff.removed": "#ff00001a",
		//"inlineChatInput.background": "#3c3c3c",
		//"inlineChatInput.border": "#454545",
		//"inlineChatInput.focusBorder": "#007fd4",
		//"inlineChatInput.placeholderForeground": "#a6a6a6",
		//"inlineEdit.gutterIndicator.background": "#2d2d2d80",
		//"inlineEdit.gutterIndicator.primaryBackground": "#0e639c66",
		//"inlineEdit.gutterIndicator.primaryBorder": "#0e639c",
		//"inlineEdit.gutterIndicator.primaryForeground": "#ffffff",
		//"inlineEdit.gutterIndicator.secondaryBackground": "#3a3d41",
		//"inlineEdit.gutterIndicator.secondaryBorder": "#3a3d41",
		//"inlineEdit.gutterIndicator.secondaryForeground": "#ffffff",
		//"inlineEdit.gutterIndicator.successfulBackground": "#0e639c",
		//"inlineEdit.gutterIndicator.successfulBorder": "#0e639c",
		//"inlineEdit.gutterIndicator.successfulForeground": "#ffffff",
		//"inlineEdit.modifiedBackground": "#9ccc2c0f",
		//"inlineEdit.modifiedBorder": "#9ccc2c33",
		//"inlineEdit.modifiedChangedLineBackground": "#9bb95524",
		//"inlineEdit.modifiedChangedTextBackground": "#9ccc2c24",
		//"inlineEdit.originalBackground": "#ff00000a",
		//"inlineEdit.originalBorder": "#ff000033",
		//"inlineEdit.originalChangedLineBackground": "#ff000029",
		//"inlineEdit.originalChangedTextBackground": "#ff000029",
		//"inlineEdit.tabWillAcceptModifiedBorder": "#9ccc2c33",
		//"inlineEdit.tabWillAcceptOriginalBorder": "#ff000033",
		//"input.background": "#3c3c3c",
		//"input.foreground": "#cccccc",
		//"inputOption.activeBackground": "#007fd466",
		//"inputOption.activeBorder": "#007acc",
		//"inputOption.activeForeground": "#ffffff",
		//"inputOption.hoverBackground": "#5a5d5e80",
		//"inputValidation.errorBackground": "#5a1d1d",
		//"inputValidation.errorBorder": "#be1100",
		//"inputValidation.infoBackground": "#063b49",
		//"inputValidation.infoBorder": "#007acc",
		//"inputValidation.warningBackground": "#352a05",
		//"inputValidation.warningBorder": "#b89500",
		//"interactive.activeCodeBorder": "#007acc",
		//"interactive.inactiveCodeBorder": "#37373d",
		//"keybindingLabel.background": "#8080802b",
		//"keybindingLabel.border": "#33333399",
		//"keybindingLabel.bottomBorder": "#44444499",
		//"keybindingLabel.foreground": "#cccccc",
		//"keybindingTable.headerBackground": "#cccccc0a",
		//"keybindingTable.rowsBackground": "#cccccc0a",
		//"list.activeSelectionBackground": "#04395e",
		//"list.activeSelectionForeground": "#ffffff",
		//"list.deemphasizedForeground": "#8c8c8c",
		//"list.dropBetweenBackground": "#c5c5c5",
		//"list.errorForeground": "#f88070",
		//"list.filterMatchBackground": "#ea5c0055",
		//"list.focusHighlightForeground": "#2aaaff",
		//"list.focusOutline": "#007fd4",
		//"list.highlightForeground": "#2aaaff",
		//"list.hoverBackground": "#2a2d2e",
		//"list.inactiveSelectionBackground": "#37373d",
		//"list.invalidItemForeground": "#b89500",
		//"list.warningForeground": "#cca700",
		//"listFilterWidget.background": "#252526",
		//"listFilterWidget.noMatchesOutline": "#be1100",
		//"listFilterWidget.outline": "#00000000",
		//"listFilterWidget.shadow": "#0000005c",
		//"markdown.extension.editor.codeSpan.background": "#00000000",
		//"markdown.extension.editor.codeSpan.border": "#264f78",
		//"markdown.extension.editor.formattingMark.foreground": "#e3e4e229",
		//"markdown.extension.editor.trailingSpace.background": "#cccccc33",
		//"menu.selectionForeground": "#ffffff",
		//"menubar.selectionBackground": "#5a5d5e50",
		//"menubar.selectionForeground": "#cccccc",
		//"merge.commonContentBackground": "#60606029",
		//"merge.commonHeaderBackground": "#60606066",
		//"merge.currentContentBackground": "#40c8ae33",
		//"merge.currentHeaderBackground": "#40c8ae80",
		//"merge.incomingContentBackground": "#40a6ff33",
		//"merge.incomingHeaderBackground": "#40a6ff80",
		//"mergeEditor.change.background": "#9bb95533",
		//"mergeEditor.change.word.background": "#9ccc2c33",
		//"mergeEditor.changeBase.background": "#4b1818",
		//"mergeEditor.changeBase.word.background": "#6f1313",
		//"mergeEditor.conflict.handled.minimapOverViewRuler": "#adaca8ee",
		//"mergeEditor.conflict.handledFocused.border": "#c1c1c1cc",
		//"mergeEditor.conflict.handledUnfocused.border": "#86868649",
		//"mergeEditor.conflict.input1.background": "#40c8ae33",
		//"mergeEditor.conflict.input2.background": "#40a6ff33",
		//"mergeEditor.conflict.unhandled.minimapOverViewRuler": "#fcba03",
		//"mergeEditor.conflict.unhandledFocused.border": "#ffa600",
		//"mergeEditor.conflict.unhandledUnfocused.border": "#ffa6007a",
		//"mergeEditor.conflictingLines.background": "#ffea0047",
		//"minimap.chatEditHighlight": "#1e1e1e99",
		//"minimap.errorHighlight": "#ff1212b3",
		//"minimap.findMatchHighlight": "#d18616",
		//"minimap.foregroundOpacity": "#000000",
		//"minimap.infoHighlight": "#3794ff",
		//"minimap.selectionHighlight": "#264f78",
		//"minimap.selectionOccurrenceHighlight": "#676767",
		//"minimap.warningHighlight": "#cca700",
		//"minimapGutter.addedBackground": "#487e02",
		//"minimapGutter.deletedBackground": "#f14c4c",
		//"minimapGutter.modifiedBackground": "#1b81a8",
		//"minimapSlider.activeBackground": "#bfbfbf33",
		//"minimapSlider.background": "#79797933",
		//"minimapSlider.hoverBackground": "#64646459",
		//"multiDiffEditor.background": "#1e1e1e",
		//"multiDiffEditor.border": "#cccccc33",
		//"multiDiffEditor.headerBackground": "#262626",
		//"notebook.cellBorderColor": "#37373d",
		//"notebook.cellEditorBackground": "#252526",
		//"notebook.cellInsertionIndicator": "#007fd4",
		//"notebook.cellStatusBarItemHoverBackground": "#ffffff26",
		//"notebook.cellToolbarSeparator": "#80808059",
		//"notebook.editorBackground": "#1e1e1e",
		//"notebook.focusedCellBorder": "#007fd4",
		//"notebook.focusedEditorBorder": "#007fd4",
		//"notebook.inactiveFocusedCellBorder": "#37373d",
		//"notebook.selectedCellBackground": "#37373d",
		//"notebook.selectedCellBorder": "#37373d",
		//"notebook.symbolHighlightBackground": "#ffffff0b",
		//"notebookEditorOverviewRuler.runningCellForeground": "#89d185",
		//"notebookScrollbarSlider.activeBackground": "#bfbfbf66",
		//"notebookScrollbarSlider.background": "#79797966",
		//"notebookScrollbarSlider.hoverBackground": "#646464b3",
		//"notebookStatusErrorIcon.foreground": "#f48771",
		//"notebookStatusRunningIcon.foreground": "#cccccc",
		//"notebookStatusSuccessIcon.foreground": "#89d185",
		//"notificationCenter.border": "#303031",
		//"notificationCenterHeader.background": "#303031",
		//"notificationLink.foreground": "#3794ff",
		//"notificationToast.border": "#303031",
		//"notifications.background": "#252526",
		//"notifications.border": "#303031",
		//"notifications.foreground": "#cccccc",
		//"notificationsErrorIcon.foreground": "#f14c4c",
		//"notificationsInfoIcon.foreground": "#3794ff",
		//"notificationsWarningIcon.foreground": "#cca700",
		//"panel.background": "#1e1e1e",
		//"panel.border": "#80808059",
		//"panel.dropBorder": "#e7e7e7",
		//"panelSection.border": "#80808059",
		//"panelSection.dropBackground": "#53595d80",
		//"panelSectionHeader.background": "#80808033",
		//"panelStickyScroll.background": "#1e1e1e",
		//"panelStickyScroll.shadow": "#000000",
		//"panelTitle.activeBorder": "#e7e7e7",
		//"panelTitle.activeForeground": "#e7e7e7",
		//"panelTitle.inactiveForeground": "#e7e7e799",
		//"panelTitleBadge.background": "#007acc",
		//"panelTitleBadge.foreground": "#ffffff",
		//"peekView.border": "#3794ff",
		//"peekViewEditor.background": "#001f33",
		//"peekViewEditor.matchHighlightBackground": "#ff8f0099",
		//"peekViewEditorGutter.background": "#001f33",
		//"peekViewEditorStickyScroll.background": "#001f33",
		//"peekViewResult.background": "#252526",
		//"peekViewResult.fileForeground": "#ffffff",
		//"peekViewResult.lineForeground": "#bbbbbb",
		//"peekViewResult.matchHighlightBackground": "#ea5c004d",
		//"peekViewResult.selectionBackground": "#3399ff33",
		//"peekViewResult.selectionForeground": "#ffffff",
		//"peekViewTitle.background": "#252526",
		//"peekViewTitleDescription.foreground": "#ccccccb3",
		//"peekViewTitleLabel.foreground": "#ffffff",
		//"pickerGroup.border": "#3f3f46",
		//"pickerGroup.foreground": "#3794ff",
		//"problemsErrorIcon.foreground": "#f14c4c",
		//"problemsInfoIcon.foreground": "#3794ff",
		//"problemsWarningIcon.foreground": "#cca700",
		//"profileBadge.background": "#4d4d4d",
		//"profileBadge.foreground": "#ffffff",
		//"profiles.sashBorder": "#80808059",
		//"progressBar.background": "#0e70c0",
		//"quickInput.background": "#252526",
		//"quickInput.foreground": "#cccccc",
		//"quickInputList.focusBackground": "#04395e",
		//"quickInputList.focusForeground": "#ffffff",
		//"quickInputList.focusIconForeground": "#ffffff",
		//"quickInputTitle.background": "#ffffff1b",
		//"radio.activeBackground": "#007fd466",
		//"radio.activeBorder": "#007acc",
		//"radio.activeForeground": "#ffffff",
		//"radio.inactiveBorder": "#ffffff33",
		//"radio.inactiveHoverBackground": "#5a5d5e80",
		//"sash.hoverBorder": "#007fd4",
		//"scmGraph.foreground1": "#ffb000",
		//"scmGraph.foreground2": "#dc267f",
		//"scmGraph.foreground3": "#994f00",
		//"scmGraph.foreground4": "#40b0a6",
		//"scmGraph.foreground5": "#b66dff",
		//"scmGraph.historyItemBaseRefColor": "#ea5c00",
		//"scmGraph.historyItemHoverAdditionsForeground": "#81b88b",
		//"scmGraph.historyItemHoverDefaultLabelBackground": "#4d4d4d",
		//"scmGraph.historyItemHoverDefaultLabelForeground": "#cccccc",
		//"scmGraph.historyItemHoverDeletionsForeground": "#c74e39",
		//"scmGraph.historyItemHoverLabelForeground": "#ffffff",
		//"scmGraph.historyItemRefColor": "#3794ff",
		//"scmGraph.historyItemRemoteRefColor": "#b180d7",
		//"scrollbar.shadow": "#000000",
		//"scrollbarSlider.activeBackground": "#bfbfbf66",
		//"scrollbarSlider.background": "#79797966",
		//"scrollbarSlider.hoverBackground": "#646464b3",
		//"search.resultsInfoForeground": "#cccccca6",
		//"searchEditor.findMatchBackground": "#ea5c0038",
		//"settings.checkboxBackground": "#3c3c3c",
		//"settings.checkboxBorder": "#6b6b6b",
		//"settings.checkboxForeground": "#f0f0f0",
		//"settings.dropdownBackground": "#3c3c3c",
		//"settings.dropdownBorder": "#3c3c3c",
		//"settings.dropdownForeground": "#f0f0f0",
		//"settings.dropdownListBorder": "#454545",
		//"settings.focusedRowBackground": "#2a2d2e99",
		//"settings.focusedRowBorder": "#007fd4",
		//"settings.headerBorder": "#80808059",
		//"settings.headerForeground": "#e7e7e7",
		//"settings.modifiedItemIndicator": "#0c7d9d",
		//"settings.numberInputBackground": "#3c3c3c",
		//"settings.numberInputForeground": "#cccccc",
		//"settings.rowHoverBackground": "#2a2d2e4d",
		//"settings.sashBorder": "#80808059",
		//"settings.settingsHeaderHoverForeground": "#e7e7e7b3",
		//"settings.textInputBackground": "#3c3c3c",
		//"settings.textInputForeground": "#cccccc",
		//"sideBar.background": "#252526",
		//"sideBar.dropBackground": "#53595d80",
		//"sideBarActivityBarTop.border": "#cccccc33",
		//"sideBarStickyScroll.background": "#252526",
		//"sideBarStickyScroll.shadow": "#000000",
		//"sideBarTitle.background": "#252526",
		//"sideBySideEditor.horizontalBorder": "#444444",
		//"sideBySideEditor.verticalBorder": "#444444",
		//"simpleFindWidget.sashBorder": "#454545",
		//"statusBar.background": "#007acc",
		//"statusBar.debuggingBackground": "#cc6633",
		//"statusBar.debuggingForeground": "#ffffff",
		//"statusBar.focusBorder": "#ffffff",
		//"statusBar.foreground": "#ffffff",
		//"statusBar.noFolderBackground": "#68217a",
		//"statusBar.noFolderForeground": "#ffffff",
		//"statusBarItem.activeBackground": "#ffffff2e",
		//"statusBarItem.compactHoverBackground": "#ffffff33",
		//"statusBarItem.errorBackground": "#c72e0f",
		//"statusBarItem.errorForeground": "#ffffff",
		//"statusBarItem.errorHoverBackground": "#ffffff1f",
		//"statusBarItem.errorHoverForeground": "#ffffff",
		//"statusBarItem.focusBorder": "#ffffff",
		//"statusBarItem.hoverBackground": "#ffffff1f",
		//"statusBarItem.hoverForeground": "#ffffff",
		//"statusBarItem.offlineBackground": "#6c1717",
		//"statusBarItem.offlineForeground": "#ffffff",
		//"statusBarItem.offlineHoverBackground": "#ffffff1f",
		//"statusBarItem.offlineHoverForeground": "#ffffff",
		//"statusBarItem.prominentBackground": "#00000080",
		//"statusBarItem.prominentForeground": "#ffffff",
		//"statusBarItem.prominentHoverBackground": "#ffffff1f",
		//"statusBarItem.prominentHoverForeground": "#ffffff",
		//"statusBarItem.remoteHoverBackground": "#ffffff1f",
		//"statusBarItem.remoteHoverForeground": "#ffffff",
		//"statusBarItem.warningBackground": "#7a6400",
		//"statusBarItem.warningForeground": "#ffffff",
		//"statusBarItem.warningHoverBackground": "#ffffff1f",
		//"statusBarItem.warningHoverForeground": "#ffffff",
		//"symbolIcon.arrayForeground": "#cccccc",
		//"symbolIcon.booleanForeground": "#cccccc",
		//"symbolIcon.classForeground": "#ee9d28",
		//"symbolIcon.colorForeground": "#cccccc",
		//"symbolIcon.constantForeground": "#cccccc",
		//"symbolIcon.constructorForeground": "#b180d7",
		//"symbolIcon.enumeratorForeground": "#ee9d28",
		//"symbolIcon.enumeratorMemberForeground": "#75beff",
		//"symbolIcon.eventForeground": "#ee9d28",
		//"symbolIcon.fieldForeground": "#75beff",
		//"symbolIcon.fileForeground": "#cccccc",
		//"symbolIcon.folderForeground": "#cccccc",
		//"symbolIcon.functionForeground": "#b180d7",
		//"symbolIcon.interfaceForeground": "#75beff",
		//"symbolIcon.keyForeground": "#cccccc",
		//"symbolIcon.keywordForeground": "#cccccc",
		//"symbolIcon.methodForeground": "#b180d7",
		//"symbolIcon.moduleForeground": "#cccccc",
		//"symbolIcon.namespaceForeground": "#cccccc",
		//"symbolIcon.nullForeground": "#cccccc",
		//"symbolIcon.numberForeground": "#cccccc",
		//"symbolIcon.objectForeground": "#cccccc",
		//"symbolIcon.operatorForeground": "#cccccc",
		//"symbolIcon.packageForeground": "#cccccc",
		//"symbolIcon.propertyForeground": "#cccccc",
		//"symbolIcon.referenceForeground": "#cccccc",
		//"symbolIcon.snippetForeground": "#cccccc",
		//"symbolIcon.stringForeground": "#cccccc",
		//"symbolIcon.structForeground": "#cccccc",
		//"symbolIcon.textForeground": "#cccccc",
		//"symbolIcon.typeParameterForeground": "#cccccc",
		//"symbolIcon.unitForeground": "#cccccc",
		//"symbolIcon.variableForeground": "#75beff",
		//"tab.activeBackground": "#1e1e1e",
		//"tab.activeForeground": "#ffffff",
		//"tab.activeModifiedBorder": "#3399cc",
		//"tab.border": "#252526",
		//"tab.dragAndDropBorder": "#ffffff",
		//"tab.inactiveBackground": "#2d2d2d",
		//"tab.inactiveForeground": "#ffffff80",
		//"tab.inactiveModifiedBorder": "#3399cc80",
		//"tab.unfocusedActiveBackground": "#1e1e1e",
		//"tab.unfocusedActiveForeground": "#ffffff80",
		//"tab.unfocusedActiveModifiedBorder": "#3399cc80",
		//"tab.unfocusedInactiveBackground": "#2d2d2d",
		//"tab.unfocusedInactiveForeground": "#ffffff40",
		//"tab.unfocusedInactiveModifiedBorder": "#3399cc40",
		//"terminal.ansiBlack": "#000000",
		//"terminal.ansiBlue": "#2472c8",
		//"terminal.ansiBrightBlack": "#666666",
		//"terminal.ansiBrightBlue": "#3b8eea",
		//"terminal.ansiBrightCyan": "#29b8db",
		//"terminal.ansiBrightGreen": "#23d18b",
		//"terminal.ansiBrightMagenta": "#d670d6",
		//"terminal.ansiBrightRed": "#f14c4c",
		//"terminal.ansiBrightWhite": "#e5e5e5",
		//"terminal.ansiBrightYellow": "#f5f543",
		//"terminal.ansiCyan": "#11a8cd",
		//"terminal.ansiGreen": "#0dbc79",
		//"terminal.ansiMagenta": "#bc3fbc",
		//"terminal.ansiRed": "#cd3131",
		//"terminal.ansiWhite": "#e5e5e5",
		//"terminal.ansiYellow": "#e5e510",
		//"terminal.border": "#80808059",
		//"terminal.dropBackground": "#53595d80",
		//"terminal.findMatchBackground": "#515c6a",
		//"terminal.findMatchHighlightBackground": "#ea5c0055",
		//"terminal.foreground": "#cccccc",
		//"terminal.hoverHighlightBackground": "#264f7820",
		//"terminal.initialHintForeground": "#ffffff56",
		//"terminal.selectionBackground": "#264f78",
		//"terminalCommandDecoration.defaultBackground": "#ffffff40",
		//"terminalCommandDecoration.errorBackground": "#f14c4c",
		//"terminalCommandDecoration.successBackground": "#1b81a8",
		//"terminalCommandGuide.foreground": "#37373d",
		//"terminalOverviewRuler.border": "#7f7f7f4d",
		//"terminalOverviewRuler.cursorForeground": "#a0a0a0cc",
		//"terminalOverviewRuler.findMatchForeground": "#d186167e",
		//"terminalStickyScrollHover.background": "#2a2d2e",
		//"terminalSymbolIcon.aliasForeground": "#b180d7",
		//"terminalSymbolIcon.argumentForeground": "#75beff",
		//"terminalSymbolIcon.fileForeground": "#cccccc",
		//"terminalSymbolIcon.flagForeground": "#ee9d28",
		//"terminalSymbolIcon.folderForeground": "#cccccc",
		//"terminalSymbolIcon.methodForeground": "#b180d7",
		//"terminalSymbolIcon.optionForeground": "#ee9d28",
		//"terminalSymbolIcon.optionValueForeground": "#75beff",
		//"testExplorer.errorDecorationBackground": "#5a1d1d",
		//"testing.coverCountBadgeBackground": "#4d4d4d",
		//"testing.coverCountBadgeForeground": "#ffffff",
		//"testing.coveredBackground": "#9ccc2c33",
		//"testing.coveredBorder": "#9ccc2c26",
		//"testing.coveredGutterBackground": "#9ccc2c1f",
		//"testing.iconErrored": "#f14c4c",
		//"testing.iconErrored.retired": "#f14c4cb3",
		//"testing.iconFailed": "#f14c4c",
		//"testing.iconFailed.retired": "#f14c4cb3",
		//"testing.iconPassed": "#73c991",
		//"testing.iconPassed.retired": "#73c991b3",
		//"testing.iconQueued": "#cca700",
		//"testing.iconQueued.retired": "#cca700b3",
		//"testing.iconSkipped": "#848484",
		//"testing.iconSkipped.retired": "#848484b3",
		//"testing.iconUnset": "#848484",
		//"testing.iconUnset.retired": "#848484b3",
		//"testing.message.error.badgeBackground": "#f14c4c",
		//"testing.message.error.badgeBorder": "#f14c4c",
		//"testing.message.error.badgeForeground": "#000000",
		//"testing.message.info.decorationForeground": "#d4d4d480",
		//"testing.messagePeekBorder": "#3794ff",
		//"testing.messagePeekHeaderBackground": "#3794ff1a",
		//"testing.peekBorder": "#f14c4c",
		//"testing.peekHeaderBackground": "#f14c4c1a",
		//"testing.runAction": "#73c991",
		//"testing.uncoveredBackground": "#ff000033",
		//"testing.uncoveredBorder": "#ff000026",
		//"testing.uncoveredBranchBackground": "#781212",
		//"testing.uncoveredGutterBackground": "#ff00004d",
		//"textBlockQuote.background": "#222222",
		//"textBlockQuote.border": "#007acc80",
		//"textCodeBlock.background": "#0a0a0a66",
		//"textLink.activeForeground": "#3794ff",
		//"textLink.foreground": "#3794ff",
		//"textPreformat.background": "#ffffff1a",
		//"textPreformat.foreground": "#d7ba7d",
		//"textSeparator.foreground": "#ffffff2e",
		//"titleBar.activeBackground": "#3c3c3c",
		//"titleBar.activeForeground": "#cccccc",
		//"titleBar.inactiveBackground": "#3c3c3c99",
		//"titleBar.inactiveForeground": "#cccccc99",
		//"toolbar.activeBackground": "#63666750",
		//"toolbar.hoverBackground": "#5a5d5e50",
		//"tree.inactiveIndentGuidesStroke": "#58585866",
		//"tree.indentGuidesStroke": "#585858",
		//"tree.tableColumnsBorder": "#cccccc20",
		//"tree.tableOddRowsBackground": "#cccccc0a",
		//"walkThrough.embeddedEditorBackground": "#00000066",
		//"walkthrough.stepTitle.foreground": "#ffffff",
		//"welcomePage.progress.background": "#3c3c3c",
		//"welcomePage.progress.foreground": "#3794ff",
		//"welcomePage.tileBackground": "#252526",
		//"welcomePage.tileBorder": "#ffffff1a",
		//"welcomePage.tileHoverBackground": "#2c2c2d",
		//"widget.shadow": "#0000005c",
		//"activityBar.activeBackground": null,
		//"activityBar.activeFocusBorder": null,
		//"activityBar.border": null,
		//"activityBarTop.activeBackground": null,
		//"activityBarTop.background": null,
		//"button.border": null,
		//"contrastActiveBorder": null,
		//"contrastBorder": null,
		//"debugToolBar.border": null,
		//"diffEditor.border": null,
		//"diffEditor.insertedTextBorder": null,
		//"diffEditor.removedTextBorder": null,
		//"diffEditorGutter.insertedLineBackground": null,
		//"diffEditorGutter.removedLineBackground": null,
		//"diffEditorOverview.insertedForeground": null,
		//"diffEditorOverview.removedForeground": null,
		//"dropdown.listBackground": null,
		//"editor.findMatchBorder": null,
		//"editor.findMatchForeground": null,
		//"editor.findMatchHighlightBorder": null,
		//"editor.findMatchHighlightForeground": null,
		//"editor.findRangeHighlightBorder": null,
		//"editor.lineHighlightBackground": null,
		//"editor.rangeHighlightBorder": null,
		//"editor.selectionForeground": null,
		//"editor.selectionHighlightBorder": null,
		//"editor.snippetFinalTabstopHighlightBackground": null,
		//"editor.snippetTabstopHighlightBorder": null,
		//"editor.symbolHighlightBorder": null,
		//"editor.wordHighlightBorder": null,
		//"editor.wordHighlightStrongBorder": null,
		//"editor.wordHighlightTextBorder": null,
		//"editorCursor.background": null,
		//"editorError.background": null,
		//"editorError.border": null,
		//"editorGhostText.background": null,
		//"editorGhostText.border": null,
		//"editorGroup.dropIntoPromptBorder": null,
		//"editorGroup.emptyBackground": null,
		//"editorGroup.focusedEmptyBorder": null,
		//"editorGroupHeader.border": null,
		//"editorGroupHeader.tabsBorder": null,
		//"editorHint.border": null,
		//"editorInfo.background": null,
		//"editorInfo.border": null,
		//"editorLineNumber.dimmedForeground": null,
		//"editorMultiCursor.primary.background": null,
		//"editorMultiCursor.secondary.background": null,
		//"editorOverviewRuler.background": null,
		//"editorStickyScroll.border": null,
		//"editorUnicodeHighlight.background": null,
		//"editorUnnecessaryCode.border": null,
		//"editorWarning.background": null,
		//"editorWarning.border": null,
		//"editorWidget.resizeBorder": null,
		//"gauge.border": null,
		//"input.border": null,
		//"inputValidation.errorForeground": null,
		//"inputValidation.infoForeground": null,
		//"inputValidation.warningForeground": null,
		//"list.filterMatchBorder": null,
		//"list.focusAndSelectionOutline": null,
		//"list.focusBackground": null,
		//"list.focusForeground": null,
		//"list.hoverForeground": null,
		//"list.inactiveFocusBackground": null,
		//"list.inactiveFocusOutline": null,
		//"list.inactiveSelectionForeground": null,
		//"list.inactiveSelectionIconForeground": null,
		//"menu.selectionBorder": null,
		//"menubar.selectionBorder": null,
		//"merge.border": null,
		//"minimap.background": null,
		//"notebook.cellHoverBackground": null,
		//"notebook.focusedCellBackground": null,
		//"notebook.inactiveSelectedCellBorder": null,
		//"notebook.outputContainerBackgroundColor": null,
		//"notebook.outputContainerBorderColor": null,
		//"notificationCenterHeader.foreground": null,
		//"outputView.background": null,
		//"outputViewStickyScroll.background": null,
		//"panelInput.border": null,
		//"panelSectionHeader.border": null,
		//"panelSectionHeader.foreground": null,
		//"panelStickyScroll.border": null,
		//"panelTitle.border": null,
		//"peekViewEditor.matchHighlightBorder": null,
		//"radio.inactiveBackground": null,
		//"radio.inactiveForeground": null,
		//"searchEditor.findMatchBorder": null,
		//"searchEditor.textInputBorder": null,
		//"selection.background": null,
		//"settings.numberInputBorder": null,
		//"settings.textInputBorder": null,
		//"sideBar.border": null,
		//"sideBar.foreground": null,
		//"sideBarSectionHeader.foreground": null,
		//"sideBarStickyScroll.border": null,
		//"sideBarTitle.border": null,
		//"statusBar.border": null,
		//"statusBar.debuggingBorder": null,
		//"statusBar.noFolderBorder": null,
		//"tab.activeBorder": null,
		//"tab.activeBorderTop": null,
		//"tab.hoverBackground": null,
		//"tab.hoverBorder": null,
		//"tab.hoverForeground": null,
		//"tab.selectedBorderTop": null,
		//"tab.unfocusedActiveBorder": null,
		//"tab.unfocusedActiveBorderTop": null,
		//"tab.unfocusedHoverBackground": null,
		//"tab.unfocusedHoverBorder": null,
		//"tab.unfocusedHoverForeground": null,
		//"terminal.background": null,
		//"terminal.findMatchBorder": null,
		//"terminal.findMatchHighlightBorder": null,
		//"terminal.selectionForeground": null,
		//"terminal.tab.activeBorder": null,
		//"terminalCursor.background": null,
		//"terminalCursor.foreground": null,
		//"terminalStickyScroll.background": null,
		//"terminalStickyScroll.border": null,
		//"terminalSymbolIcon.inlineSuggestionForeground": null,
		//"testing.message.error.lineBackground": null,
		//"testing.message.info.lineBackground": null,
		//"titleBar.border": null,
		//"toolbar.hoverOutline": null,
		//"welcomePage.background": null,
		//"window.activeBorder": null,
		//"window.inactiveBorder": null
	},
	"tokenColors": [
		{
			"scope": [
				"meta.embedded",
				"source.groovy.embedded",
				"string meta.image.inline.markdown",
				"variable.legacy.builtin.python"
			],
			"settings": {
				"foreground": "#D4D4D4"
			}
		},
		{
			"scope": "emphasis",
			"settings": {
				"fontStyle": "italic"
			}
		},
		{
			"scope": "strong",
			"settings": {
				"fontStyle": "bold"
			}
		},
		{
			"scope": "header",
			"settings": {
				"foreground": "#000080"
			}
		},
		{
			"scope": "comment",
			"settings": {
				"foreground": "#6A9955"
			}
		},
		{
			"scope": "constant.language",
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": [
				"constant.numeric",
				"variable.other.enummember",
				"keyword.operator.plus.exponent",
				"keyword.operator.minus.exponent"
			],
			"settings": {
				"foreground": "#B5CEA8"
			}
		},
		{
			"scope": "constant.regexp",
			"settings": {
				"foreground": "#646695"
			}
		},
		{
			"scope": "entity.name.tag",
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": [
				"entity.name.tag.css",
				"entity.name.tag.less"
			],
			"settings": {
				"foreground": "#D7BA7D"
			}
		},
		{
			"scope": "entity.other.attribute-name",
			"settings": {
				"foreground": "#9CDCFE"
			}
		},
		{
			"scope": [
				"entity.other.attribute-name.class.css",
				"source.css entity.other.attribute-name.class",
				"entity.other.attribute-name.id.css",
				"entity.other.attribute-name.parent-selector.css",
				"entity.other.attribute-name.parent.less",
				"source.css entity.other.attribute-name.pseudo-class",
				"entity.other.attribute-name.pseudo-element.css",
				"source.css.less entity.other.attribute-name.id",
				"entity.other.attribute-name.scss"
			],
			"settings": {
				"foreground": "#D7BA7D"
			}
		},
		{
			"scope": "invalid",
			"settings": {
				"foreground": "#F44747"
			}
		},
		{
			"scope": "markup.underline",
			"settings": {
				"fontStyle": "underline"
			}
		},
		{
			"scope": "markup.bold",
			"settings": {
				"foreground": "#569CD6",
				"fontStyle": "bold"
			}
		},
		{
			"scope": "markup.heading",
			"settings": {
				"foreground": "#569CD6",
				"fontStyle": "bold"
			}
		},
		{
			"scope": "markup.italic",
			"settings": {
				"fontStyle": "italic"
			}
		},
		{
			"scope": "markup.strikethrough",
			"settings": {
				"fontStyle": "strikethrough"
			}
		},
		{
			"scope": "markup.inserted",
			"settings": {
				"foreground": "#B5CEA8"
			}
		},
		{
			"scope": "markup.deleted",
			"settings": {
				"foreground": "#CE9178"
			}
		},
		{
			"scope": "markup.changed",
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": "punctuation.definition.quote.begin.markdown",
			"settings": {
				"foreground": "#6A9955"
			}
		},
		{
			"scope": "punctuation.definition.list.begin.markdown",
			"settings": {
				"foreground": "#6796E6"
			}
		},
		{
			"scope": "markup.inline.raw",
			"settings": {
				"foreground": "#CE9178"
			}
		},
		{
			"scope": "punctuation.definition.tag",
			"settings": {
				"foreground": "#808080"
			}
		},
		{
			"scope": [
				"meta.preprocessor",
				"entity.name.function.preprocessor"
			],
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": "meta.preprocessor.string",
			"settings": {
				"foreground": "#CE9178"
			}
		},
		{
			"scope": "meta.preprocessor.numeric",
			"settings": {
				"foreground": "#B5CEA8"
			}
		},
		{
			"scope": "meta.structure.dictionary.key.python",
			"settings": {
				"foreground": "#9CDCFE"
			}
		},
		{
			"scope": "meta.diff.header",
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": "storage",
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": "storage.type",
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": [
				"storage.modifier",
				"keyword.operator.noexcept"
			],
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": [
				"string",
				"meta.embedded.assembly"
			],
			"settings": {
				"foreground": "#CE9178"
			}
		},
		{
			"scope": "string.tag",
			"settings": {
				"foreground": "#CE9178"
			}
		},
		{
			"scope": "string.value",
			"settings": {
				"foreground": "#CE9178"
			}
		},
		{
			"scope": "string.regexp",
			"settings": {
				"foreground": "#D16969"
			}
		},
		{
			"scope": [
				"punctuation.definition.template-expression.begin",
				"punctuation.definition.template-expression.end",
				"punctuation.section.embedded"
			],
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": [
				"meta.template.expression"
			],
			"settings": {
				"foreground": "#D4D4D4"
			}
		},
		{
			"scope": [
				"support.type.vendored.property-name",
				"support.type.property-name",
				"source.css variable",
				"source.coffee.embedded"
			],
			"settings": {
				"foreground": "#9CDCFE"
			}
		},
		{
			"scope": "keyword",
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": "keyword.control",
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": "keyword.operator",
			"settings": {
				"foreground": "#D4D4D4"
			}
		},
		{
			"scope": [
				"keyword.operator.new",
				"keyword.operator.expression",
				"keyword.operator.cast",
				"keyword.operator.sizeof",
				"keyword.operator.alignof",
				"keyword.operator.typeid",
				"keyword.operator.alignas",
				"keyword.operator.instanceof",
				"keyword.operator.logical.python",
				"keyword.operator.wordlike"
			],
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": "keyword.other.unit",
			"settings": {
				"foreground": "#B5CEA8"
			}
		},
		{
			"scope": [
				"punctuation.section.embedded.begin.php",
				"punctuation.section.embedded.end.php"
			],
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": "support.function.git-rebase",
			"settings": {
				"foreground": "#9CDCFE"
			}
		},
		{
			"scope": "constant.sha.git-rebase",
			"settings": {
				"foreground": "#B5CEA8"
			}
		},
		{
			"scope": [
				"storage.modifier.import.java",
				"variable.language.wildcard.java",
				"storage.modifier.package.java"
			],
			"settings": {
				"foreground": "#D4D4D4"
			}
		},
		{
			"scope": "variable.language",
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": [
				"entity.name.function",
				"support.function",
				"support.constant.handlebars",
				"source.powershell variable.other.member",
				"entity.name.operator.custom-literal"
			],
			"settings": {
				"foreground": "#DCDCAA"
			}
		},
		{
			"scope": [
				"support.class",
				"support.type",
				"entity.name.type",
				"entity.name.namespace",
				"entity.other.attribute",
				"entity.name.scope-resolution",
				"entity.name.class",
				"storage.type.numeric.go",
				"storage.type.byte.go",
				"storage.type.boolean.go",
				"storage.type.string.go",
				"storage.type.uintptr.go",
				"storage.type.error.go",
				"storage.type.rune.go",
				"storage.type.cs",
				"storage.type.generic.cs",
				"storage.type.modifier.cs",
				"storage.type.variable.cs",
				"storage.type.annotation.java",
				"storage.type.generic.java",
				"storage.type.java",
				"storage.type.object.array.java",
				"storage.type.primitive.array.java",
				"storage.type.primitive.java",
				"storage.type.token.java",
				"storage.type.groovy",
				"storage.type.annotation.groovy",
				"storage.type.parameters.groovy",
				"storage.type.generic.groovy",
				"storage.type.object.array.groovy",
				"storage.type.primitive.array.groovy",
				"storage.type.primitive.groovy"
			],
			"settings": {
				"foreground": "#4EC9B0"
			}
		},
		{
			"scope": [
				"meta.type.cast.expr",
				"meta.type.new.expr",
				"support.constant.math",
				"support.constant.dom",
				"support.constant.json",
				"entity.other.inherited-class",
				"punctuation.separator.namespace.ruby"
			],
			"settings": {
				"foreground": "#4EC9B0"
			}
		},
		{
			"scope": [
				"keyword.control",
				"source.cpp keyword.operator.new",
				"keyword.operator.delete",
				"keyword.other.using",
				"keyword.other.directive.using",
				"keyword.other.operator",
				"entity.name.operator"
			],
			"settings": {
				"foreground": "#C586C0"
			}
		},
		{
			"scope": [
				"variable",
				"meta.definition.variable.name",
				"support.variable",
				"entity.name.variable",
				"constant.other.placeholder"
			],
			"settings": {
				"foreground": "#9CDCFE"
			}
		},
		{
			"scope": [
				"variable.other.constant",
				"variable.other.enummember"
			],
			"settings": {
				"foreground": "#4FC1FF"
			}
		},
		{
			"scope": [
				"meta.object-literal.key"
			],
			"settings": {
				"foreground": "#9CDCFE"
			}
		},
		{
			"scope": [
				"support.constant.property-value",
				"support.constant.font-name",
				"support.constant.media-type",
				"support.constant.media",
				"constant.other.color.rgb-value",
				"constant.other.rgb-value",
				"support.constant.color"
			],
			"settings": {
				"foreground": "#CE9178"
			}
		},
		{
			"scope": [
				"punctuation.definition.group.regexp",
				"punctuation.definition.group.assertion.regexp",
				"punctuation.definition.character-class.regexp",
				"punctuation.character.set.begin.regexp",
				"punctuation.character.set.end.regexp",
				"keyword.operator.negation.regexp",
				"support.other.parenthesis.regexp"
			],
			"settings": {
				"foreground": "#CE9178"
			}
		},
		{
			"scope": [
				"constant.character.character-class.regexp",
				"constant.other.character-class.set.regexp",
				"constant.other.character-class.regexp",
				"constant.character.set.regexp"
			],
			"settings": {
				"foreground": "#D16969"
			}
		},
		{
			"scope": [
				"keyword.operator.or.regexp",
				"keyword.control.anchor.regexp"
			],
			"settings": {
				"foreground": "#DCDCAA"
			}
		},
		{
			"scope": "keyword.operator.quantifier.regexp",
			"settings": {
				"foreground": "#D7BA7D"
			}
		},
		{
			"scope": [
				"constant.character",
				"constant.other.option"
			],
			"settings": {
				"foreground": "#569CD6"
			}
		},
		{
			"scope": "constant.character.escape",
			"settings": {
				"foreground": "#D7BA7D"
			}
		},
		{
			"scope": "entity.name.label",
			"settings": {
				"foreground": "#C8C8C8"
			}
		},
		{
			"scope": "ref.matchtext",
			"settings": {
				"foreground": "#FFFFFF"
			}
		},
		{
			"scope": "token.info-token",
			"settings": {
				"foreground": "#6796E6"
			}
		},
		{
			"scope": "token.warn-token",
			"settings": {
				"foreground": "#CD9731"
			}
		},
		{
			"scope": "token.error-token",
			"settings": {
				"foreground": "#F44747"
			}
		},
		{
			"scope": "token.debug-token",
			"settings": {
				"foreground": "#B267E6"
			}
		}
	]
}

@extends('layouts/Main')

@section('title', translateText('د لټون پایلې') . ' - ' . $search_query)

@section('contents')
<div id="ssd">
    <!-- <h1>{{ translateText('د لټون پایلې') }}: "{{ $search_query }}"</h1> -->
    
    <form action="{{ route('search') }}" method="GET">
        <div id="search_sec">
            <button type="submit" id="serch_btn">
                <i class="fas fa-search"></i>
            </button>
            <input type="search" name="query" placeholder="{{ translateText('پلټنه وکړئ....') }}" value="{{ $search_query }}">
        </div>
    </form>

    <!-- Articles Results -->
    @if($articles->count() > 0)
        <!-- <h2>{{ translateText('کتابونه او مقالې') }}</h2> -->
        <div class="books-container">
            @foreach($articles as $article)
                <div class="book-card">
                    <div class="book-image">
                        @if($article->A_Image)
                            <img src="{{ asset('imagese/' . $article->A_Image) }}" alt="{{ $article->A_Title }}">
                        @else
                            <img src="{{ asset('imagese/book1.jpg') }}" alt="Default Image">
                        @endif
                    </div>
                    <div class="book-info">
                        <h3>{{translateText('نوم:')}} {{$article->A_Title}}</h3>
                        <p>{{translateText('لیکوال: ')}}{{$article->A_Author}}</p>
                        <p>{{translateText('ژبه: ')}}{{$article->A_Language}}</p>
                        <p>{{translateText('ګتګوری:')}}{{$article->A_catagory}}</p>
                        <p>{{translateText('خپریدو نیټه:')}}{{$article->A_Publication_date}}</p>
                        
                        @if($article->A_File)
                            <a href="{{ asset('imagese/' . $article->A_File) }}" class="button" target="_blank">
                                {{ translateText('وګورئ') }}
                            </a>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>
        
        <!-- Pagination for Articles -->
        <div class="pagination">
            @if($articles->previousPageUrl())
                <a href="{{ $articles->previousPageUrl() }}" class="pagination-btn">{{ translateText('مخکنی') }}</a>
            @endif
            
            @for($i = 1; $i <= $articles->lastPage(); $i++)
                <a href="{{ $articles->url($i) }}" class="pagination-btn {{ $articles->currentPage() == $i ? 'active' : '' }}">{{ $i }}</a>
            @endfor
            
            @if($articles->nextPageUrl())
                <a href="{{ $articles->nextPageUrl() }}" class="pagination-btn">{{ translateText('ورستنی') }}</a>
            @endif
        </div>
    @endif

    <!-- News Results -->
    @if($news->count() > 0)
        <!-- <h2>{{ translateText('خبرونه') }}</h2> -->
        <div id="jkjk">
            @foreach($news as $new)
                <div class="news">
                    <h2>{{translateText($new->News_Title)}}</h2>
                    <div class="wraper"></div>
                    <p>{{translateText($new->News_Discription)}}</p>

                    @if($new->New_Images)
                        <img src="{{ asset('imagese/' . $new->New_Images) }}" alt="News Image" width="200">
                    @else
                        <img src="{{ asset('imagese/image6.jpg') }}" alt="Default Image" width="200">
                    @endif

                    <p>{{translateText('خپریدو نیټه:')}}<time>{{$new->created_at}}</time></p>
                
                </div>
            @endforeach
        </div>
        
    @endif

    @if($articles->count() == 0 && $news->count() == 0)
        <div class="no-results">
            <p style="text-align: center;">{{translateText('د پلټنې سره سمون لرونکي معلومات ونه موندل شول')}}</p>
        </div>
    @endif
</div>

</div>


@endsection





@extends('layouts/Admin')

@section('title', 'د رواني روغتیا ډشبورډ - د کندهار پوهنتون')

@push('styles')
<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    /* Professional Dashboard Styles - Version 3.0 - Cache Bust: {{ time() }} */
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);

        --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
        --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.12);
        --shadow-xl: 0 12px 40px rgba(0, 0, 0, 0.15);

        --border-radius: 16px;
        --border-radius-lg: 20px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .dashboard-container {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        min-height: 100vh;
        padding: 0;
        position: relative;
    }

    .dashboard-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 300px;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        z-index: 0;
    }

    .dashboard-content {
        position: relative;
        z-index: 1;
    }

    /* Simple Dashboard Header Styles */
    .dashboard-header {
        animation: slideInDown 0.6s ease-out;
    }

    /* Professional Action Buttons */
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .action-btn:active {
        transform: translateY(0);
    }

    .notifications-btn:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    }

    .new-user-btn:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
    }

    .logout-btn:hover {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }

    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Standard Statistics Cards */
    .stat-card {
        width: 100%;
        max-width: 320px;
        min-width: 280px;
        height: 240px !important;
        margin: 0 auto;
    }

    .stat-card:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12), 0 8px 25px rgba(0, 0, 0, 0.06);
    }

    .stat-card:hover .stat-icon {
        transform: scale(1.05);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .stat-card .stat-number {
        transition: all 0.3s ease;
    }

    .stat-card:hover .stat-number {
        transform: scale(1.03);
    }

    /* Standard Grid Layout */
    .stats-grid {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
        gap: 1.5rem !important;
        max-width: 1200px !important;
        margin: 0 auto 3rem auto !important;
        padding: 0 1rem;
    }

    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: 1fr !important;
            gap: 1rem !important;
        }

        .stat-card {
            min-width: 100%;
            max-width: 100%;
        }
    }

    /* Professional Card Animations */
    .stat-card {
        animation: fadeInUp 0.6s ease-out;
    }

    .stat-card:nth-child(1) { animation-delay: 0.1s; }
    .stat-card:nth-child(2) { animation-delay: 0.2s; }
    .stat-card:nth-child(3) { animation-delay: 0.3s; }
    .stat-card:nth-child(4) { animation-delay: 0.4s; }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Professional Pulse Animation */
    @keyframes professionalPulse {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.1);
            opacity: 0.8;
        }
    }

    .animate-pulse {
        animation: professionalPulse 2s infinite;
    }

    /* Simple Search Styles */
    .search-wrapper:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: #d1d5db;
    }

    .search-wrapper:focus-within {
        border-color: #6366f1;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
    }

    .search-input:focus {
        outline: none;
    }

    .search-input::placeholder {
        color: #9ca3af;
    }

    .search-btn:hover {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    }

    .search-btn:active {
        transform: scale(0.98);
    }

    /* Enhanced Search Results */
    .search-results {
        animation: fadeInDown 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        max-height: 400px;
    }

    @keyframes fadeInDown {
        from {
            opacity: 0;
            transform: translateY(-15px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .search-result-item {
        padding: 16px 20px;
        border-bottom: 1px solid rgba(243, 244, 246, 0.8);
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
    }

    .search-result-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        transform: scaleY(0);
        transition: transform 0.3s ease;
    }

    .search-result-item:hover {
        background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
        transform: translateX(8px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .search-result-item:hover::before {
        transform: scaleY(1);
    }

    .search-result-item:last-child {
        border-bottom: none;
    }

    /* Responsive Dashboard Header */
    @media (max-width: 1024px) {
        .dashboard-header .flex {
            flex-direction: column;
            gap: 1.5rem;
            text-align: center;
        }

        .search-input {
            width: 300px !important;
        }
    }

    @media (max-width: 768px) {
        .dashboard-header {
            padding: 1rem 1.5rem;
        }

        .university-logo {
            width: 60px !important;
            height: 60px !important;
        }

        .dashboard-header h1 {
            font-size: 1.75rem;
        }

        .search-input {
            width: 250px !important;
        }

        .search-btn span {
            display: none;
        }
    }

    /* Quick Actions Menu */
    .quick-actions-menu.show {
        opacity: 1;
        visibility: visible;
        transform: scale(1);
    }

    /* Standard Search Results */
    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        margin-top: 4px;
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }

    .search-result-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f3f4f6;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .search-result-item:hover {
        background: #f9fafb;
    }

    .search-result-item:last-child {
        border-bottom: none;
    }

    /* Professional Animations */
    @keyframes searchPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .search-icon-container:hover {
        animation: searchPulse 0.6s ease-in-out;
    }

    /* Glass Morphism Effects */
    .glass-effect {
        background: rgba(255, 255, 255, 0.25);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.18);
    }

    /* Professional Gradients */
    .gradient-text {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .university-logo:hover {
        transform: scale(1.1) rotate(5deg);
        transition: all 0.3s ease;
    }

    /* Search Results Dropdown */
    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border: 1px solid #e2e8f0;
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }

    .search-result-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f1f5f9;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .search-result-item:hover {
        background: #f8fafc;
        transform: translateX(4px);
    }

    .search-result-item:last-child {
        border-bottom: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .top-header .flex {
            flex-direction: column;
            gap: 1rem;
        }

        .search-section {
            width: 100%;
            justify-content: center;
        }

        .search-input {
            width: 250px !important;
        }
    }

    .welcome-banner {
        background: var(--primary-gradient);
        border-radius: var(--border-radius-lg);
        padding: 2.5rem;
        margin-bottom: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-xl);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .welcome-banner::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 8s ease-in-out infinite;
    }

    .welcome-banner::after {
        content: '';
        position: absolute;
        bottom: -50px;
        left: -50px;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        animation: pulse 4s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 0.7; }
        50% { transform: scale(1.2); opacity: 0.3; }
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes fadeInScale {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
        animation: slideInUp 0.6s ease-out;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-md);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.3);
        animation: fadeInScale 0.6s ease-out;
        animation-fill-mode: both;
    }

    .stat-card:nth-child(1) { animation-delay: 0.1s; }
    .stat-card:nth-child(2) { animation-delay: 0.2s; }
    .stat-card:nth-child(3) { animation-delay: 0.3s; }
    .stat-card:nth-child(4) { animation-delay: 0.4s; }
    .stat-card:nth-child(5) { animation-delay: 0.5s; }
    .stat-card:nth-child(6) { animation-delay: 0.6s; }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: var(--gradient);
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.5s ease;
    }

    .stat-card::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 5px;
        background: var(--gradient);
        transform: scaleY(0);
        transform-origin: bottom;
        transition: transform 0.5s ease 0.2s;
    }

    .stat-card:hover::before {
        transform: scaleX(1);
    }

    .stat-card:hover::after {
        transform: scaleY(1);
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.01);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 10px 25px rgba(0, 0, 0, 0.08);
        background: linear-gradient(145deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 1) 100%);
        border: 1px solid rgba(99, 102, 241, 0.2);
    }

    .stat-card.patients { --gradient: var(--primary-gradient); }
    .stat-card.doctors { --gradient: var(--secondary-gradient); }
    .stat-card.news { --gradient: var(--success-gradient); }
    .stat-card.articles { --gradient: var(--warning-gradient); }
    .stat-card.feedback { --gradient: var(--danger-gradient); }
    .stat-card.videos { --gradient: var(--info-gradient); }

    .stat-icon {
        width: 70px;
        height: 70px;
        border-radius: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        margin-bottom: 1.5rem;
        background: var(--gradient);
        color: white;
        box-shadow: var(--shadow-lg);
        transition: var(--transition);
        position: relative;
    }

    .stat-icon::before {
        content: '';
        position: absolute;
        inset: -2px;
        background: var(--gradient);
        border-radius: 20px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .stat-card:hover .stat-icon {
        transform: scale(1.08) rotate(3deg);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2), 0 8px 20px rgba(0, 0, 0, 0.1);
        filter: brightness(1.1) saturate(1.2);
    }

    .stat-card:hover .stat-icon::before {
        opacity: 0.4;
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 900;
        color: #1a202c;
        margin-bottom: 0.5rem;
        background: var(--gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1;
        transition: var(--transition);
    }

    .stat-card:hover .stat-number {
        transform: scale(1.03);
        color: #1e40af;
        text-shadow: 0 2px 8px rgba(30, 64, 175, 0.2);
    }

    .stat-label {
        color: #4a5568;
        font-weight: 700;
        font-size: 1.1rem;
        margin-bottom: 0.75rem;
        transition: var(--transition);
    }

    .stat-card:hover .stat-label {
        color: #1e40af;
        font-weight: 600;
        transform: translateY(-1px);
    }

    .stat-description {
        color: #718096;
        font-size: 0.9rem;
        line-height: 1.5;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: var(--transition);
    }

    .stat-card:hover .stat-description {
        color: #3b82f6;
        font-weight: 500;
        transform: translateY(-1px);
    }

    .stat-trend {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 600;
        margin-top: 0.5rem;
    }

    .stat-trend.up {
        background: rgba(72, 187, 120, 0.1);
        color: #38a169;
    }

    .stat-trend.down {
        background: rgba(245, 101, 101, 0.1);
        color: #e53e3e;
    }

    .chart-container {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
        animation: slideInUp 0.8s ease-out;
    }

    .chart-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-md);
        transition: var(--transition);
        border: 1px solid rgba(255, 255, 255, 0.3);
        position: relative;
        overflow: hidden;
    }

    .chart-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--primary-gradient);
        transform: scaleX(0);
        transform-origin: left;
        transition: transform 0.5s ease;
    }

    .chart-card:hover::before {
        transform: scaleX(1);
    }

    .chart-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
        background: rgba(255, 255, 255, 1);
    }

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(102, 126, 234, 0.1);
        position: relative;
    }

    .chart-header::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 60px;
        height: 2px;
        background: var(--primary-gradient);
        border-radius: 1px;
    }

    .chart-title {
        font-size: 1.4rem;
        font-weight: 800;
        color: #1a202c;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .chart-title i {
        color: #667eea;
        font-size: 1.2rem;
    }

    .chart-actions {
        display: flex;
        gap: 0.5rem;
        background: rgba(102, 126, 234, 0.05);
        padding: 0.25rem;
        border-radius: 10px;
    }

    .chart-btn {
        padding: 0.6rem 1.2rem;
        border: none;
        background: transparent;
        border-radius: 8px;
        font-weight: 600;
        color: #4a5568;
        cursor: pointer;
        transition: var(--transition);
        font-size: 0.9rem;
        position: relative;
    }

    .chart-btn::before {
        content: '';
        position: absolute;
        inset: 0;
        background: var(--primary-gradient);
        border-radius: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .chart-btn span {
        position: relative;
        z-index: 1;
    }

    .chart-btn:hover, .chart-btn.active {
        color: white;
        transform: translateY(-1px);
    }

    .chart-btn:hover::before, .chart-btn.active::before {
        opacity: 1;
    }

    .chart-content {
        position: relative;
        border-radius: 12px;
        overflow: hidden;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
    }

    .activity-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: var(--border-radius);
        padding: 2rem;
        box-shadow: var(--shadow-md);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: var(--transition);
        animation: slideInUp 1s ease-out;
    }

    .activity-section:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
    }

    .activity-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid rgba(102, 126, 234, 0.1);
        position: relative;
    }

    .activity-header::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 80px;
        height: 2px;
        background: var(--primary-gradient);
        border-radius: 1px;
    }

    .activity-title {
        font-size: 1.4rem;
        font-weight: 800;
        color: #1a202c;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .activity-title i {
        color: #667eea;
        font-size: 1.2rem;
    }

    .view-all-btn {
        color: #667eea;
        font-weight: 600;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: var(--transition);
        padding: 0.5rem 1rem;
        border-radius: 8px;
        background: rgba(102, 126, 234, 0.05);
    }

    .view-all-btn:hover {
        color: white;
        background: var(--primary-gradient);
        transform: translateX(-2px);
    }

    .activity-item {
        display: flex;
        align-items: center;
        padding: 1.25rem;
        border-radius: 12px;
        margin-bottom: 1rem;
        background: rgba(248, 250, 252, 0.8);
        transition: var(--transition);
        border-left: 4px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .activity-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--primary-gradient);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .activity-item:hover {
        background: rgba(255, 255, 255, 1);
        border-left-color: #667eea;
        transform: translateX(8px);
        box-shadow: var(--shadow-md);
    }

    .activity-item:hover::before {
        opacity: 0.02;
    }

    .activity-icon {
        width: 56px;
        height: 56px;
        border-radius: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        background: var(--primary-gradient);
        color: white;
        font-size: 20px;
        box-shadow: var(--shadow-md);
        transition: var(--transition);
        position: relative;
        z-index: 1;
    }

    .activity-item:hover .activity-icon {
        transform: scale(1.1) rotate(5deg);
        box-shadow: var(--shadow-lg);
    }

    .activity-content {
        flex: 1;
        position: relative;
        z-index: 1;
    }

    .activity-text {
        font-weight: 700;
        color: #1a202c;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .activity-time {
        color: #718096;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
        animation: slideInUp 0.6s ease-out;
    }

    .quick-action-btn {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 2px solid rgba(102, 126, 234, 0.1);
        border-radius: 14px;
        padding: 1.5rem;
        text-align: center;
        text-decoration: none;
        color: #2d3748;
        font-weight: 700;
        transition: var(--transition);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
        position: relative;
        overflow: hidden;
    }

    .quick-action-btn::before {
        content: '';
        position: absolute;
        inset: 0;
        background: var(--primary-gradient);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .quick-action-btn:hover {
        border-color: #667eea;
        color: white;
        transform: translateY(-4px) scale(1.02);
        box-shadow: var(--shadow-xl);
    }

    .quick-action-btn:hover::before {
        opacity: 1;
    }

    .quick-action-icon {
        font-size: 2rem;
        transition: var(--transition);
        position: relative;
        z-index: 1;
    }

    .quick-action-btn:hover .quick-action-icon {
        transform: scale(1.1) rotate(5deg);
    }

    .quick-action-btn span {
        position: relative;
        z-index: 1;
        font-size: 0.95rem;
    }

    /* Enhanced Footer */
    .dashboard-footer {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(15px);
        border-radius: var(--border-radius);
        padding: 2rem;
        text-align: center;
        color: #4a5568;
        font-weight: 600;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: var(--shadow-md);
        margin-top: 2rem;
        position: relative;
        overflow: hidden;
    }

    .dashboard-footer::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--primary-gradient);
    }

    /* Loading States */
    .loading-placeholder {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 8px;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* Dynamic Chart Loading States */
    .chart-loading {
        position: relative;
        overflow: hidden;
    }

    .chart-loading::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
        animation: shimmer 1.5s infinite;
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    /* Button Loading States */
    .chart-btn.loading {
        position: relative;
        color: transparent;
    }

    .chart-btn.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin: -8px 0 0 -8px;
        border: 2px solid #fff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* Refresh Button Styles */
    .refresh-btn {
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .refresh-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .refresh-btn:active {
        transform: translateY(0);
    }

    .refresh-btn:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
    }

    .refresh-btn i {
        transition: transform 0.3s ease;
    }

    .refresh-btn:hover i {
        transform: rotate(180deg);
    }

    /* Enhanced Responsive Design */
    @media (max-width: 1200px) {
        .chart-container {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }
    }

    @media (max-width: 768px) {
        .dashboard-container {
            padding: 0;
        }

        .welcome-banner {
            padding: 1.5rem;
            margin: 1rem;
            border-radius: 12px;
        }

        .welcome-banner h1 {
            font-size: 1.75rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
            margin: 0 1rem 2rem;
        }

        .stat-card {
            padding: 1.5rem;
        }

        .stat-number {
            font-size: 2.5rem;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            font-size: 24px;
        }

        .quick-actions {
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
            margin: 0 1rem 2rem;
        }

        .quick-action-btn {
            padding: 1.25rem;
        }

        .chart-card, .activity-section {
            margin: 0 1rem;
            padding: 1.5rem;
        }

        .chart-title, .activity-title {
            font-size: 1.2rem;
        }

        .chart-actions {
            flex-wrap: wrap;
            gap: 0.25rem;
        }

        .chart-btn {
            padding: 0.5rem 0.75rem;
            font-size: 0.8rem;
        }

        .activity-item {
            padding: 1rem;
        }

        .activity-icon {
            width: 48px;
            height: 48px;
            font-size: 18px;
        }
    }

    @media (max-width: 480px) {
        .welcome-banner {
            text-align: center;
        }

        .welcome-banner .flex {
            flex-direction: column;
            gap: 1rem;
        }

        .stat-number {
            font-size: 2rem;
        }

        .quick-actions {
            grid-template-columns: 1fr;
        }

        .chart-actions {
            justify-content: center;
        }

        .activity-header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
        .dashboard-container {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
        }

        .stat-card, .chart-card, .activity-section, .quick-action-btn {
            background: rgba(45, 55, 72, 0.95);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .stat-label, .chart-title, .activity-title, .activity-text {
            color: #e2e8f0;
        }

        .stat-description, .activity-time {
            color: #a0aec0;
        }
    }

    /* Print styles */
    @media print {
        .welcome-banner, .quick-actions, .chart-actions {
            display: none;
        }

        .dashboard-container {
            background: white;
        }

        .stat-card, .chart-card, .activity-section {
            box-shadow: none;
            border: 1px solid #e2e8f0;
        }
    }
</style>
@endpush

@section('contents')

@if(isset($error))
<div class="alert alert-danger" style="background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%); border: 1px solid #f87171; border-radius: 12px; padding: 1rem; margin: 1rem auto; max-width: 1200px; color: #dc2626;">
    <div style="display: flex; align-items: center;">
        <i class="fas fa-exclamation-triangle" style="font-size: 1.5rem; margin-left: 1rem;"></i>
        <div>
            <h4 style="font-weight: 600; margin-bottom: 0.5rem;">{{ translateText('د سیسټم کې ستونزه رامنځته شوه') }}</h4>
            <p style="margin: 0;">{{ translateText('مهرباني وکړئ بیا هڅه وکړئ یا د سیسټم مدیر سره اړیکه ونیسئ') }}</p>
            @if(config('app.debug'))
                <details style="margin-top: 0.5rem;">
                    <summary style="cursor: pointer; font-weight: 500;">Technical Details</summary>
                    <pre style="background: rgba(0,0,0,0.1); padding: 0.5rem; border-radius: 4px; margin-top: 0.5rem; font-size: 0.8rem; overflow-x: auto;">{{ $error }}</pre>
                </details>
            @endif
        </div>
    </div>
</div>
@endif

<div class="dashboard-container">
    <div class="dashboard-content">


        <!-- Enhanced Statistics Cards -->
        <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; margin-bottom: 3rem; max-width: 1200px; margin-left: auto; margin-right: auto;">
            <!-- Professional Patients Card -->
            <div class="stat-card patients" style="background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%); backdrop-filter: blur(15px); border-radius: 16px; padding: 1.5rem; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(102, 126, 234, 0.05); transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.3); cursor: pointer; height: 240px; display: flex; flex-direction: column; justify-content: space-between;">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div style="background-image: radial-gradient(circle at 20% 80%, #667eea 2px, transparent 2px), radial-gradient(circle at 80% 20%, #764ba2 2px, transparent 2px); background-size: 40px 40px;"></div>
                </div>

                <!-- Standard Icon -->
                <div class="stat-icon" style="width: 60px; height: 60px; border-radius: 14px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 1rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25); position: relative; overflow: hidden;">
                    <i class="fas fa-users relative z-10"></i>
                </div>

                <!-- Standard Number Display -->
                <div class="stat-number" style="font-size: 2.5rem; font-weight: 800; margin-bottom: 0.5rem; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; line-height: 1;">
                    {{ $patientCount ?? 247 }}
                </div>

                <!-- Standard Label -->
                <div class="stat-label" style="color: #374151; font-weight: 600; font-size: 1rem; margin-bottom: 0.5rem;">{{ translateText('ټول ناروغان') }}</div>

                <!-- Standard Description -->
                <div class="stat-description" style="color: #6b7280; font-size: 0.85rem; margin-bottom: 1rem; line-height: 1.4;">
                    {{ translateText('د ټولو ناروغانو شمیر چې په سیسټم کې ثبت دي') }}
                </div>

               
                <!-- Hover Effect Overlay -->
                <div class="absolute inset-0 bg-gradient-to-br from-blue-500/8 to-indigo-500/8 opacity-0 hover:opacity-100 transition-all duration-300 rounded-16 pointer-events-none"></div>
            </div>

            <!-- Professional Doctors Card -->
            <div class="stat-card doctors" style="background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(254, 242, 242, 0.95) 100%); backdrop-filter: blur(15px); border-radius: 16px; padding: 1.5rem; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(240, 147, 251, 0.05); transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.3); cursor: pointer; height: 240px; display: flex; flex-direction: column; justify-content: space-between;">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div style="background-image: radial-gradient(circle at 20% 80%, #f093fb 2px, transparent 2px), radial-gradient(circle at 80% 20%, #f5576c 2px, transparent 2px); background-size: 40px 40px;"></div>
                </div>

                <!-- Standard Icon -->
                <div class="stat-icon" style="width: 60px; height: 60px; border-radius: 14px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 1rem; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; box-shadow: 0 6px 20px rgba(240, 147, 251, 0.25); position: relative; overflow: hidden;">
                    <i class="fas fa-user-md relative z-10"></i>
                </div>

                <!-- Standard Number Display -->
                <div class="stat-number" style="font-size: 2.5rem; font-weight: 800; margin-bottom: 0.5rem; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; line-height: 1;">
                    {{ $doctorCount ?? 18 }}
                </div>

                <!-- Standard Label -->
                <div class="stat-label" style="color: #374151; font-weight: 600; font-size: 1rem; margin-bottom: 0.5rem;">{{ translateText('ټول ډاکټران') }}</div>

                <!-- Standard Description -->
                <div class="stat-description" style="color: #6b7280; font-size: 0.85rem; margin-bottom: 1rem; line-height: 1.4;">
                    {{ translateText('د ټولو ډاکټرانو شمیر چې په سیسټم کې ثبت دي') }}
                </div>

            

                <!-- Hover Effect Overlay -->
                <div class="absolute inset-0 bg-gradient-to-br from-pink-500/8 to-rose-500/8 opacity-0 hover:opacity-100 transition-all duration-300 rounded-16 pointer-events-none"></div>
            </div>

            <!-- Standard News Card -->
            <div class="stat-card news" style="background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 251, 235, 0.95) 100%); backdrop-filter: blur(15px); border-radius: 16px; padding: 1.5rem; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(251, 191, 36, 0.05); transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.3); cursor: pointer; height: 240px; display: flex; flex-direction: column; justify-content: space-between;">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div style="background-image: radial-gradient(circle at 20% 80%, #fbbf24 2px, transparent 2px), radial-gradient(circle at 80% 20%, #f59e0b 2px, transparent 2px); background-size: 40px 40px;"></div>
                </div>

                <!-- Standard Icon -->
                <div class="stat-icon" style="width: 60px; height: 60px; border-radius: 14px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 1rem; background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); color: white; box-shadow: 0 6px 20px rgba(251, 191, 36, 0.25); position: relative; overflow: hidden;">
                    <i class="fas fa-newspaper relative z-10"></i>
                </div>

                <!-- Standard Number Display -->
                <div class="stat-number" style="font-size: 2.5rem; font-weight: 800; margin-bottom: 0.5rem; background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; line-height: 1;">
                    {{ $newsCount ?? 156 }}
                </div>

                <!-- Standard Label -->
                <div class="stat-label" style="color: #374151; font-weight: 600; font-size: 1rem; margin-bottom: 0.5rem;">{{ translateText('ټول خبرونه') }}</div>

                <!-- Standard Description -->
                <div class="stat-description" style="color: #6b7280; font-size: 0.85rem; margin-bottom: 1rem; line-height: 1.4;">
                    {{ translateText('د ټولو خبرونو شمیر چې دلته شته') }}
                </div>

              

                <!-- Hover Effect Overlay -->
                <div class="absolute inset-0 bg-gradient-to-br from-amber-500/8 to-yellow-500/8 opacity-0 hover:opacity-100 transition-all duration-300 rounded-16 pointer-events-none"></div>
            </div>

            <!-- Standard Articles Card -->
            <div class="stat-card articles" style="background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(245, 243, 255, 0.95) 100%); backdrop-filter: blur(15px); border-radius: 16px; padding: 1.5rem; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(139, 92, 246, 0.05); transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.3); cursor: pointer; height: 240px; display: flex; flex-direction: column; justify-content: space-between;">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div style="background-image: radial-gradient(circle at 20% 80%, #8b5cf6 2px, transparent 2px), radial-gradient(circle at 80% 20%, #7c3aed 2px, transparent 2px); background-size: 40px 40px;"></div>
                </div>

                <!-- Standard Icon -->
                <div class="stat-icon" style="width: 60px; height: 60px; border-radius: 14px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 1rem; background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white; box-shadow: 0 6px 20px rgba(139, 92, 246, 0.25); position: relative; overflow: hidden;">
                    <i class="fas fa-book relative z-10"></i>
                </div>

                <!-- Standard Number Display -->
                <div class="stat-number" style="font-size: 2.5rem; font-weight: 800; margin-bottom: 0.5rem; background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; line-height: 1;">
                    {{ $articleCount ?? 89 }}
                </div>

                <!-- Standard Label -->
                <div class="stat-label" style="color: #374151; font-weight: 600; font-size: 1rem; margin-bottom: 0.5rem;">{{ translateText('ټولې مقالې') }}</div>

                <!-- Standard Description -->
                <div class="stat-description" style="color: #6b7280; font-size: 0.85rem; margin-bottom: 1rem; line-height: 1.4;">
                    {{ translateText('د ټولو مقالو شمیر چې دلته شته') }}
                </div>

            
                <!-- Hover Effect Overlay -->
                <div class="absolute inset-0 bg-gradient-to-br from-purple-500/8 to-violet-500/8 opacity-0 hover:opacity-100 transition-all duration-300 rounded-16 pointer-events-none"></div>
            </div>

            <!-- Standard Feedback Card -->
            <div class="stat-card feedback" style="background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(240, 253, 244, 0.95) 100%); backdrop-filter: blur(15px); border-radius: 16px; padding: 1.5rem; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(34, 197, 94, 0.05); transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.3); cursor: pointer; height: 240px; display: flex; flex-direction: column; justify-content: space-between;">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-5">
                    <div style="background-image: radial-gradient(circle at 20% 80%, #22c55e 2px, transparent 2px), radial-gradient(circle at 80% 20%, #16a34a 2px, transparent 2px); background-size: 40px 40px;"></div>
                </div>

                <!-- Standard Icon -->
                <div class="stat-icon" style="width: 60px; height: 60px; border-radius: 14px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 1rem; background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%); color: white; box-shadow: 0 6px 20px rgba(34, 197, 94, 0.25); position: relative; overflow: hidden;">
                    <i class="fas fa-comments relative z-10"></i>
                </div>

                <!-- Standard Number Display -->
                <div class="stat-number" style="font-size: 2.5rem; font-weight: 800; margin-bottom: 0.5rem; background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; line-height: 1;">
                    {{ $feedbackCount ?? 342 }}
                </div>

                <!-- Standard Label -->
                <div class="stat-label" style="color: #374151; font-weight: 600; font-size: 1rem; margin-bottom: 0.5rem;">{{ translateText('ټول نظریات') }}</div>

                <!-- Standard Description -->
                <div class="stat-description" style="color: #6b7280; font-size: 0.85rem; margin-bottom: 1rem; line-height: 1.4;">
                    {{ translateText('د ټولو نظریاتو شمیر چې دلته راغلي دي') }}
                </div>

                

                <!-- Hover Effect Overlay -->
                <div class="absolute inset-0 bg-gradient-to-br from-green-500/8 to-emerald-500/8 opacity-0 hover:opacity-100 transition-all duration-300 rounded-16 pointer-events-none"></div>
            </div>

            <!-- Standard Videos Card -->
            <!-- <div class="stat-card videos" style="background: linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(254, 242, 242, 0.95) 100%); backdrop-filter: blur(15px); border-radius: 16px; padding: 1.5rem; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(239, 68, 68, 0.05); transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); position: relative; overflow: hidden; border: 1px solid rgba(255, 255, 255, 0.3); cursor: pointer; height: 240px; display: flex; flex-direction: column; justify-content: space-between;"> -->
                <!-- Background Pattern -->
                <!-- <div class="absolute inset-0 opacity-5">
                    <div style="background-image: radial-gradient(circle at 20% 80%, #ef4444 2px, transparent 2px), radial-gradient(circle at 80% 20%, #dc2626 2px, transparent 2px); background-size: 40px 40px;"></div>
                </div>

               
                <div class="stat-icon" style="width: 60px; height: 60px; border-radius: 14px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 1rem; background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; box-shadow: 0 6px 20px rgba(239, 68, 68, 0.25); position: relative; overflow: hidden;">
                    <i class="fas fa-video relative z-10"></i>
                </div>

                <div class="stat-number" style="font-size: 2.5rem; font-weight: 800; margin-bottom: 0.5rem; background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; line-height: 1;">
                    {{ $videoCount ?? 67 }}
                </div>

                <div class="stat-label" style="color: #374151; font-weight: 600; font-size: 1rem; margin-bottom: 0.5rem;">{{ translateText('ټول ویدیوګانی') }}</div>

               
                <div class="stat-description" style="color: #6b7280; font-size: 0.85rem; margin-bottom: 1rem; line-height: 1.4;">
                    {{ translateText('د ټولو ویدیوګانو شمیر چې دلته شته') }}
                </div>

             
                
                <div class="absolute inset-0 bg-gradient-to-br from-red-500/8 to-pink-500/8 opacity-0 hover:opacity-100 transition-all duration-300 rounded-16 pointer-events-none"></div>
            </div> -->
        </div>


        <!-- Enhanced Charts Section -->
        <div class="chart-container">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        {{ translateText('میاشتنې پرمختګ') }}
                    </h3>
                    <div class="chart-actions">
                        <button class="chart-btn active"><span>{{ translateText('میاشتنی') }}</span></button>
                        <button class="chart-btn"><span>{{ translateText('کلنی') }}</span></button>
                        <button class="chart-btn"><span>{{ translateText('اونیزه') }}</span></button>
                    </div>
                </div>
                <div class="chart-content">
                    <div class="h-80 flex items-center justify-center rounded-lg">
                        <div class="text-center" id="chart-loading-1">
                            <div class="loading-placeholder w-16 h-16 rounded-full mx-auto mb-4"></div>
                            <p class="text-gray-500 font-medium">{{ translateText('د چارټ ډیټا د لوډولو په حال کې...') }}</p>
                        </div>
                        <canvas id="patientsChart" class="hidden w-full h-full"></canvas>
                    </div>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-users"></i>
                        {{ translateText('د ناروغانو ډیموګرافي') }}
                    </h3>
                    <div class="chart-actions">
                        <button class="chart-btn active"><span>{{ translateText('جنسیت') }}</span></button>
                        <button class="chart-btn"><span>{{ translateText('عمر') }}</span></button>
                        <button class="chart-btn"><span>{{ translateText('سیمه') }}</span></button>
                    </div>
                </div>
                <div class="chart-content">
                    <div class="h-80 flex items-center justify-center rounded-lg">
                        <div class="text-center" id="chart-loading-2">
                            <div class="loading-placeholder w-16 h-16 rounded-full mx-auto mb-4"></div>
                            <p class="text-gray-500 font-medium">{{ translateText('د ډیموګرافي ډیټا د لوډولو په حال کې...') }}</p>
                        </div>
                        <canvas id="demographicsChart" class="hidden w-full h-full"></canvas>
                    </div>
                </div>
            </div>
        </div>

      
        <!-- Patient Charts Section -->
        <div class="charts-section" style="margin-top: 2rem; margin-bottom: 2rem;">
            <div class="charts-header" style="text-align: center; margin-bottom: 2rem;">
                <h3 style="font-size: 1.5rem; font-weight: bold; color: #374151; margin-bottom: 0.5rem;">
                    <i class="fas fa-chart-bar" style="margin-left: 0.5rem; color: #3b82f6;"></i>
                    {{ translateText('د ناروغانو ډیټا چارټونه') }}
                </h3>
                <p style="color: #6b7280; font-size: 0.9rem;">{{ translateText('د ثبت شوي ناروغانو تحلیلي معلومات') }}</p>
            </div>

            <div class="charts-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 2rem; max-width: 1400px; margin: 0 auto;">

                <!-- Gender Distribution Chart -->
                <div class="chart-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 20px; padding: 1.5rem; box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3); color: white; position: relative; overflow: hidden;">
                    <div class="chart-decoration" style="position: absolute; top: -50px; right: -50px; width: 100px; height: 100px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; opacity: 0.5;"></div>
                    <div class="chart-header" style="margin-bottom: 1.5rem; position: relative; z-index: 2;">
                        <h4 style="font-size: 1.2rem; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center;">
                            <i class="fas fa-venus-mars" style="margin-left: 0.5rem; color: #fbbf24;"></i>
                            {{ translateText('د جنسیت له مخې ویش') }}
                        </h4>
                        <p style="font-size: 0.85rem; opacity: 0.9;">{{ translateText('د نارینه او ښځینه ناروغانو شمیر') }}</p>
                    </div>
                    <div class="chart-container" style="height: 300px; position: relative; z-index: 2;">
                        <canvas id="genderChart" style="width: 100%; height: 100%;"></canvas>
                    </div>
                </div>

                <!-- Age Groups Chart -->
                <div class="chart-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border-radius: 20px; padding: 1.5rem; box-shadow: 0 10px 30px rgba(240, 147, 251, 0.3); color: white; position: relative; overflow: hidden;">
                    <div class="chart-decoration" style="position: absolute; top: -50px; right: -50px; width: 100px; height: 100px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; opacity: 0.5;"></div>
                    <div class="chart-header" style="margin-bottom: 1.5rem; position: relative; z-index: 2;">
                        <h4 style="font-size: 1.2rem; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center;">
                            <i class="fas fa-users" style="margin-left: 0.5rem; color: #fbbf24;"></i>
                            {{ translateText('د عمر له مخې ویش') }}
                        </h4>
                        <p style="font-size: 0.85rem; opacity: 0.9;">{{ translateText('د مختلفو عمر ډلو ناروغان') }}</p>
                    </div>
                    <div class="chart-container" style="height: 300px; position: relative; z-index: 2;">
                        <canvas id="ageChart" style="width: 100%; height: 100%;"></canvas>
                    </div>
                </div>

                <!-- Monthly Registration Chart -->
                <div class="chart-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border-radius: 20px; padding: 1.5rem; box-shadow: 0 10px 30px rgba(79, 172, 254, 0.3); color: white; position: relative; overflow: hidden;">
                    <div class="chart-decoration" style="position: absolute; top: -50px; right: -50px; width: 100px; height: 100px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; opacity: 0.5;"></div>
                    <div class="chart-header" style="margin-bottom: 1.5rem; position: relative; z-index: 2;">
                        <h4 style="font-size: 1.2rem; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center;">
                            <i class="fas fa-calendar-alt" style="margin-left: 0.5rem; color: #fbbf24;"></i>
                            {{ translateText('میاشتني ثبتونه') }}
                        </h4>
                        <p style="font-size: 0.85rem; opacity: 0.9;">{{ translateText('د دغه کال میاشتني ثبت شوي ناروغان') }}</p>
                    </div>
                    <div class="chart-container" style="height: 300px; position: relative; z-index: 2;">
                        <canvas id="monthlyChart" style="width: 100%; height: 100%;"></canvas>
                    </div>
                </div>

                <!-- Province Distribution Chart -->
                <div class="chart-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); border-radius: 20px; padding: 1.5rem; box-shadow: 0 10px 30px rgba(250, 112, 154, 0.3); color: white; position: relative; overflow: hidden;">
                    <div class="chart-decoration" style="position: absolute; top: -50px; right: -50px; width: 100px; height: 100px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; opacity: 0.5;"></div>
                    <div class="chart-header" style="margin-bottom: 1.5rem; position: relative; z-index: 2;">
                        <h4 style="font-size: 1.2rem; font-weight: 600; margin-bottom: 0.5rem; display: flex; align-items: center;">
                            <i class="fas fa-map-marker-alt" style="margin-left: 0.5rem; color: #fbbf24;"></i>
                            {{ translateText('د ولایتونو له مخې ویش') }}
                        </h4>
                        <p style="font-size: 0.85rem; opacity: 0.9;">{{ translateText('د مختلفو ولایتونو ناروغان') }}</p>
                    </div>
                    <div class="chart-container" style="height: 300px; position: relative; z-index: 2;">
                        <canvas id="provinceChart" style="width: 100%; height: 100%;"></canvas>
                    </div>
                </div>

            </div>
        </div>

        <!-- Question Charts Section -->
        <div class="question-charts-section" style="margin-top: 3rem; margin-bottom: 2rem;">
            <div class="charts-header" style="text-align: center; margin-bottom: 2rem; position: relative;">
                <h3 style="font-size: 1.5rem; font-weight: bold; color: #374151; margin-bottom: 0.5rem;">
                    <i class="fas fa-question-circle" style="margin-left: 0.5rem; color: #8b5cf6;"></i>
                    {{ translateText('د پوښتنو ځوابونو چارټونه') }}
                </h3>
                <p style="color: #6b7280; font-size: 0.9rem;">{{ translateText('د هرې پوښتنې لپاره د ټولو انتخابونو تحلیل') }}</p>

            </div>

            <div class="question-charts-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem; max-width: 1600px; margin: 0 auto;">

                @if(isset($questionChartData) && count($questionChartData) > 0)
                    @foreach($questionChartData as $index => $question)
                        <div class="question-chart-card" style="background: linear-gradient(135deg,
                            @if($index % 4 == 0) #667eea 0%, #764ba2 100%
                            @elseif($index % 4 == 1) #f093fb 0%, #f5576c 100%
                            @elseif($index % 4 == 2) #4facfe 0%, #00f2fe 100%
                            @else #fa709a 0%, #fee140 100%
                            @endif
                        ); border-radius: 16px; padding: 1.2rem; box-shadow: 0 8px 25px rgba(0,0,0,0.15); color: white; position: relative; overflow: hidden; min-height: 320px;">

                            <div class="chart-decoration" style="position: absolute; top: -30px; right: -30px; width: 80px; height: 80px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; opacity: 0.6;"></div>

                            <div class="chart-header" style="margin-bottom: 1rem; position: relative; z-index: 2;">
                                <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 0.3rem; display: flex; align-items: center;">
                                    <i class="fas fa-chart-pie" style="margin-left: 0.4rem; color: #fbbf24; font-size: 0.9rem;"></i>
                                    {{ $question['short_text'] }}
                                </h4>
                                <p style="font-size: 0.75rem; opacity: 0.9; line-height: 1.3; max-height: 2.6rem; overflow: hidden;">
                                    {{ Str::limit($question['question_text'], 60) }}
                                </p>
                                <div style="font-size: 0.7rem; opacity: 0.8; margin-top: 0.3rem;">
                                    <i class="fas fa-users" style="margin-left: 0.3rem;"></i>
                                    {{ $question['total_responses'] }} ځوابونه
                                </div>
                            </div>

                            <div class="chart-container" style="height: 200px; position: relative; z-index: 2;">
                                <canvas id="questionChart{{ $question['question_number'] }}" style="width: 100%; height: 100%;"></canvas>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-radius: 16px; color: #6b7280;">
                        <i class="fas fa-chart-bar" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <h4 style="font-size: 1.2rem; margin-bottom: 0.5rem;">{{ translateText('د پوښتنو ډیټا شتون نلري') }}</h4>
                        <p style="font-size: 0.9rem;">{{ translateText('کله چې کارونکي پوښتنې ځواب ورکړي، دلته به چارټونه ښکاره شي') }}</p>
                    </div>
                @endif

            </div>
        </div>

          <!-- Enhanced Recent Activity -->
       


        <!-- Enhanced Footer -->
        <div class="dashboard-footer">
            <div class="flex items-center justify-center gap-4 mb-2">
                <i class="fas fa-university text-2xl text-blue-600"></i>
                <span class="text-lg font-bold">{{ translateText('د کندهار پوهنتون') }}</span>
            </div>
            <p class="text-sm">
                © ۱۴۰۳ - {{ translateText('د رواني روغتیا مرکز') }} - {{ translateText('د کندهار پوهنتون') }}
            </p>
            <div class="flex items-center justify-center gap-6 mt-3 text-sm">
                <span class="flex items-center gap-1">
                    <i class="fas fa-shield-alt text-green-500"></i>
                    {{ translateText('محفوظ سیسټم') }}
                </span>
                <span class="flex items-center gap-1">
                    <i class="fas fa-clock text-blue-500"></i>
                    {{ translateText('۲۴/۷ فعال') }}
                </span>
                <span class="flex items-center gap-1">
                    <i class="fas fa-heart text-red-500"></i>
                    {{ translateText('د ناروغانو خدماتو لپاره') }}
                </span>
            </div>
        </div>
    </div>
    </div>
    <script>
        // Enhanced Dashboard JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Debug: Check if styles are loaded
            console.log('Dashboard JavaScript loaded');
            const testDiv = document.querySelector('.test-styles-loaded');
            if (testDiv) {
                console.log('Test div found - styles should be working');
            }

            // Check if dashboard container has styles
            const dashboardContainer = document.querySelector('.dashboard-container');
            if (dashboardContainer) {
                const styles = window.getComputedStyle(dashboardContainer);
                console.log('Dashboard container background:', styles.background);
            }

            // Initialize loading animations
            initializeLoadingAnimations();

            // Initialize date and time
            updateDateTime();
            setInterval(updateDateTime, 1000);

            // Initialize charts with delay for better UX
            setTimeout(initializeCharts, 1500);

            // Initialize interactive elements
            initializeInteractiveElements();

            // Initialize dynamic functionality
            initializeDynamicFeatures();

            // Load question charts dynamically
            setTimeout(loadInitialQuestionCharts, 2000);
        });

        // Update date and time
        function updateDateTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            };

            // Update banner date
            const bannerDate = document.getElementById('current-date-banner');
            if (bannerDate) {
                bannerDate.textContent = now.toLocaleDateString('fa-AF', options);
            }

            // Update live time
            const liveTime = document.getElementById('live-time');
            if (liveTime) {
                liveTime.textContent = now.toLocaleTimeString('fa-AF', {
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }
        }

        // Initialize loading animations
        function initializeLoadingAnimations() {
            // Animate stat cards
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // Animate quick action buttons
            const quickActions = document.querySelectorAll('.quick-action-btn');
            quickActions.forEach((btn, index) => {
                btn.style.animationDelay = `${index * 0.1}s`;
            });
        }

        // Initialize charts
        function initializeCharts() {
            // Hide loading indicators
            document.getElementById('chart-loading-1')?.classList.add('hidden');
            document.getElementById('chart-loading-2')?.classList.add('hidden');

            // Show charts
            document.getElementById('patientsChart')?.classList.remove('hidden');
            document.getElementById('demographicsChart')?.classList.remove('hidden');

            // Get patient data from PHP
            const patientData = @json($patientChartData ?? []);

            // Get question data from PHP
            const questionData = @json($questionChartData ?? []);

            // Gender Distribution Chart
            const genderCtx = document.getElementById('genderChart');
            if (genderCtx && patientData.genderData) {
                new Chart(genderCtx, {
                    type: 'doughnut',
                    data: {
                        labels: Object.keys(patientData.genderData),
                        datasets: [{
                            label: 'د جنسیت له مخې ناروغان',
                            data: Object.values(patientData.genderData),
                            backgroundColor: [
                                '#3b82f6',
                                '#ec4899',
                                '#10b981',
                                '#f59e0b'
                            ],
                            borderWidth: 3,
                            borderColor: '#fff',
                            hoverBorderWidth: 5,
                            hoverOffset: 15
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    usePointStyle: true,
                                    padding: 20,
                                    font: {
                                        size: 14,
                                        weight: '600'
                                    },
                                    color: '#fff'
                                }
                            }
                        },
                        cutout: '60%',
                        animation: {
                            animateRotate: true,
                            duration: 2000
                        }
                    }
                });
            }

            // Age Groups Chart
            const ageCtx = document.getElementById('ageChart');
            if (ageCtx && patientData.ageGroups) {
                new Chart(ageCtx, {
                    type: 'bar',
                    data: {
                        labels: Object.keys(patientData.ageGroups),
                        datasets: [{
                            label: 'د عمر له مخې ناروغان',
                            data: Object.values(patientData.ageGroups),
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(239, 68, 68, 0.8)'
                            ],
                            borderColor: [
                                '#3b82f6',
                                '#10b981',
                                '#f59e0b',
                                '#ef4444'
                            ],
                            borderWidth: 2,
                            borderRadius: 8,
                            borderSkipped: false,
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.2)',
                                    drawBorder: false
                                },
                                ticks: {
                                    font: {
                                        size: 12
                                    },
                                    color: '#fff'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 12
                                    },
                                    color: '#fff'
                                }
                            }
                        }
                    }
                });
            }

            // Monthly Registration Chart
            const monthlyCtx = document.getElementById('monthlyChart');
            if (monthlyCtx && patientData.monthlyData) {
                new Chart(monthlyCtx, {
                    type: 'line',
                    data: {
                        labels: Object.keys(patientData.monthlyData),
                        datasets: [{
                            label: 'میاشتني ثبتونه',
                            data: Object.values(patientData.monthlyData),
                            borderColor: '#fff',
                            backgroundColor: 'rgba(255, 255, 255, 0.2)',
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#fff',
                            pointBorderColor: '#fff',
                            pointBorderWidth: 3,
                            pointRadius: 6,
                            pointHoverRadius: 8,
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.2)',
                                    drawBorder: false
                                },
                                ticks: {
                                    font: {
                                        size: 12
                                    },
                                    color: '#fff'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 12
                                    },
                                    color: '#fff'
                                }
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        }
                    }
                });
            }

            // Province Distribution Chart
            const provinceCtx = document.getElementById('provinceChart');
            if (provinceCtx && patientData.provinceData) {
                const provinceLabels = Object.keys(patientData.provinceData);
                const provinceValues = Object.values(patientData.provinceData);

                new Chart(provinceCtx, {
                    type: 'pie',
                    data: {
                        labels: provinceLabels,
                        datasets: [{
                            label: 'د ولایتونو له مخې ناروغان',
                            data: provinceValues,
                            backgroundColor: [
                                '#3b82f6',
                                '#10b981',
                                '#f59e0b',
                                '#ef4444',
                                '#8b5cf6',
                                '#06b6d4',
                                '#84cc16',
                                '#f97316'
                            ],
                            borderWidth: 3,
                            borderColor: '#fff',
                            hoverBorderWidth: 5,
                            hoverOffset: 10
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    usePointStyle: true,
                                    padding: 15,
                                    font: {
                                        size: 12,
                                        weight: '600'
                                    },
                                    color: '#fff'
                                }
                            }
                        },
                        animation: {
                            animateRotate: true,
                            duration: 2000
                        }
                    }
                });
            }

            // Question Charts
            if (questionData && questionData.length > 0) {
                questionData.forEach(function(question) {
                    const questionCtx = document.getElementById('questionChart' + question.question_number);
                    if (questionCtx) {
                        const labels = Object.values(question.options);
                        const data = Object.values(question.counts);

                        new Chart(questionCtx, {
                            type: 'doughnut',
                            data: {
                                labels: labels,
                                datasets: [{
                                    label: 'ځوابونه',
                                    data: data,
                                    backgroundColor: [
                                        'rgba(59, 130, 246, 0.8)',   // Blue for A
                                        'rgba(16, 185, 129, 0.8)',   // Green for B
                                        'rgba(245, 158, 11, 0.8)',   // Yellow for C
                                        'rgba(239, 68, 68, 0.8)'     // Red for D
                                    ],
                                    borderColor: [
                                        '#3b82f6',
                                        '#10b981',
                                        '#f59e0b',
                                        '#ef4444'
                                    ],
                                    borderWidth: 2,
                                    hoverBorderWidth: 3,
                                    hoverOffset: 8
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'bottom',
                                        labels: {
                                            usePointStyle: true,
                                            padding: 12,
                                            font: {
                                                size: 11,
                                                weight: '600'
                                            },
                                            color: '#fff'
                                        }
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                                const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                                            }
                                        }
                                    }
                                },
                                cutout: '50%',
                                animation: {
                                    animateRotate: true,
                                    duration: 1500
                                }
                            }
                        });
                    }
                });
            }

            // Existing charts (if they exist)
            const patientsCtx = document.getElementById('patientsChart');
            if (patientsCtx) {
                new Chart(patientsCtx, {
                    type: 'line',
                    data: {
                        labels: ['جنوري', 'فبروري', 'مارچ', 'اپریل', 'مۍ', 'جون', 'جولای'],
                        datasets: [{
                            label: 'د ناروغانو شمیر',
                            data: [85, 92, 104, 98, 112, 125, 134],
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#fff',
                            pointBorderColor: '#667eea',
                            pointBorderWidth: 3,
                            pointRadius: 6,
                            pointHoverRadius: 8,
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    usePointStyle: true,
                                    padding: 20,
                                    font: {
                                        size: 14,
                                        weight: '600'
                                    }
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: false,
                                grid: {
                                    color: 'rgba(102, 126, 234, 0.1)',
                                    drawBorder: false
                                },
                                ticks: {
                                    stepSize: 20,
                                    font: {
                                        size: 12
                                    },
                                    color: '#718096'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: {
                                        size: 12
                                    },
                                    color: '#718096'
                                }
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        }
                    }
                });
            }

            const demoCtx = document.getElementById('demographicsChart');
            if (demoCtx) {
                new Chart(demoCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['نارینه (۱۸-۳۰)', 'نارینه (۳۰+)', 'ښځینه (۱۸-۳۰)', 'ښځینه (۳۰+)', 'نور'],
                        datasets: [{
                            label: 'ناروغان',
                            data: [120, 85, 150, 95, 30],
                            backgroundColor: [
                                '#667eea',
                                '#764ba2',
                                '#f093fb',
                                '#f5576c',
                                '#4facfe'
                            ],
                            borderWidth: 3,
                            borderColor: '#fff',
                            hoverBorderWidth: 5,
                            hoverOffset: 10
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    usePointStyle: true,
                                    padding: 20,
                                    font: {
                                        size: 12,
                                        weight: '600'
                                    }
                                }
                            }
                        },
                        cutout: '60%',
                        animation: {
                            animateRotate: true,
                            duration: 2000
                        }
                    }
                });
            }
        }

        // Initialize interactive elements
        function initializeInteractiveElements() {
            // Chart button interactions with dynamic data loading
            const chartBtns = document.querySelectorAll('.chart-btn');
            chartBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const parent = this.closest('.chart-actions');
                    const chartCard = this.closest('.chart-card');
                    const chartType = chartCard.querySelector('.chart-title i').classList.contains('fa-chart-line') ? 'progress' : 'demographics';

                    // Remove active class from siblings
                    parent.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // Get button text to determine filter/period
                    const buttonText = this.textContent.trim();
                    let filter = 'monthly';

                    if (chartType === 'progress') {
                        if (buttonText.includes('میاشتنی')) filter = 'monthly';
                        else if (buttonText.includes('کلنی')) filter = 'yearly';
                        else if (buttonText.includes('اونیزه')) filter = 'weekly';

                        loadDynamicChartData('progress', filter, chartCard);
                    } else if (chartType === 'demographics') {
                        if (buttonText.includes('جنسیت')) filter = 'gender';
                        else if (buttonText.includes('عمر')) filter = 'age';
                        else if (buttonText.includes('سیمه')) filter = 'region';

                        loadDynamicChartData('demographics', filter, chartCard);
                    }
                });
            });

            // Stat card hover effects
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Quick action button effects
            const quickActionBtns = document.querySelectorAll('.quick-action-btn');
            quickActionBtns.forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px) scale(1.02)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Activity item interactions
            const activityItems = document.querySelectorAll('.activity-item');
            activityItems.forEach(item => {
                item.addEventListener('click', function() {
                    // Add click effect
                    this.style.transform = 'translateX(12px)';
                    setTimeout(() => {
                        this.style.transform = 'translateX(8px)';
                    }, 150);
                });
            });

            // Add smooth scrolling for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add loading states for buttons
            const actionButtons = document.querySelectorAll('.quick-action-btn, .view-all-btn');
            actionButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    // Don't prevent default for actual navigation
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> در حال لوډولو...';

                    // Reset after a short delay (for demo purposes)
                    setTimeout(() => {
                        this.innerHTML = originalText;
                    }, 2000);
                });
            });
        }

        // Add notification system
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' :
                type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
            } text-white`;

            notification.innerHTML = `
                <div class="flex items-center gap-2">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : type === 'warning' ? 'exclamation' : 'info'}-circle"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // Add real-time updates simulation
        function simulateRealTimeUpdates() {
            setInterval(() => {
                // Randomly update stat numbers (for demo)
                const statNumbers = document.querySelectorAll('.stat-number');
                statNumbers.forEach(num => {
                    const currentValue = parseInt(num.textContent);
                    const change = Math.floor(Math.random() * 3) - 1; // -1, 0, or 1
                    if (change !== 0) {
                        num.textContent = Math.max(0, currentValue + change);

                        // Add animation
                        num.style.transform = 'scale(1.1)';
                        num.style.color = change > 0 ? '#10b981' : '#ef4444';

                        setTimeout(() => {
                            num.style.transform = 'scale(1)';
                            num.style.color = '';
                        }, 500);
                    }
                });
            }, 30000); // Update every 30 seconds
        }

        // Initialize real-time updates
        setTimeout(simulateRealTimeUpdates, 5000);

        // Performance monitoring
        function monitorPerformance() {
            // Monitor page load time
            window.addEventListener('load', function() {
                const loadTime = performance.now();
                console.log(`Dashboard loaded in ${Math.round(loadTime)}ms`);

                if (loadTime > 3000) {
                    showNotification('صفحه د ډیر وخت وروسته لوډ شوه. د انټرنیټ اتصال وګورئ.', 'warning');
                }
            });

            // Monitor memory usage (if available)
            if ('memory' in performance) {
                setInterval(() => {
                    const memory = performance.memory;
                    if (memory.usedJSHeapSize > 50 * 1024 * 1024) { // 50MB
                        console.warn('High memory usage detected');
                    }
                }, 60000);
            }
        }

        // Initialize performance monitoring
        monitorPerformance();

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + D for dashboard
            if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                e.preventDefault();
                window.location.href = '/dashboard';
            }

            // Ctrl/Cmd + P for patients
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                window.location.href = '/patientd';
            }

            // Ctrl/Cmd + N for new patient
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                window.location.href = '/patientd';
            }
        });

        // Add accessibility improvements
        function improveAccessibility() {
            // Add ARIA labels to interactive elements
            const interactiveElements = document.querySelectorAll('.stat-card, .quick-action-btn, .activity-item');
            interactiveElements.forEach(element => {
                if (!element.getAttribute('aria-label')) {
                    const text = element.textContent.trim();
                    element.setAttribute('aria-label', text);
                }
                element.setAttribute('tabindex', '0');
            });

            // Add keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    const focused = document.activeElement;
                    if (focused.classList.contains('stat-card') ||
                        focused.classList.contains('quick-action-btn') ||
                        focused.classList.contains('activity-item')) {
                        e.preventDefault();
                        focused.click();
                    }
                }
            });
        }

        // Initialize accessibility improvements
        improveAccessibility();

        // Add error handling (disabled to prevent false error notifications)
        window.addEventListener('error', function(e) {
            console.error('Dashboard error:', e.error);
            // Disabled error notifications to prevent false positives
            // showNotification('د سیسټم کې ستونزه رامنځته شوه. مهرباني وکړئ بیا هڅه وکړئ.', 'error');
        });

        // Add offline detection
        window.addEventListener('online', function() {
            showNotification('انټرنیټ اتصال بیرته راغی!', 'success');
        });

        window.addEventListener('offline', function() {
            showNotification('انټرنیټ اتصال قطع شو. ځینې فیچرونه کار نه کوي.', 'warning');
        });

        // Standard Dashboard Search Functionality
        const searchInput = document.getElementById('dashboard-search');
        const searchContainer = document.querySelector('.search-container');

        // Create standard search results dropdown
        const searchResults = document.createElement('div');
        searchResults.className = 'search-results';
        searchContainer.appendChild(searchResults);

        // Sample search data (you can replace this with actual data from your backend)
        const searchData = [
            { type: 'patient', name: 'احمد علی', id: '001', category: 'ناروغ' },
            { type: 'patient', name: 'فاطمه خان', id: '002', category: 'ناروغ' },
            { type: 'doctor', name: 'ډاکټر محمد حسن', id: 'D001', category: 'ډاکټر' },
            { type: 'doctor', name: 'ډاکټر عایشه احمد', id: 'D002', category: 'ډاکټر' },
            { type: 'appointment', name: 'د نن ملاقاتونه', id: 'A001', category: 'ملاقات' },
            { type: 'report', name: 'میاشتنی راپور', id: 'R001', category: 'راپور' },
            { type: 'medication', name: 'د درملو لیست', id: 'M001', category: 'درمل' }
        ];

        // Search input event listener
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase().trim();

            if (query.length < 2) {
                searchResults.style.display = 'none';
                return;
            }

            const filteredResults = searchData.filter(item =>
                item.name.toLowerCase().includes(query) ||
                item.category.toLowerCase().includes(query) ||
                item.id.toLowerCase().includes(query)
            );

            displaySearchResults(filteredResults);
        });

        // Display search results
        function displaySearchResults(results) {
            if (results.length === 0) {
                searchResults.innerHTML = '<div class="search-result-item text-gray-500 text-center text-sm">هیڅ پایله ونه موندل شوه</div>';
            } else {
                searchResults.innerHTML = results.map(item => `
                    <div class="search-result-item" onclick="selectSearchResult('${item.type}', '${item.id}', '${item.name}')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <i class="fas fa-${getIconForType(item.type)} text-gray-400 w-4"></i>
                                <div>
                                    <div class="font-medium text-gray-900 text-sm">${item.name}</div>
                                    <div class="text-xs text-gray-500">${item.category}</div>
                                </div>
                            </div>
                            <div class="text-xs text-gray-400">ID: ${item.id}</div>
                        </div>
                    </div>
                `).join('');
            }

            searchResults.style.display = 'block';
        }

        // Get icon for search result type
        function getIconForType(type) {
            const icons = {
                'patient': 'user',
                'doctor': 'user-md',
                'appointment': 'calendar',
                'report': 'chart-bar',
                'medication': 'pills'
            };
            return icons[type] || 'search';
        }

        // Select search result
        function selectSearchResult(type, id, name) {
            searchInput.value = name;
            searchResults.style.display = 'none';

            // Show notification
            showNotification(`${name} وټاکل شو`, 'success');

            // Navigate based on type (you can customize these routes)
            const routes = {
                'patient': '/patientd',
                'doctor': '/doctorcopy',
                'appointment': '/appointments',
                'report': '/reports',
                'medication': '/medications'
            };

            if (routes[type]) {
                // You can uncomment this to enable navigation
                // window.location.href = routes[type];
            }
        }

        // Professional top-right search function
        window.performSearch = function() {
            const query = searchInput.value.trim();
            const searchBtn = document.querySelector('.search-btn');
            const searchWrapper = document.querySelector('.search-wrapper');

            if (query) {
                // Add professional loading state
                const originalContent = searchBtn.innerHTML;
                searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin text-sm"></i><span class="mr-1 text-sm">{{ translateText("لټون کیږي...") }}</span>';
                searchBtn.disabled = true;
                searchBtn.style.background = 'linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%)';

                // Add searching effect to wrapper
                searchWrapper.style.transform = 'translateY(-1px)';
                searchWrapper.style.boxShadow = '0 4px 12px rgba(99, 102, 241, 0.2)';
                searchWrapper.style.borderColor = '#6366f1';

                // Simulate professional search
                setTimeout(() => {
                    searchBtn.innerHTML = originalContent;
                    searchBtn.disabled = false;
                    searchBtn.style.background = '';
                    searchWrapper.style.transform = '';
                    searchWrapper.style.boxShadow = '';
                    searchWrapper.style.borderColor = '';

                    const resultCount = Math.floor(Math.random() * 50) + 1;
                    showProfessionalNotification(`د "${query}" لپاره ${resultCount} پایلې وموندل شوې`, 'success');
                }, 1200);

                showProfessionalNotification(`د "${query}" لپاره لټون پیل شو...`, 'info');
                console.log('Top-right search for:', query);
            } else {
                showProfessionalNotification('مهرباني وکړئ د لټون لپاره یو څه ولیکئ', 'warning');
                searchInput.focus();

                // Add professional shake animation
                searchWrapper.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    searchWrapper.style.animation = '';
                }, 500);
            }
        };

        // Quick actions toggle
        window.toggleQuickActions = function() {
            const menu = document.getElementById('quick-actions-menu');
            menu.classList.toggle('show');
        };

        // Close quick actions when clicking outside
        document.addEventListener('click', function(e) {
            const quickActions = document.querySelector('.quick-actions');
            const menu = document.getElementById('quick-actions-menu');

            if (!quickActions.contains(e.target)) {
                menu.classList.remove('show');
            }
        });

        // Professional advanced search modal
        window.showAdvancedSearch = function() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center backdrop-filter backdrop-blur-sm';
            modal.innerHTML = `
                <div class="bg-white rounded-3xl shadow-2xl max-w-lg w-full mx-4 transform transition-all duration-300 scale-95 opacity-0" id="advanced-modal">
                    <!-- Professional Header -->
                    <div class="p-8 border-b border-gray-100 bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 text-white rounded-t-3xl relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"></div>
                        <div class="relative z-10 flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <div class="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-filter backdrop-blur-sm">
                                    <i class="fas fa-search-plus text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold">پرمختللی لټون</h3>
                                    <p class="text-white/80 text-sm">د ډیرو فلټرونو سره لټون وکړئ</p>
                                </div>
                            </div>
                            <button onclick="closeAdvancedModal()" class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center hover:bg-white/30 transition-colors duration-200">
                                <i class="fas fa-times text-lg"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Professional Form -->
                    <div class="p-8">
                        <div class="space-y-6">
                            <!-- Search Type -->
                            <div class="space-y-3">
                                <label class="block text-sm font-bold text-gray-800 mb-3">د لټون ډول</label>
                                <div class="grid grid-cols-2 gap-3">
                                    <label class="relative cursor-pointer">
                                        <input type="radio" name="search_type" value="all" class="sr-only peer" checked>
                                        <div class="p-4 border-2 border-gray-200 rounded-xl peer-checked:border-indigo-500 peer-checked:bg-indigo-50 transition-all duration-200 hover:border-indigo-300">
                                            <div class="flex items-center gap-3">
                                                <i class="fas fa-globe text-indigo-500"></i>
                                                <span class="font-medium">ټول</span>
                                            </div>
                                        </div>
                                    </label>
                                    <label class="relative cursor-pointer">
                                        <input type="radio" name="search_type" value="patients" class="sr-only peer">
                                        <div class="p-4 border-2 border-gray-200 rounded-xl peer-checked:border-indigo-500 peer-checked:bg-indigo-50 transition-all duration-200 hover:border-indigo-300">
                                            <div class="flex items-center gap-3">
                                                <i class="fas fa-users text-indigo-500"></i>
                                                <span class="font-medium">ناروغان</span>
                                            </div>
                                        </div>
                                    </label>
                                    <label class="relative cursor-pointer">
                                        <input type="radio" name="search_type" value="doctors" class="sr-only peer">
                                        <div class="p-4 border-2 border-gray-200 rounded-xl peer-checked:border-indigo-500 peer-checked:bg-indigo-50 transition-all duration-200 hover:border-indigo-300">
                                            <div class="flex items-center gap-3">
                                                <i class="fas fa-user-md text-indigo-500"></i>
                                                <span class="font-medium">ډاکټران</span>
                                            </div>
                                        </div>
                                    </label>
                                    <label class="relative cursor-pointer">
                                        <input type="radio" name="search_type" value="reports" class="sr-only peer">
                                        <div class="p-4 border-2 border-gray-200 rounded-xl peer-checked:border-indigo-500 peer-checked:bg-indigo-50 transition-all duration-200 hover:border-indigo-300">
                                            <div class="flex items-center gap-3">
                                                <i class="fas fa-chart-bar text-indigo-500"></i>
                                                <span class="font-medium">راپورونه</span>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- Date Range -->
                            <div class="space-y-3">
                                <label class="block text-sm font-bold text-gray-800">د نیټې څانګه</label>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="relative">
                                        <input type="date" class="w-full border-2 border-gray-200 rounded-xl px-4 py-3 focus:outline-none focus:border-indigo-500 transition-colors duration-200">
                                        <label class="absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-gray-600">د پیل نیټه</label>
                                    </div>
                                    <div class="relative">
                                        <input type="date" class="w-full border-2 border-gray-200 rounded-xl px-4 py-3 focus:outline-none focus:border-indigo-500 transition-colors duration-200">
                                        <label class="absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-gray-600">د پای نیټه</label>
                                    </div>
                                </div>
                            </div>

                            <!-- Keywords -->
                            <div class="space-y-3">
                                <label class="block text-sm font-bold text-gray-800">کلیدي کلمې</label>
                                <div class="relative">
                                    <input type="text" placeholder="کلیدي کلمې ولیکئ..." class="w-full border-2 border-gray-200 rounded-xl px-4 py-3 pl-12 focus:outline-none focus:border-indigo-500 transition-colors duration-200">
                                    <i class="fas fa-key absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Professional Footer -->
                    <div class="p-8 border-t border-gray-100 bg-gray-50 rounded-b-3xl">
                        <div class="flex justify-end space-x-4 space-x-reverse">
                            <button onclick="closeAdvancedModal()" class="px-6 py-3 text-gray-600 border-2 border-gray-300 rounded-xl hover:bg-gray-100 transition-colors duration-200 font-medium">
                                لغوه کول
                            </button>
                            <button onclick="performAdvancedSearch(); closeAdvancedModal();" class="px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 font-bold shadow-lg hover:shadow-xl transform hover:scale-105">
                                <i class="fas fa-search mr-2"></i>
                                لټون پیل کړئ
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Animate modal in
            setTimeout(() => {
                const modalContent = document.getElementById('advanced-modal');
                modalContent.style.transform = 'scale(1)';
                modalContent.style.opacity = '1';
            }, 100);
        };

        // Close advanced modal
        window.closeAdvancedModal = function() {
            const modal = document.querySelector('.fixed.inset-0');
            const modalContent = document.getElementById('advanced-modal');

            modalContent.style.transform = 'scale(0.95)';
            modalContent.style.opacity = '0';

            setTimeout(() => {
                if (modal && modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
        };

        // Perform advanced search
        window.performAdvancedSearch = function() {
            showNotification('پرمختللی لټون پیل شو...', 'info');
            // Add your advanced search logic here
        };

        // Hide search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchContainer.contains(e.target)) {
                searchResults.style.display = 'none';
            }
        });

        // Search on Enter key
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Professional notification system
        function showProfessionalNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-6 right-6 z-50 transform transition-all duration-500 cubic-bezier(0.4, 0, 0.2, 1)`;

            const colors = {
                success: 'from-green-500 to-emerald-600',
                error: 'from-red-500 to-rose-600',
                warning: 'from-yellow-500 to-orange-600',
                info: 'from-blue-500 to-indigo-600'
            };

            const icons = {
                success: 'check-circle',
                error: 'times-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };

            notification.innerHTML = `
                <div class="bg-gradient-to-r ${colors[type]} text-white px-6 py-4 rounded-2xl shadow-2xl backdrop-filter backdrop-blur-sm border border-white border-opacity-20 max-w-sm">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <i class="fas fa-${icons[type]} text-lg"></i>
                        </div>
                        <div class="flex-1">
                            <p class="font-semibold text-sm leading-relaxed">${message}</p>
                        </div>
                        <button onclick="this.closest('.fixed').remove()" class="text-white hover:text-gray-200 transition-colors duration-200">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>
                    <div class="mt-2 h-1 bg-white bg-opacity-20 rounded-full overflow-hidden">
                        <div class="h-full bg-white bg-opacity-40 rounded-full animate-progress"></div>
                    </div>
                </div>
            `;

            // Initial position (off-screen)
            notification.style.transform = 'translateX(100%) translateY(-20px)';
            notification.style.opacity = '0';

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0) translateY(0)';
                notification.style.opacity = '1';
            }, 100);

            // Auto remove after 4 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%) translateY(-20px)';
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        document.body.removeChild(notification);
                    }
                }, 500);
            }, 4000);
        }

        // Add shake animation CSS
        const shakeStyle = document.createElement('style');
        shakeStyle.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                20%, 40%, 60%, 80% { transform: translateX(5px); }
            }

            @keyframes progress {
                0% { width: 0%; }
                100% { width: 100%; }
            }

            .animate-progress {
                animation: progress 4s linear;
            }
        `;
        document.head.appendChild(shakeStyle);

        // Dynamic chart data loading function
        function loadDynamicChartData(type, filter, chartCard) {
            const chartCanvas = chartCard.querySelector('canvas');
            const chartContent = chartCard.querySelector('.chart-content');
            const activeBtn = chartCard.querySelector('.chart-btn.active');

            // Add loading state to button
            if (activeBtn) {
                activeBtn.classList.add('loading');
            }

            // Show loading state
            chartContent.innerHTML = `
                <div class="h-80 flex items-center justify-center rounded-lg chart-loading">
                    <div class="text-center">
                        <div class="loading-placeholder w-16 h-16 rounded-full mx-auto mb-4"></div>
                        <p class="text-gray-500 font-medium">{{ translateText('د چارټ ډیټا د لوډولو په حال کې...') }}</p>
                    </div>
                </div>
            `;

            // Make AJAX request to get dynamic data
            fetch(`/dashboard/chart-data?type=${type}&period=${filter}&filter=${filter}`)
                .then(response => response.json())
                .then(data => {
                    // Remove loading state from button
                    if (activeBtn) {
                        activeBtn.classList.remove('loading');
                    }

                    // Restore canvas
                    chartContent.innerHTML = `<canvas id="${chartCanvas.id}" class="w-full h-full"></canvas>`;
                    const newCanvas = chartContent.querySelector('canvas');

                    // Create new chart with dynamic data
                    if (type === 'progress') {
                        createProgressChart(newCanvas, data);
                    } else if (type === 'demographics') {
                        createDemographicsChart(newCanvas, data);
                    }

                    showProfessionalNotification(`چارټ د ${filter} معلوماتو سره تازه شو`, 'success');
                })
                .catch(error => {
                    console.error('Error loading chart data:', error);

                    // Remove loading state from button
                    if (activeBtn) {
                        activeBtn.classList.remove('loading');
                    }

                    chartContent.innerHTML = `
                        <div class="h-80 flex items-center justify-center rounded-lg">
                            <div class="text-center text-red-500">
                                <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                                <p class="font-medium">{{ translateText('د چارټ ډیټا لوډولو کې ستونزه') }}</p>
                            </div>
                        </div>
                    `;
                    // Silently handle chart loading error
                });
        }

        // Create progress chart
        function createProgressChart(canvas, data) {
            new Chart(canvas, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: 'د ناروغانو شمیر',
                        data: data.data,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#fff',
                        pointBorderColor: '#667eea',
                        pointBorderWidth: 3,
                        pointRadius: 6,
                        pointHoverRadius: 8,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    size: 14,
                                    weight: '600'
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(102, 126, 234, 0.1)',
                                drawBorder: false
                            },
                            ticks: {
                                stepSize: 5,
                                font: {
                                    size: 12
                                },
                                color: '#718096'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 12
                                },
                                color: '#718096'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // Create demographics chart
        function createDemographicsChart(canvas, data) {
            const chartType = data.filter === 'age' ? 'bar' : 'doughnut';

            new Chart(canvas, {
                type: chartType,
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: 'ناروغان',
                        data: data.data,
                        backgroundColor: [
                            '#667eea',
                            '#764ba2',
                            '#f093fb',
                            '#f5576c',
                            '#4facfe',
                            '#00f2fe',
                            '#43e97b',
                            '#38f9d7'
                        ],
                        borderWidth: chartType === 'doughnut' ? 3 : 2,
                        borderColor: chartType === 'doughnut' ? '#fff' : [
                            '#667eea',
                            '#764ba2',
                            '#f093fb',
                            '#f5576c',
                            '#4facfe',
                            '#00f2fe',
                            '#43e97b',
                            '#38f9d7'
                        ],
                        hoverBorderWidth: chartType === 'doughnut' ? 5 : 3,
                        hoverOffset: chartType === 'doughnut' ? 10 : 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 20,
                                font: {
                                    size: 12,
                                    weight: '600'
                                }
                            }
                        }
                    },
                    cutout: chartType === 'doughnut' ? '60%' : 0,
                    scales: chartType === 'bar' ? {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(102, 126, 234, 0.1)',
                                drawBorder: false
                            },
                            ticks: {
                                font: {
                                    size: 12
                                },
                                color: '#718096'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 12
                                },
                                color: '#718096'
                            }
                        }
                    } : {},
                    animation: {
                        animateRotate: true,
                        duration: 2000
                    }
                }
            });
        }

        // Load dynamic recent activities
        function loadRecentActivities() {
            fetch('/dashboard/chart-data?type=activities')
                .then(response => response.json())
                .then(activities => {
                    const activityList = document.querySelector('.activity-list');
                    if (activityList && activities.length > 0) {
                        activityList.innerHTML = activities.map(activity => `
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-${activity.icon}"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-text">${activity.text}</div>
                                    <div class="activity-time">
                                        <i class="fas fa-clock"></i>
                                        ${activity.time}
                                    </div>
                                </div>
                            </div>
                        `).join('');

                        showProfessionalNotification('وروستي فعالیتونه تازه شول', 'success');
                    }
                })
                .catch(error => {
                    console.error('Error loading activities:', error);
                    // Silently handle error without showing notification
                });
        }

        // Refresh question charts dynamically
        function refreshQuestionCharts() {
            const refreshBtn = document.querySelector('.question-charts-section .refresh-btn');
            const originalContent = refreshBtn.innerHTML;

            // Show loading state
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>{{ translateText("لوډ کیږي...") }}</span>';
            refreshBtn.disabled = true;

            // Fetch fresh question data
            fetch('/dashboard/question-statistics')
                .then(response => response.json())
                .then(result => {
                    if (result.success && result.data) {
                        updateQuestionChartsDisplay(result.data);
                        showProfessionalNotification('د پوښتنو چارټونه تازه شول', 'success');
                    } else {
                        throw new Error(result.error || 'Unknown error');
                    }
                })
                .catch(error => {
                    console.error('Error refreshing question charts:', error);
                    // Silently handle error without showing notification
                })
                .finally(() => {
                    // Reset button
                    refreshBtn.innerHTML = originalContent;
                    refreshBtn.disabled = false;
                });
        }

        // Update question charts display with new data
        function updateQuestionChartsDisplay(questionData) {
            const questionChartsGrid = document.querySelector('.question-charts-grid');

            if (!questionChartsGrid || !questionData || questionData.length === 0) {
                // Show no data message
                if (questionChartsGrid) {
                    questionChartsGrid.innerHTML = `
                        <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-radius: 16px; color: #6b7280;">
                            <i class="fas fa-chart-bar" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                            <h4 style="font-size: 1.2rem; margin-bottom: 0.5rem;">{{ translateText('د پوښتنو ډیټا شتون نلري') }}</h4>
                            <p style="font-size: 0.9rem;">{{ translateText('کله چې کارونکي پوښتنې ځواب ورکړي، دلته به چارټونه ښکاره شي') }}</p>
                        </div>
                    `;
                }
                return;
            }

            // Generate new chart cards
            const chartCards = questionData.map((question, index) => {
                const gradients = [
                    '#667eea 0%, #764ba2 100%',
                    '#f093fb 0%, #f5576c 100%',
                    '#4facfe 0%, #00f2fe 100%',
                    '#fa709a 0%, #fee140 100%'
                ];
                const gradient = gradients[index % 4];

                return `
                    <div class="question-chart-card" style="background: linear-gradient(135deg, ${gradient}); border-radius: 16px; padding: 1.2rem; box-shadow: 0 8px 25px rgba(0,0,0,0.15); color: white; position: relative; overflow: hidden; min-height: 320px;">
                        <div class="chart-decoration" style="position: absolute; top: -30px; right: -30px; width: 80px; height: 80px; background: rgba(255, 255, 255, 0.1); border-radius: 50%; opacity: 0.6;"></div>

                        <div class="chart-header" style="margin-bottom: 1rem; position: relative; z-index: 2;">
                            <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 0.3rem; display: flex; align-items: center;">
                                <i class="fas fa-chart-pie" style="margin-left: 0.4rem; color: #fbbf24; font-size: 0.9rem;"></i>
                                ${question.short_text}
                            </h4>
                            <p style="font-size: 0.75rem; opacity: 0.9; line-height: 1.3; max-height: 2.6rem; overflow: hidden;">
                                ${question.question_text.length > 60 ? question.question_text.substring(0, 60) + '...' : question.question_text}
                            </p>
                            <div style="font-size: 0.7rem; opacity: 0.8; margin-top: 0.3rem;" class="response-count">
                                <i class="fas fa-users" style="margin-left: 0.3rem;"></i>
                                ${question.total_responses} ځوابونه
                            </div>
                        </div>

                        <div class="chart-container" style="height: 200px; position: relative; z-index: 2;">
                            <canvas id="questionChart${question.question_number}" style="width: 100%; height: 100%;"></canvas>
                        </div>
                    </div>
                `;
            }).join('');

            // Update the grid
            questionChartsGrid.innerHTML = chartCards;

            // Create charts for each question
            questionData.forEach(question => {
                const canvas = document.getElementById(`questionChart${question.question_number}`);
                if (canvas) {
                    createQuestionChart(canvas, question);
                }
            });
        }

        // Create individual question chart
        function createQuestionChart(canvas, questionData) {
            const labels = Object.values(questionData.options);
            const data = Object.values(questionData.counts);

            new Chart(canvas, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'ځوابونه',
                        data: data,
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',   // Blue for A
                            'rgba(16, 185, 129, 0.8)',   // Green for B
                            'rgba(245, 158, 11, 0.8)',   // Yellow for C
                            'rgba(239, 68, 68, 0.8)'     // Red for D
                        ],
                        borderColor: [
                            '#3b82f6',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444'
                        ],
                        borderWidth: 2,
                        hoverBorderWidth: 3,
                        hoverOffset: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 12,
                                font: {
                                    size: 11,
                                    weight: '600'
                                },
                                color: '#fff'
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                                    return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    cutout: '50%',
                    animation: {
                        animateRotate: true,
                        duration: 1500
                    }
                }
            });

            // Store total responses for comparison
            canvas.dataset.totalResponses = questionData.total_responses;
        }

        // Refresh recent activities
        function refreshRecentActivities() {
            const refreshBtn = document.querySelector('.activity-section .refresh-btn');
            const originalContent = refreshBtn.innerHTML;

            // Show loading state
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>{{ translateText("لوډ کیږي...") }}</span>';
            refreshBtn.disabled = true;

            // Load fresh activities
            loadRecentActivities();

            // Reset button after 2 seconds
            setTimeout(() => {
                refreshBtn.innerHTML = originalContent;
                refreshBtn.disabled = false;
            }, 2000);
        }

        // Auto-refresh question charts every 5 minutes
        setInterval(() => {
            console.log('Auto-refreshing question charts...');
            // Silently reload question data without showing loading state
            fetch('/dashboard/question-statistics')
                .then(response => response.json())
                .then(result => {
                    if (result.success && result.data && result.data.length > 0) {
                        // Check if any data has changed before updating
                        const hasChanges = checkForQuestionDataChanges(result.data);
                        if (hasChanges) {
                            updateQuestionChartsData(result.data);
                            console.log('Question charts updated with new data');
                        }
                    }
                })
                .catch(error => {
                    console.error('Auto-refresh error:', error);
                });
        }, 300000); // 5 minutes

        // Check if question data has changed
        function checkForQuestionDataChanges(newData) {
            let hasChanges = false;

            newData.forEach(question => {
                const canvas = document.getElementById(`questionChart${question.question_number}`);
                if (canvas) {
                    const currentTotal = parseInt(canvas.dataset.totalResponses || 0);
                    const newTotal = question.total_responses;

                    if (newTotal !== currentTotal) {
                        hasChanges = true;
                    }
                }
            });

            return hasChanges;
        }

        // Update question charts with new data
        function updateQuestionChartsData(questionData) {
            questionData.forEach(question => {
                const canvas = document.getElementById(`questionChart${question.question_number}`);
                if (canvas) {
                    const chart = Chart.getChart(canvas);
                    if (chart) {
                        // Update chart data
                        chart.data.datasets[0].data = Object.values(question.counts);
                        chart.update('none'); // Update without animation for auto-refresh

                        // Update stored total
                        canvas.dataset.totalResponses = question.total_responses;

                        // Update the response count display
                        const responseCount = canvas.closest('.question-chart-card').querySelector('.response-count');
                        if (responseCount) {
                            responseCount.innerHTML = `<i class="fas fa-users" style="margin-left: 0.3rem;"></i>${question.total_responses} ځوابونه`;
                        }
                    }
                }
            });
        }

        // Auto-refresh activities every 5 minutes
        setInterval(loadRecentActivities, 300000);

        // Initialize dynamic features
        function initializeDynamicFeatures() {
            // Add click handlers for chart buttons
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // Add visual feedback
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // Add hover effects for stat cards
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                });
            });

            // Initialize tooltips for chart buttons
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.title = `د ${btn.textContent.trim()} معلوماتو لپاره کلیک وکړئ`;
            });

            console.log('Dynamic dashboard features initialized');
        }

        // Load initial question charts
        function loadInitialQuestionCharts() {
            console.log('Loading initial question charts...');

            fetch('/dashboard/question-statistics')
                .then(response => response.json())
                .then(result => {
                    if (result.success && result.data) {
                        updateQuestionChartsDisplay(result.data);
                        console.log('Initial question charts loaded successfully');
                    } else {
                        console.warn('No question data available:', result.error);
                        // Show empty state
                        updateQuestionChartsDisplay([]);
                    }
                })
                .catch(error => {
                    console.error('Error loading initial question charts:', error);
                    // Show empty state instead of error to prevent user confusion
                    updateQuestionChartsDisplay([]);
                });
        }

        // Legacy notification function for compatibility
        function showNotification(message, type = 'info') {
            showProfessionalNotification(message, type);
        }

        // Professional Action Button Functions
        window.showNotifications = function() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center backdrop-filter backdrop-blur-sm';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95 opacity-0" id="notifications-modal">
                    <!-- Professional Header -->
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-t-2xl">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-bell text-xl"></i>
                                </div>
                                <h3 class="text-xl font-bold">{{ translateText('خبرتیاوی') }}</h3>
                            </div>
                            <button onclick="closeNotificationsModal()" class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center hover:bg-white/30 transition-colors duration-200">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Notifications Content -->
                    <div class="p-6 max-h-96 overflow-y-auto">
                        <div class="space-y-4">
                            <div class="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                                <i class="fas fa-info-circle text-blue-500 mt-1"></i>
                                <div>
                                    <p class="font-medium text-gray-800">{{ translateText('نوی ناروغ ثبت شو') }}</p>
                                    <p class="text-sm text-gray-600">{{ translateText('احمد علی د نوي ناروغ په توګه ثبت شو') }}</p>
                                    <span class="text-xs text-gray-500">{{ translateText('5 دقیقې دمخه') }}</span>
                                </div>
                            </div>

                            <div class="flex items-start gap-3 p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                                <i class="fas fa-check-circle text-green-500 mt-1"></i>
                                <div>
                                    <p class="font-medium text-gray-800">{{ translateText('ملاقات بشپړ شو') }}</p>
                                    <p class="text-sm text-gray-600">{{ translateText('د فاطمې خان ملاقات بریالي بشپړ شو') }}</p>
                                    <span class="text-xs text-gray-500">{{ translateText('15 دقیقې دمخه') }}</span>
                                </div>
                            </div>

                            <div class="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                                <i class="fas fa-exclamation-triangle text-yellow-500 mt-1"></i>
                                <div>
                                    <p class="font-medium text-gray-800">{{ translateText('سیسټم تازه شو') }}</p>
                                    <p class="text-sm text-gray-600">{{ translateText('د سیسټم نوي ورژن نصب شو') }}</p>
                                    <span class="text-xs text-gray-500">{{ translateText('1 ساعت دمخه') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="p-4 border-t border-gray-100 bg-gray-50 rounded-b-2xl">
                        <button onclick="closeNotificationsModal()" class="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium">
                            {{ translateText('ټول خبرتیاوی وګورئ') }}
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Animate modal in
            setTimeout(() => {
                const modalContent = document.getElementById('notifications-modal');
                modalContent.style.transform = 'scale(1)';
                modalContent.style.opacity = '1';
            }, 100);
        };

        window.closeNotificationsModal = function() {
            const modal = document.querySelector('.fixed.inset-0');
            const modalContent = document.getElementById('notifications-modal');

            modalContent.style.transform = 'scale(0.95)';
            modalContent.style.opacity = '0';

            setTimeout(() => {
                if (modal && modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
        };

        window.showNewUserModal = function() {
            showProfessionalNotification('د نوي یوزر جوړولو پاڼه پرانیستل کیږي...', 'info');
            // Add your new user modal logic here
            setTimeout(() => {
                showProfessionalNotification('دا فیچر ډیر ژر راتلونکي کې فعال کیږي', 'info');
            }, 1000);
        };

        window.confirmLogout = function() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-60 z-50 flex items-center justify-center backdrop-filter backdrop-blur-sm';
            modal.innerHTML = `
                <div class="bg-white rounded-2xl shadow-2xl max-w-sm w-full mx-4 transform transition-all duration-300 scale-95 opacity-0" id="logout-modal">
                    <!-- Professional Header -->
                    <div class="p-6 border-b border-gray-100 bg-gradient-to-r from-red-500 to-rose-600 text-white rounded-t-2xl">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                                <i class="fas fa-sign-out-alt text-xl"></i>
                            </div>
                            <h3 class="text-xl font-bold">{{ translateText('د سیسټم څخه وتل') }}</h3>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="p-6">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-question-circle text-red-500 text-2xl"></i>
                            </div>
                            <h4 class="text-lg font-bold text-gray-800 mb-2">{{ translateText('آیا ډاډه یاست؟') }}</h4>
                            <p class="text-gray-600 mb-6">{{ translateText('آیا غواړئ د سیسټم څخه وتل؟') }}</p>
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="p-4 border-t border-gray-100 bg-gray-50 rounded-b-2xl flex gap-3">
                        <button onclick="closeLogoutModal()" class="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors duration-200 font-medium">
                            {{ translateText('لغوه کول') }}
                        </button>
                        <button onclick="performLogout()" class="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 font-medium">
                            {{ translateText('هو، وتل') }}
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Animate modal in
            setTimeout(() => {
                const modalContent = document.getElementById('logout-modal');
                modalContent.style.transform = 'scale(1)';
                modalContent.style.opacity = '1';
            }, 100);
        };

        window.closeLogoutModal = function() {
            const modal = document.querySelector('.fixed.inset-0');
            const modalContent = document.getElementById('logout-modal');

            modalContent.style.transform = 'scale(0.95)';
            modalContent.style.opacity = '0';

            setTimeout(() => {
                if (modal && modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            }, 300);
        };

        window.performLogout = function() {
            showProfessionalNotification('د سیسټم څخه وتل کیږي...', 'info');
            // Add actual logout logic here
            setTimeout(() => {
                window.location.href = '/logout';
            }, 1500);
        };
    </script>

@endsection









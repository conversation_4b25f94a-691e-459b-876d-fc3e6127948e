@extends('layouts/Admin')

@section('title', 'Dashboard')

@section('contents')
<!-- Main Content -->
<!-- Header -->
<div class="header">
    <div class="dashboard-title"><i class="fas fa-brain"></i><span>د رواني روغتیا کنټرول پاڼه</span></div>
    <div class="header-actions flex space-x-3">
        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display:none">@csrf</form>
        <a href="#" onclick="event.preventDefault();document.getElementById('logout-form').submit();" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
            <i class="fas fa-sign-out-alt mr-2"></i><span>وتل</span>
        </a>
        <a href="{{ route('register') }}" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
            <i class="fas fa-user-plus mr-2"></i><span>نوی یوزر ثبت کړئ</span>
        </a>
        <a href="{{ route('notifications.index') }}" class="bg-white text-gray-700 px-5 py-2.5 rounded-lg font-medium flex items-center shadow-md hover:bg-gray-200 hover:text-black transition-all duration-300 border-0">
            <i class="fas fa-bell mr-2"></i><span>{{ translateText('خبرتیاوې') }}</span>
            <span id="dashboard-notification-badge" class="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1 hidden">0</span>
        </a>
    </div>
</div>

<!-- Messages -->
@if(session('success'))
<div class="alert alert-success" role="alert">
    {{ session('success') }}
</div>
@endif

@if(session('error'))
<div class="alert alert-danger" role="alert">
    {{ session('error') }}
</div>
@endif

<!-- Six Summary Cards with New Design -->
<div class="summary-cards">
    <!-- ناروغان -->
    <div class="summary-card ">
        <div class="card-decoration"></div>
        <div class="card-header">
            <div class="card-title font-semibold text-gray-500">ناروغان</div>
            <div class="card-icon">
                <i class="fas fa-users"></i>
            </div>
        </div>
        <div class="card-content">
            <div class="card-description text-[10px] text-gray-500">{{ $patientsCount ?? $patientCount ?? 0 }}</div>
            <div class="card-description text-[10px] text-gray-500">ټول ثبت شوي ناروغان</div>
        </div>
    </div>

    <!-- ډاکټران -->
    <div class="summary-card doctors">
        <div class="card-decoration"></div>
        <div class="card-header">
            <div class="card-title font-semibold text-gray-500">ډاکټران</div>
            <div class="card-icon">
                <i class="fas fa-user-md"></i>
            </div>
        </div>
        <div class="card-content">
            <div class="card-description text-[10px] text-gray-500">{{ $doctorCount ?? 0 }}</div>
            <div class="card-description text-[10px] text-gray-500">متخصیصن ډاکټران</div>
        </div>
    </div>

    <!-- نظریات -->
    <div class="summary-card comments">
        <div class="card-decoration"></div>
        <div class="card-header">
            <div class="card-title font-semibold text-gray-500">نظریات</div>
            <div class="card-icon">
                <i class="fas fa-comments"></i>
            </div>
        </div>
        <div class="card-content">
            <div class="card-description text-[10px] text-gray-500">{{ $commentCount ?? 0 }}</div>
            <div class="card-description text-[10px] text-gray-500">نوي نظرونه</div>
        </div>
    </div>

    <!-- خبرونه -->
    <div class="summary-card appointments">
        <div class="card-decoration"></div>
        <div class="card-header">
            <div class="card-title font-semibold text-gray-500">خبرونه</div>
            <div class="card-icon">
                <i class="fas fa-calendar-check"></i>
            </div>
        </div>
        <div class="card-content">
            <div class="card-description text-[10px] text-gray-500">{{ $newsCount ?? 0 }}</div>
            <div class="card-description text-[10px] text-gray-500">ټول خبرونه</div>
        </div>
    </div>

    <!-- مقالې -->
    <div class="summary-card articles">
        <div class="card-decoration"></div>
        <div class="card-header">
            <div class="card-title font-semibold text-gray-500">مقالې</div>
            <div class="card-icon">
                <i class="fas fa-book-medical"></i>
            </div>
        </div>
        <div class="card-content">
            <div class="card-description text-[10px] text-gray-500">{{ $articleCount ?? 0 }}</div>
            <div class="card-description text-[10px] text-gray-500">د رواني روغتیا مقالې</div>
        </div>
    </div>

    <!-- ویدیوګاني -->
 <div class="summary-card surveys text-[px]">
    <div class="card-decoration"></div>

    <div class="card-header flex justify-between items-center mb-1">
        <div class="card-title font-semibold text-gray-500">ویدیوګاني</div>
        <div class="card-icon text-indigo-500 text-sm">
            <i class="fas fa-poll"></i>
        </div>
    </div>

    <div class="card-content">
        <div class="card-value text-sm font-bold text-gray-800">{{ $videoCount ?? 0 }}</div>
        <div class="card-description text-[10px] text-gray-500">ټولې ویدیوګانې</div>
    </div>
</div>

<!-- Add this card to your dashboard grid -->
{{-- <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
    <div class="p-6">
        <div class="flex items-center">
            <div class="rounded-full bg-blue-100 p-3">
                <i class="fas fa-chart-bar text-blue-600 text-xl"></i>
            </div>
            <div class="ml-4">
                <h3 class="text-lg font-semibold text-gray-900">{{ translateText('د ناروغانو راپور') }}</h3>
                <p class="text-gray-600">{{ translateText('د ناروغانو شمېرنې او احصایې') }}</p>
            </div>
        </div>
        <div class="mt-4">
            <a href="{{ route('patient.report') }}" class="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300">
                {{ translateText('راپور کتل') }}
            </a>
        </div>
    </div>
</div> --}}

<!-- Charts Section -->
{{-- <div class="charts-container">
    <div class="chart-card">
        <div class="chart-header">
            <h3 class="chart-title">میاشتنې پرمختګ</h3>
            <div class="chart-actions">
                <button class="chart-btn active">میاشتنی</button>
                <button class="chart-btn">کلنی</button>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="patientsChart"></canvas>
        </div>
    </div>

    <div class="chart-card">
        <div class="chart-header">
            <h3 class="chart-title">د ناروغانو ډیموګرافي</h3>
            <div class="chart-actions">
                <button class="chart-btn active">جنسیت</button>
                <button class="chart-btn">عمر</button>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="demographicsChart"></canvas>
        </div>
    </div>
</div>

<!-- Footer --> --}}
<div class="footer">
    © ۱۴۰۳ - د رواني روغتیا مرکز - د کندهار پوهنتون
</div>

<!-- Initialize Charts -->
{{-- <script>
    // Initialize charts
    document.addEventListener('DOMContentLoaded', function() {
        // Patients Chart
        const patientsCtx = document.getElementById('patientsChart').getContext('2d');
        const patientsChart = new Chart(patientsCtx, {
            type: 'line',
            data: {
                labels: ['جنوري', 'فبروري', 'مارچ', 'اپریل', 'مۍ', 'جون', 'جولای'],
                datasets: [{
                    label: 'د ناروغانو شمیر',
                    data: [85, 92, 104, 98, 112, 125, 134],
                    borderColor: '#4361ee',
                    backgroundColor: 'rgba(67, 97, 238, 0.1)',
                    fill: true,
                    tension: 0.3,
                    pointBackgroundColor: '#fff',
                    pointBorderColor: '#4361ee',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        }); --}}
{{--
//         // Demographics Chart
//         const demographicsCtx = document.getElementById('demographicsChart').getContext('2d');
//         const demographicsChart = new Chart(demographicsCtx, {
//             type: 'doughnut',
//             data: {
//                 labels: ['نارینه', 'ښځینه'],
//                 datasets: [{
//                     data: [65, 35],
//                     backgroundColor: ['#4361ee', '#f72585'],
//                     borderWidth: 0,
//                     hoverOffset: 4
//                 }]
//             },
//             options: {
//                 responsive: true,
//                 maintainAspectRatio: false,
//                 plugins: {
//                     legend: {
//                         position: 'bottom'
//                     }
//                 },
//                 cutout: '70%'
//             }
//         });
//     });
// </script> --}}
@endsection

<script>
// Update dashboard notification badge
function updateDashboardNotificationBadge() {
    fetch('/api/notifications/count')
        .then(response => response.json())
        .then(data => {
            const badge = document.getElementById('dashboard-notification-badge');
            if (badge) {
                if (data.count > 0) {
                    badge.textContent = data.count;
                    badge.classList.remove('hidden');
                } else {
                    badge.classList.add('hidden');
                }
            }
        })
        .catch(error => {
            console.error('Error updating dashboard notification badge:', error);
        });
}

// Initialize dashboard notification badge
document.addEventListener('DOMContentLoaded', function() {
    updateDashboardNotificationBadge();
    // Update every 30 seconds
    setInterval(updateDashboardNotificationBadge, 30000);
});
</script>














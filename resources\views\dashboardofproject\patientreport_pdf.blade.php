<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>د ناروغانو راپور</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            direction: rtl;
            text-align: right;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        th {
            background-color: #f2f2f2;
        }
        .page-break {
            page-break-before: always;
        }
        h2 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>د روغتیایي مرکز د ناروغانو راپور</h1>
        <p>د راپور نېټه: {{ now()->format('Y-m-d') }}</p>
        @if($year)
            <p>کال: {{ $year }}</p>
        @endif
        @if(isset($month) && $month)
            <p>میاشت: {{ ['جنوري', 'فبروري', 'مارچ', 'اپریل', 'می', 'جون', 'جولای', 'اګست', 'سپتمبر', 'اکتوبر', 'نومبر', 'دسمبر'][$month-1] }}</p>
        @endif
    </div>

    <!-- Monthly Report -->
    <h2>میاشتنی راپور</h2>
    <table>
        <thead>
            <tr>
                <th>میاشت</th>
                <th>نارینه</th>
                <th>ښځینه</th>
                <th>ټول</th>
            </tr>
        </thead>
        <tbody>
            @php
                $months = ['جنوري', 'فبروري', 'مارچ', 'اپریل', 'می', 'جون', 'جولای', 'اګست', 'سپتمبر', 'اکتوبر', 'نومبر', 'دسمبر'];
            @endphp
            
            @foreach($monthlyStats as $month => $stats)
                <tr>
                    <td>{{ $months[$month-1] }}</td>
                    <td>{{ $stats['male'] }}</td>
                    <td>{{ $stats['female'] }}</td>
                    <td>{{ $stats['total'] }}</td>
                </tr>
            @endforeach
            
            <tr>
                <td><strong>ټول کال</strong></td>
                <td><strong>{{ $yearlyStats['male'] }}</strong></td>
                <td><strong>{{ $yearlyStats['female'] }}</strong></td>
                <td><strong>{{ $yearlyStats['total'] }}</strong></td>
            </tr>
        </tbody>
    </table>

    <!-- Patient List -->
    <div class="page-break"></div>
    <h2>د ناروغانو لیست</h2>
    <table>
        <thead>
            <tr>
                <th>شمېره</th>
                <th>نوم</th>
                <th>عمر</th>
                <th>جنسیت</th>
                <th>اړیکه</th>
                <th>ولایت</th>
                <th>ولسوالي</th>
                <th>کلی</th>
            </tr>
        </thead>
        <tbody>
            @if(count($patients) > 0)
                @foreach($patients as $p)
                    <tr>
                        <td>{{ $loop->iteration }}</td>
                        <td>{{ $p->Patiet_Name }}</td>
                        <td>{{ $p->Patient_Age }}</td>
                        <td>{{ $p->Patient_Gender }}</td>
                        <td>{{ $p->Patient_phone ?? 'نشته' }}</td>
                        <td>{{ $p->address->P_Province ?? 'نشته' }}</td>
                        <td>{{ $p->address->P_Distract ?? 'نشته' }}</td>
                        <td>{{ $p->address->P_Village ?? 'نشته' }}</td>
                    </tr>
                @endforeach
            @else
                <tr>
                    <td colspan="8">هیڅ ناروغ شتون نه لري</td>
                </tr>
            @endif
        </tbody>
    </table>
</body>
</html>


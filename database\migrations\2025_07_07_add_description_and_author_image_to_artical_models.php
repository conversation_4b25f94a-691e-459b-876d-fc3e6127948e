<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('artical_models', function (Blueprint $table) {
            $table->text('A_Description')->nullable()->after('A_Image');
            $table->string('A_Author_Image')->nullable()->after('A_Description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('artical_models', function (Blueprint $table) {
            $table->dropColumn(['A_Description', 'A_Author_Image']);
        });
    }
};

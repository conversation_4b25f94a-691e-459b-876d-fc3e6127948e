<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('patient_models', function (Blueprint $table) {
            $table->bigIncrements('Patient_id');
          
            $table->string('Patiet_Name');
            $table->integer('Patient_Age')->nullable();
            $table->string('Patient_Gender');
            $table->string('Patient_phone')->nullable();
            $table->string('Patient_email')->nullable(); // ایمیل ستون اختیاري کول
            $table->unsignedBigInteger('Dr_id');
            $table->foreign('Dr_Id')->references('Dr_Id')->on('doctor_models')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('patient_models');
    }
};





<?php

namespace App\Http\Controllers;

use App\Models\QuestionerModel;
use App\Models\PatientModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class QuestionerController extends Controller
{
    /**
     * Display a listing of the questions.
     */
    public function index()
    {
        $questions = QuestionerModel::orderBy('Question_No', 'asc')->get();
        return view('questions.index', compact('questions'));
    }

    /**
     * Show the form for creating a new question.
     */
    public function create()
    {
        $patients = PatientModel::all();
        return view('questions.create', compact('patients'));
    }

    /**
     * Store a newly created question in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'Q_Discription' => 'required|string',
            'Question_No' => 'required|integer',
            'A' => 'nullable|string',
            'B' => 'nullable|string',
            'C' => 'nullable|string',
            'D' => 'nullable|string',
            'Patient_Id' => 'required|exists:patient_models,Patient_id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        QuestionerModel::create($request->all());

        return redirect()->route('questions.index')
            ->with('success', 'سوال په بریالیتوب سره اضافه شو!');
    }

    /**
     * Display the specified question.
     */
    public function show($id)
    {
        $question = QuestionerModel::findOrFail($id);
        return view('questions.show', compact('question'));
    }

    /**
     * Show the form for editing the specified question.
     */
    public function edit($id)
    {
        $question = QuestionerModel::findOrFail($id);
        $patients = PatientModel::all();
        return view('questions.edit', compact('question', 'patients'));
    }

    /**
     * Update the specified question in storage.
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'Q_Discription' => 'required|string',
            'Question_No' => 'required|integer',
            'A' => 'nullable|string',
            'B' => 'nullable|string',
            'C' => 'nullable|string',
            'D' => 'nullable|string',
            'Patient_Id' => 'required|exists:patient_models,Patient_id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $question = QuestionerModel::findOrFail($id);
        $question->update($request->all());

        return redirect()->route('questions.index')
            ->with('success', 'سوال په بریالیتوب سره تازه شو!');
    }

    /**
     * Remove the specified question from storage.
     */
    public function destroy($id)
    {
        $question = QuestionerModel::findOrFail($id);
        $question->delete();

        return redirect()->route('questions.index')
            ->with('success', 'سوال په بریالیتوب سره حذف شو!');
    }

    /**
     * Display questions for a specific patient.
     */
    public function patientQuestions($patientId)
    {
        $patient = PatientModel::findOrFail($patientId);
        $questions = QuestionerModel::where('Patient_Id', $patientId)
            ->orderBy('Question_No', 'asc')
            ->get();
            
        return view('questions.patient', compact('questions', 'patient'));
    }

    /**
     * Show form to answer questions for a patient.
     */
    public function answerForm($patientId)
    {
        $patient = PatientModel::findOrFail($patientId);
        $questions = QuestionerModel::where('Patient_Id', $patientId)
            ->orderBy('Question_No', 'asc')
            ->get();
            
        return view('questions.answer', compact('questions', 'patient'));
    }

    /**
     * Save answers for patient questions.
     */
    public function saveAnswers(Request $request, $patientId)
    {
        $patient = PatientModel::findOrFail($patientId);
        
        foreach ($request->all() as $key => $value) {
            if (strpos($key, 'answer_') === 0) {
                $questionId = substr($key, 7); // Extract question ID from input name
                
                $question = QuestionerModel::findOrFail($questionId);
                $question->Selected_Option = $value; // Assuming you have this column
                $question->save();
            }
        }
        
        return redirect()->route('patients.show', $patientId)
            ->with('success', 'د ناروغ جوابونه په بریالیتوب سره ذخیره شول!');
    }
}
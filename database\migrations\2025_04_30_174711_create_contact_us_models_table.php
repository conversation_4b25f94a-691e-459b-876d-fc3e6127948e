<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_us_models', function (Blueprint $table) {
            $table->bigIncrements('contact_id');
            $table->string('fname'); // First name
            $table->string('lname'); // Last name
            $table->string('email'); // Email address
            $table->string('phone'); // Phone number
            $table->text('message'); // Message content
            $table->unsignedBigInteger('User_id')->nullable(); // Make nullable for guest users

            $table->foreign('User_id')->references('id')->on('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_us_models');
    }
};


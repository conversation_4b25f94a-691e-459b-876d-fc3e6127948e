<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ config('app.name', 'Laravel') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Styles -->
        @livewireStyles

        <!-- Custom Styles -->
        <link href="{{ asset('css/custom.css') }}" rel="stylesheet">
    </head>
    <body class="font-sans antialiased">
        <x-banner />

        <!-- Add this right after the opening body tag or in your main content area -->
        @if(Session::has('notification'))
            <div id="notification" class="fixed top-4 right-4 z-50 max-w-md bg-white rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 ease-in-out">
                <div class="px-4 py-2 {{ Session::get('notification.type') === 'success' ? 'bg-green-500' : 'bg-red-500' }} text-white flex justify-between items-center">
                    <h3 class="font-bold">
                        {{ Session::get('notification.type') === 'success' ? 'بریالیتوب!' : 'تېروتنه!' }}
                    </h3>
                    <button onclick="closeNotification()" class="text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-4 bg-white">
                    <p class="text-gray-800">{{ Session::get('notification.message') }}</p>
                    @if(Session::get('notification.patient_name'))
                        <p class="mt-2 text-gray-600">
                            <strong>ناروغ:</strong> {{ Session::get('notification.patient_name') }}
                        </p>
                    @endif
                    @if(Session::get('notification.patient_id'))
                        <p class="text-gray-600">
                            <strong>د ناروغ شمېره:</strong> {{ Session::get('notification.patient_id') }}
                        </p>
                    @endif
                </div>
                <div class="px-4 py-2 bg-gray-100 text-right">
                    <a href="{{ route('patient.index') }}" class="text-blue-500 hover:text-blue-700 text-sm">
                        ټول ناروغان وګورئ
                    </a>
                </div>
            </div>

            <script>
                // Auto-hide notification after 5 seconds
                setTimeout(function() {
                    const notification = document.getElementById('notification');
                    if (notification) {
                        notification.classList.add('opacity-0');
                        setTimeout(function() {
                            notification.style.display = 'none';
                        }, 300);
                    }
                }, 5000);

                function closeNotification() {
                    const notification = document.getElementById('notification');
                    notification.classList.add('opacity-0');
                    setTimeout(function() {
                        notification.style.display = 'none';
                    }, 300);
                }
            </script>
        @endif

        <div class="min-h-screen bg-gray-100">
            @livewire('navigation-menu')

            <!-- Page Heading -->
            @if (isset($header))
                <header class="bg-white shadow">
                    <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {{ $header }}
                    </div>
                </header>
            @endif


            <!-- Page Content -->
            <main>
                {{ $slot }}
            </main>
        </div>

        @stack('modals')

        @livewireScripts
    </body>
</html>






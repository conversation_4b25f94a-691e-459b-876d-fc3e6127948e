@extends('layouts/Admin')
@section('title','Patient Details')
@section('contents')
<div class="container-fluid px-4 py-6">
    <!-- Header -->
    <div class="bg-white rounded-2xl shadow-xl mb-8 border-0 overflow-hidden">
        <div class="bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl md:text-3xl font-bold text-white flex items-center">
                    <i class="fas fa-user-circle mr-3"></i>
                    {{ translateText('د ناروغ تفصیلات') }}
                </h1>
                <a href="{{ route('notifications.index') }}" class="bg-white text-green-600 px-4 py-2 rounded-lg hover:bg-gray-100 transition-colors font-medium">
                    <i class="fas fa-arrow-left mr-2"></i>
                    {{ translateText('بیرته خبرتیاوو ته') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Patient Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Basic Patient Info -->
        <div class="bg-white rounded-xl shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-user mr-3 text-blue-600"></i>
                {{ translateText('د ناروغ بنسټیز معلومات') }}
            </h2>
            
            <div class="space-y-4">
                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                    <span class="font-medium text-gray-600">{{ translateText('د ناروغ شمېره') }}:</span>
                    <span class="text-gray-900 font-semibold">{{ $patient->Patient_id }}</span>
                </div>
                
                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                    <span class="font-medium text-gray-600">{{ translateText('نوم') }}:</span>
                    <span class="text-gray-900 font-semibold">{{ $patient->Patiet_Name }}</span>
                </div>
                
                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                    <span class="font-medium text-gray-600">{{ translateText('عمر') }}:</span>
                    <span class="text-gray-900 font-semibold">{{ $patient->Patient_Age }} {{ translateText('کلنۍ') }}</span>
                </div>
                
                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                    <span class="font-medium text-gray-600">{{ translateText('جنس') }}:</span>
                    <span class="text-gray-900 font-semibold">{{ $patient->Patient_Gender }}</span>
                </div>
                
                @if($patient->Patient_phone)
                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                    <span class="font-medium text-gray-600">{{ translateText('د تلیفون شمېره') }}:</span>
                    <span class="text-gray-900 font-semibold">{{ $patient->Patient_phone }}</span>
                </div>
                @endif
                
                @if($patient->Patient_email)
                <div class="flex justify-between items-center py-3 border-b border-gray-100">
                    <span class="font-medium text-gray-600">{{ translateText('بریښنالیک') }}:</span>
                    <span class="text-gray-900 font-semibold">{{ $patient->Patient_email }}</span>
                </div>
                @endif
                
                <div class="flex justify-between items-center py-3">
                    <span class="font-medium text-gray-600">{{ translateText('د ثبت نېټه') }}:</span>
                    <span class="text-gray-900 font-semibold">{{ $patient->created_at->format('Y-m-d H:i') }}</span>
                </div>
            </div>
        </div>

        <!-- Notification Info -->
        <div class="bg-white rounded-xl shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-bell mr-3 text-green-600"></i>
                {{ translateText('د خبرتیا معلومات') }}
            </h2>
            
            <div class="space-y-4">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h3 class="font-semibold text-green-800 mb-2">{{ $notification->title }}</h3>
                    <p class="text-green-700">{{ $notification->message }}</p>
                    <p class="text-sm text-green-600 mt-2">{{ $notification->time_ago }}</p>
                </div>
                
                @if($notification->questionnaire_data)
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 class="font-semibold text-blue-800 mb-3">{{ translateText('د پوښتنلیک معلومات') }}</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        @if(isset($notification->questionnaire_data['total_questions']))
                        <div>
                            <span class="text-blue-600 font-medium">{{ translateText('ټولې پوښتنې') }}:</span>
                            <span class="text-blue-800">{{ $notification->questionnaire_data['total_questions'] }}</span>
                        </div>
                        @endif
                        
                        @if(isset($notification->questionnaire_data['total_score']))
                        <div>
                            <span class="text-blue-600 font-medium">{{ translateText('ټولې نمرې') }}:</span>
                            <span class="text-blue-800">{{ $notification->questionnaire_data['total_score'] }}</span>
                        </div>
                        @endif
                        
                        @if(isset($notification->questionnaire_data['percentage']))
                        <div>
                            <span class="text-blue-600 font-medium">{{ translateText('سلنه') }}:</span>
                            <span class="text-blue-800">{{ $notification->questionnaire_data['percentage'] }}%</span>
                        </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Questionnaire Details -->
    @if(($notification->questionnaire_data && isset($notification->questionnaire_data['questions_data']) && count($notification->questionnaire_data['questions_data']) > 0) ||
        ($questionnaireData && isset($questionnaireData['questions_data']) && count($questionnaireData['questions_data']) > 0))
    <div class="bg-white rounded-xl shadow-lg p-6 mt-8">
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
            <i class="fas fa-clipboard-list mr-3 text-purple-600"></i>
            {{ translateText('د پوښتنلیک ځوابونه') }}
        </h2>
        
        <div class="space-y-6">
            @php
                $questionsData = $questionnaireData['questions_data'] ?? $notification->questionnaire_data['questions_data'] ?? [];
            @endphp
            @foreach($questionsData as $index => $question)
            <div class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
                <!-- Question Header -->
                <div class="flex items-start mb-4">
                    <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">
                        {{ $question['question_number'] ?? ($index + 1) }}
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-semibold text-gray-800 mb-2">
                            {{ $question['question'] }}
                        </h4>
                    </div>
                </div>

                <!-- Enhanced Options in Column Format -->
                <div class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-200">
                    @php
                        // Use the enhanced options structure from controller
                        $options = $question['options'] ?? [
                            'A' => $question['option_a'] ?? 'هیڅ نه',
                            'B' => $question['option_b'] ?? 'لږه اندازه',
                            'C' => $question['option_c'] ?? 'لږه ډېره اندازه',
                            'D' => $question['option_d'] ?? 'ډېره اندازه'
                        ];
                        $selectedAnswer = $question['selected_answer'] ?? '';
                        $score = $question['score'] ?? 0;
                        $scoreColor = $question['score_color'] ?? 'green';
                        $scoreLevel = $question['score_level'] ?? 'عادي';
                        $maxScore = $question['max_score'] ?? 3;
                        $scorePercentage = $maxScore > 0 ? round(($score / $maxScore) * 100, 1) : 0;
                    @endphp

                    <h5 class="text-lg font-semibold text-gray-800 mb-6 flex items-center">
                        <i class="fas fa-list-ul mr-3 text-blue-600"></i>
                        {{ translateText('د ځواب اختیارونه') }}
                    </h5>

                    <!-- Options in Professional Column Format -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        @foreach($options as $optionKey => $optionText)
                            @php
                                $isSelected = $selectedAnswer === $optionKey;
                                $optionScore = match($optionKey) {
                                    'A' => 0,
                                    'B' => 1,
                                    'C' => 2,
                                    'D' => 3,
                                    default => 0
                                };
                                $optionColor = match($optionScore) {
                                    0 => 'green',
                                    1 => 'yellow',
                                    2 => 'orange',
                                    3 => 'red',
                                    default => 'gray'
                                };
                            @endphp

                            <div class="option-card relative p-4 rounded-xl border-2 transition-all duration-300 hover:shadow-md
                                {{ $isSelected ? 'bg-'.$optionColor.'-50 border-'.$optionColor.'-400 shadow-lg transform scale-105' : 'bg-white border-gray-200 hover:border-gray-300' }}">

                                <!-- Option Header -->
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 rounded-full border-2 flex items-center justify-center text-lg font-bold transition-all
                                            {{ $isSelected ? 'bg-'.$optionColor.'-500 text-white border-'.$optionColor.'-500 shadow-md' : 'border-gray-300 text-gray-500 bg-gray-50' }}">
                                            {{ $optionKey }}
                                        </div>
                                        <div class="flex-1">
                                            <span class="text-base font-medium {{ $isSelected ? 'text-'.$optionColor.'-800' : 'text-gray-700' }}">
                                                {{ $optionText }}
                                            </span>
                                        </div>
                                    </div>

                                    @if($isSelected)
                                        <div class="flex items-center space-x-2">
                                            <i class="fas fa-check-circle text-{{ $optionColor }}-500 text-xl"></i>
                                            <span class="text-xs bg-{{ $optionColor }}-500 text-white px-2 py-1 rounded-full font-medium">
                                                {{ translateText('انتخاب شوی') }}
                                            </span>
                                        </div>
                                    @endif
                                </div>

                                <!-- Option Score Info -->
                                <div class="flex items-center justify-between text-sm">
                                    <span class="text-gray-600">{{ translateText('نمرې') }}:</span>
                                    <div class="flex items-center space-x-2">
                                        <span class="font-bold text-{{ $optionColor }}-600">{{ $optionScore }}/3</span>
                                        <div class="w-12 bg-gray-200 rounded-full h-2">
                                            <div class="bg-{{ $optionColor }}-500 h-2 rounded-full" style="width: {{ ($optionScore/3)*100 }}%"></div>
                                        </div>
                                    </div>
                                </div>

                                @if($isSelected)
                                    <!-- Selected Indicator -->
                                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-{{ $optionColor }}-500 text-white rounded-full flex items-center justify-center">
                                        <i class="fas fa-star text-xs"></i>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>

                    <!-- Enhanced Selected Answer Summary with Circular Display -->
                    <div class="bg-white rounded-xl p-6 border-l-4 border-{{ $scoreColor }}-500 shadow-lg">
                        <h5 class="text-lg font-bold text-{{ $scoreColor }}-800 mb-4 flex items-center">
                            <i class="fas fa-award mr-3 text-{{ $scoreColor }}-600"></i>
                            {{ translateText('ټاکل شوی ځواب') }}
                        </h5>

                        <div class="flex items-center justify-between">
                            <!-- Answer Details -->
                            <div class="flex-1">
                                <!-- Large Circular Answer Display -->
                                <div class="flex items-center space-x-6 mb-4">
                                    <div class="relative">
                                        <!-- Circular Background -->
                                        <div class="w-20 h-20 bg-{{ $scoreColor }}-500 text-white rounded-full flex items-center justify-center text-3xl font-bold shadow-xl border-4 border-{{ $scoreColor }}-300">
                                            {{ $selectedAnswer }}
                                        </div>
                                        <!-- Score Badge -->
                                        <div class="absolute -bottom-2 -right-2 w-8 h-8 bg-white border-2 border-{{ $scoreColor }}-500 rounded-full flex items-center justify-center">
                                            <span class="text-xs font-bold text-{{ $scoreColor }}-600">{{ $score }}</span>
                                        </div>
                                    </div>

                                    <div class="flex-1">
                                        <h6 class="text-xl font-bold text-{{ $scoreColor }}-800 mb-2">
                                            {{ $question['selected_text'] ?? $options[$selectedAnswer] ?? 'نامعلوم' }}
                                        </h6>
                                        <p class="text-{{ $scoreColor }}-600 font-medium mb-1">
                                            {{ translateText('د ځواب کچه') }}: {{ $scoreLevel }}
                                        </p>
                                        <p class="text-gray-500 text-sm">
                                            {{ translateText('د پوښتنې نمبر') }}: {{ $question['question_number'] ?? 'N/A' }}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <!-- Circular Score Display -->
                            <div class="text-center">
                                <div class="relative w-24 h-24 mx-auto mb-3">
                                    <!-- Circular Progress -->
                                    <svg class="w-24 h-24 transform -rotate-90" viewBox="0 0 36 36">
                                        <path class="text-gray-200" stroke="currentColor" stroke-width="3" fill="none"
                                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                        <path class="text-{{ $scoreColor }}-500" stroke="currentColor" stroke-width="3" fill="none"
                                              stroke-linecap="round" stroke-dasharray="{{ $scorePercentage }}, 100"
                                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                    </svg>
                                    <!-- Score Text -->
                                    <div class="absolute inset-0 flex flex-col items-center justify-center">
                                        <span class="text-lg font-bold text-{{ $scoreColor }}-700">{{ $score }}</span>
                                        <span class="text-xs text-gray-500">/{{ $maxScore }}</span>
                                    </div>
                                </div>

                                <!-- Score Details -->
                                <div class="bg-{{ $scoreColor }}-50 rounded-lg p-3 border border-{{ $scoreColor }}-200">
                                    <div class="text-sm text-{{ $scoreColor }}-700 font-medium mb-1">{{ translateText('نمرې') }}</div>
                                    <div class="text-2xl font-bold text-{{ $scoreColor }}-800">{{ $scorePercentage }}%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach

            <!-- Enhanced Summary Statistics -->
            @php
                $summaryData = $questionnaireData ?? $notification->questionnaire_data ?? [];
                $questionsData = $questionnaireData['questions_data'] ?? $notification->questionnaire_data['questions_data'] ?? [];

                // Always recalculate from the actual questions data to ensure accuracy
                if (!empty($questionsData)) {
                    $totalQuestions = count($questionsData);
                    $totalScore = 0;

                    // Calculate total score from individual question scores
                    foreach ($questionsData as $question) {
                        $totalScore += $question['score'] ?? 0;
                    }

                    $maxScore = $totalQuestions * 3;
                    $percentage = $maxScore > 0 ? round(($totalScore / $maxScore) * 100, 2) : 0;

                    // Update summary data with calculated values
                    $summaryData = array_merge($summaryData, [
                        'total_questions' => $totalQuestions,
                        'total_score' => $totalScore,
                        'max_score' => $maxScore,
                        'percentage' => $percentage
                    ]);
                }

                $totalQuestions = $summaryData['total_questions'] ?? 0;
                $totalScore = $summaryData['total_score'] ?? 0;
                $maxScore = $summaryData['max_score'] ?? 0;
                $percentage = $summaryData['percentage'] ?? 0;

                // Determine overall assessment level
                $assessmentLevel = 'عادي';
                $assessmentColor = 'green';
                if ($percentage >= 75) {
                    $assessmentLevel = 'لوړ خطر';
                    $assessmentColor = 'red';
                } elseif ($percentage >= 50) {
                    $assessmentLevel = 'منځنی خطر';
                    $assessmentColor = 'orange';
                } elseif ($percentage >= 25) {
                    $assessmentLevel = 'لږ خطر';
                    $assessmentColor = 'yellow';
                }
            @endphp

            @if($totalQuestions > 0)
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 mt-6 shadow-lg">
                <h5 class="text-xl font-bold text-blue-800 mb-6 flex items-center">
                    <i class="fas fa-chart-bar mr-3 text-2xl"></i>
                    {{ translateText('د پوښتنلیک لنډیز') }}
                </h5>

                <!-- Statistics Grid -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
                    <div class="bg-white rounded-xl p-4 text-center shadow-sm border border-blue-100">
                        <div class="text-3xl font-bold text-blue-600 mb-2">{{ $totalQuestions }}</div>
                        <div class="text-sm text-blue-700 font-medium">{{ translateText('ټولې پوښتنې') }}</div>
                    </div>
                    <div class="bg-white rounded-xl p-4 text-center shadow-sm border border-green-100">
                        <div class="text-3xl font-bold text-green-600 mb-2">{{ $totalScore }}</div>
                        <div class="text-sm text-green-700 font-medium">{{ translateText('ترلاسه شوې نمرې') }}</div>
                    </div>
                    <div class="bg-white rounded-xl p-4 text-center shadow-sm border border-purple-100">
                        <div class="text-3xl font-bold text-purple-600 mb-2">{{ $maxScore }}</div>
                        <div class="text-sm text-purple-700 font-medium">{{ translateText('ټولې نمرې') }}</div>
                    </div>
                    <div class="bg-white rounded-xl p-4 text-center shadow-sm border border-{{ $assessmentColor }}-100">
                        <div class="text-3xl font-bold text-{{ $assessmentColor }}-600 mb-2">{{ $percentage }}%</div>
                        <div class="text-sm text-{{ $assessmentColor }}-700 font-medium">{{ translateText('سلنه') }}</div>
                    </div>
                </div>



                <!-- Overall Assessment -->
                <div class="bg-white rounded-xl p-6 border border-{{ $assessmentColor }}-200 shadow-sm">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-16 h-16 bg-{{ $assessmentColor }}-500 text-white rounded-full flex items-center justify-center">
                                <i class="fas fa-{{ $assessmentColor === 'green' ? 'check' : ($assessmentColor === 'red' ? 'exclamation-triangle' : 'info') }} text-2xl"></i>
                            </div>
                            <div>
                                <h6 class="text-lg font-bold text-{{ $assessmentColor }}-800">{{ translateText('ټولیز ارزونه') }}</h6>
                                <p class="text-{{ $assessmentColor }}-600 font-medium">{{ $assessmentLevel }}</p>
                                <p class="text-gray-600 text-sm mt-1">{{ translateText('د ټولو پوښتنو پر بنسټ') }}</p>
                            </div>
                        </div>

                        <!-- Progress Circle -->
                        <div class="relative w-20 h-20">
                            <svg class="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                                <path class="text-gray-200" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                                <path class="text-{{ $assessmentColor }}-500" stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round" stroke-dasharray="{{ $percentage }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                            </svg>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <span class="text-sm font-bold text-{{ $assessmentColor }}-600">{{ $percentage }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
    @elseif($notification->questionnaire_data)
    <!-- Fallback for notifications without detailed question data -->
    <div class="bg-white rounded-xl shadow-lg p-6 mt-8">
        <h2 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
            <i class="fas fa-info-circle mr-3 text-blue-600"></i>
            {{ translateText('د پوښتنلیک معلومات') }}
        </h2>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                @if(isset($notification->questionnaire_data['total_questions']))
                <div>
                    <div class="text-2xl font-bold text-blue-600">{{ $notification->questionnaire_data['total_questions'] }}</div>
                    <div class="text-sm text-blue-700">{{ translateText('ټولې پوښتنې') }}</div>
                </div>
                @endif

                @if(isset($notification->questionnaire_data['total_score']))
                <div>
                    <div class="text-2xl font-bold text-green-600">{{ $notification->questionnaire_data['total_score'] }}</div>
                    <div class="text-sm text-green-700">{{ translateText('ترلاسه شوې نمرې') }}</div>
                </div>
                @endif

                @if(isset($notification->questionnaire_data['max_score']))
                <div>
                    <div class="text-2xl font-bold text-purple-600">{{ $notification->questionnaire_data['max_score'] }}</div>
                    <div class="text-sm text-purple-700">{{ translateText('ټولې نمرې') }}</div>
                </div>
                @endif

                @if(isset($notification->questionnaire_data['percentage']))
                <div>
                    <div class="text-2xl font-bold text-orange-600">{{ $notification->questionnaire_data['percentage'] }}%</div>
                    <div class="text-sm text-orange-700">{{ translateText('سلنه') }}</div>
                </div>
                @endif
            </div>

            <div class="mt-4 text-center">
                <p class="text-blue-700">
                    {{ translateText('دا خبرتیا د زاړه سیسټم څخه ده. د تفصیلي پوښتنو د لیدو لپاره، نوي پوښتنلیک ډک کړئ.') }}
                </p>
            </div>
        </div>
    </div>
    @endif

    <!-- Action Buttons -->
    <div class="flex justify-center space-x-4 mt-8">
        <a href="{{ route('notifications.index') }}" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors font-medium">
            <i class="fas fa-arrow-left mr-2"></i>
            {{ translateText('بیرته خبرتیاوو ته') }}
        </a>
        
        <button onclick="window.print()" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors font-medium">
            <i class="fas fa-print mr-2"></i>
            {{ translateText('چاپ کول') }}
        </button>
    </div>
</div>
@endsection

@extends('layouts.Main')

@section('content')
<div class="container mt-5 mb-5">
    <div class="row">
        <div class="col-md-12">
            <h2 class="mb-4">{{ translateText('د پلټنې پایلې') }}: "{{ $query }}"</h2>
            
            @if($articles->count() + $news->count() + $doctors->count() + $patients->count() + $questions->count() + $patientLocations->count() + $doctorLocations->count() + $feedback->count() == 0)
                <div class="alert alert-info">
                    {{ translateText('هیڅ پایله ونه موندل شوه') }}
                </div>
            @else
                <div class="search-results">
                    <!-- Articles Results -->
                    @if($articles->count() > 0)
                        <div class="card mb-4">
                            <div class="card-header">{{ translateText('مقالې') }} ({{ $articles->count() }})</div>
                            <div class="card-body">
                                <ul class="list-group">
                                    @foreach($articles as $article)
                                        <li class="list-group-item">
                                            <h5>{{ $article->A_Title }}</h5>
                                            <p>{{ translateText('لیکوال') }}: {{ $article->A_Author }}</p>
                                              <p>{{ translateText('نوع') }}: {{ $article->A_Type }}</p>
                                            <p>{{ translateText('کټګوري') }}: {{ $article->A_catagory }}</p>
                                            <a href="{{ url('/article/' . $article->A_Id) }}" class="btn btn-sm btn-primary">{{ translateText('نور معلومات') }}</a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endif
                    
                    <!-- News Results -->
                    @if($news->count() > 0)
                        <div class="card mb-4">
                            <div class="card-header">{{ translateText('خبرونه') }} ({{ $news->count() }})</div>
                            <div class="card-body">
                                <ul class="list-group">
                                    @foreach($news as $item)
                                        <li class="list-group-item">
                                            <h5>{{ $item->News_Title }}</h5>
                                            <p>{{ Str::limit($item->News_Discription, 150) }}</p>
                                            <a href="{{ url('/news/' . $item->News_Id) }}" class="btn btn-sm btn-primary">{{ translateText('نور معلومات') }}</a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endif
                    
                    <!-- Doctors Results -->
                    @if($doctors->count() > 0)
                        <div class="card mb-4">
                            <div class="card-header">{{ translateText('ډاکټران') }} ({{ $doctors->count() }})</div>
                            <div class="card-body">
                                <ul class="list-group">
                                    @foreach($doctors as $doctor)
                                        <li class="list-group-item">
                                            <h5>{{ $doctor->Dr_Name }}</h5>
                                            <p>{{ translateText('د دفتر تلیفون') }}: {{ $doctor->Dr_office_phone }}</p>
                                            <a href="{{ url('/doctor/' . $doctor->Dr_Id) }}" class="btn btn-sm btn-primary">{{ translateText('نور معلومات') }}</a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endif
                    
                    <!-- Other sections... -->
                </div>
            @endif
            
            <a href="{{ url()->previous() }}" class="btn btn-secondary">{{ translateText('شاته') }}</a>
        </div>
    </div>
</div>
@endsection
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('doctor_models', function (Blueprint $table) {
            $table->bigIncrements('Dr_Id');
            $table->string('Dr_Name');
            $table->string('Dr_office_phone')->nullable()->unique();
            $table->string('Dr_Personal_phone')->nullable()->unique();
            $table->string('Dr_image')->nullable();
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')->references('user_id')->on('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('doctor_models');
    }
};


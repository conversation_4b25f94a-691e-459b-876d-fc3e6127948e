<?php

namespace App\Models;

use App\Models\QuestionerModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class QuestionModel extends Model
{
    use HasFactory;

    protected $table = 'questioner_models';
    protected $primaryKey = 'Q_Id';
    
    protected $fillable =   ['Q_Discription', 'Question_No', 'A', 'B', 'C', 'D'
    ];

    /**
     * Get the answers for this question.
     */
    public function answers()
    {
        return $this->hasMany(QuestionerModel::class, 'Question_Id', 'Question_Id');
    }
}
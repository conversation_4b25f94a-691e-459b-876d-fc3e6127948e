<?php

namespace App\Http\Controllers;

use App\Models\ArticalModel;
use Illuminate\Http\Request;

class PolicyController extends Controller
{
     public function Policy(){
        // Get policies with type 'پالیسي' or 'policy'
        $policy = ArticalModel::where('A_Type', 'پالیسي')
            ->orWhere('A_Type', 'policy')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('polices' ,compact('policy'));
     }

    /**
     * Search for policies.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function searchPolicy(Request $request)
    {
        $search = $request->input('search');

        // Search in policy title, author, category, and description
        $policy = ArticalModel::where(function($query) {
                $query->where('A_Type', 'پالیسي')
                      ->orWhere('A_Type', 'policy');
            })
            ->where(function($query) use ($search) {
                $query->where('A_Title', 'LIKE', "%{$search}%")
                      ->orWhere('A_Author', 'LIKE', "%{$search}%")
                      ->orWhere('A_catagory', 'LIKE', "%{$search}%")
                      ->orWhere('A_Description', 'LIKE', "%{$search}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Preserve search parameter in pagination links
        $policy->appends(['search' => $search]);

        return view('polices', compact('policy'));
    }

      public function edit($id)
{
    $policy = ArticalModel::findOrFail($id);
    return view('dashboardofproject.book-edit', compact('book'));
}

public function update(Request $request, $id)
{
    $policy = ArticalModel::findOrFail($id);

    $validated = $request->validate([
        'title' => 'required|string|max:255',
        'author' => 'required|string|max:255',
        'publishdate' => 'required|date',
        'language' => 'required|string|max:255',
        'type' => 'required|string|max:255',
        'catagory' => 'required|string|max:255',
        'description' => 'nullable|string',
        'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        'author_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        'file' => 'nullable|mimes:pdf,doc,docx|max:10240',
    ]);

    // Handle policy cover image upload if a new image is provided
    if ($request->hasFile('image')) {
        // Delete old image if it exists
        if ($policy->A_Image && file_exists(public_path('imagese/' . $policy->A_Image))) {
            unlink(public_path('imagese/' . $policy->A_Image));
        }

        $imageName = time() . '_image.' . $request->image->extension();
        $request->image->move(public_path('imagese'), $imageName);
        $policy->A_Image = $imageName;
    }

    // Handle author image upload if a new author image is provided
    if ($request->hasFile('author_image')) {
        // Delete old author image if it exists
        if ($policy->A_Author_Image && file_exists(public_path('imagese/' . $policy->A_Author_Image))) {
            unlink(public_path('imagese/' . $policy->A_Author_Image));
        }

        $authorImageName = time() . '_author.' . $request->author_image->extension();
        $request->author_image->move(public_path('imagese'), $authorImageName);
        $policy->A_Author_Image = $authorImageName;
    }

    // Handle file upload if a new file is provided
    if ($request->hasFile('file')) {
        // Delete old file if it exists
        if ($policy->A_File && file_exists(public_path('imagese/' . $policy->A_File))) {
            unlink(public_path('imagese/' . $policy->A_File));
        }

        $fileName = time() . '_file.' . $request->file->extension();
        $request->file->move(public_path('imagese'), $fileName);
        $policy->A_File = $fileName;
    }

    // Update policy properties
    $policy->A_Title = $request->title;
    $policy->A_Author = $request->author;
    $policy->A_Publication_date = $request->publishdate;
    $policy->A_Language = $request->language;
    $policy->A_Type = $request->type;
    $policy->A_catagory = $request->catagory;
    $policy->A_Description = $request->description;

    $policy->save();

    return redirect()->route('bookd.index')->with('success', 'کتاب په بریالیتوب سره تازه شو');
}

public function destroy($id)
{
    $policy = ArticalModel::findOrFail($id);

    // Delete associated files
    if ($policy->A_Image && file_exists(public_path($policy->A_Image))) {
        unlink(public_path($policy->A_Image));
    }

    if ($policy->A_File && file_exists(public_path($policy->A_File))) {
        unlink(public_path($policy->A_File));
    }

    $policy->delete();

    return redirect()->route('bookd.index')->with('success', 'کتاب په بریالیتوب سره لرې شو');
}

/**
 * Store a newly created policy in storage.
 *
 * @param  \Illuminate\Http\Request  $request
 * @return \Illuminate\Http\RedirectResponse
 */
public function store(Request $request)
{
    $request->validate([
        'title' => 'required|string|max:255',
        'author' => 'required|string|max:255',
        'publishdate' => 'required|date',
        'language' => 'required|string|max:255',
        'catagory' => 'required|string|max:255',
        'description' => 'nullable|string',
        'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        'author_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        'file' => 'nullable|mimes:pdf,doc,docx|max:10240',
    ]);

    try {
        $policy = new ArticalModel();
        $policy->A_Title = $request->title;
        $policy->A_Author = $request->author;
        $policy->A_Publication_date = $request->publishdate;
        $policy->A_Language = $request->language;
        $policy->A_Type = 'پالیسي'; // Always set as policy
        $policy->A_catagory = $request->catagory;
        $policy->A_Description = $request->description;
        $policy->user_id = 2024;

        // Handle policy cover image upload
        if ($request->hasFile('image')) {
            $imageName = time() . '_image.' . $request->image->extension();
            $request->image->move(public_path('imagese'), $imageName);
            $policy->A_Image = $imageName;
        } else {
            $policy->A_Image = 'default.jpg';
        }

        // Handle author image upload
        if ($request->hasFile('author_image')) {
            $authorImageName = time() . '_author.' . $request->author_image->extension();
            $request->author_image->move(public_path('imagese'), $authorImageName);
            $policy->A_Author_Image = $authorImageName;
        } else {
            $policy->A_Author_Image = 'author-default.jpg';
        }

        // Handle file upload
        if ($request->hasFile('file')) {
            $fileName = time() . '_file.' . $request->file->extension();
            $request->file->move(public_path('imagese'), $fileName);
            $policy->A_File = $fileName;
        } else {
            $policy->A_File = 'default.pdf';
        }

        $policy->save();

        return redirect()->route('bookd.index')->with('success', translateText('پالیسي په بریالیتوب سره ثبت شوه!'));
    } catch (\Exception $e) {
        return redirect()->back()->with('error', translateText('تېروتنه: ') . $e->getMessage())->withInput();
    }
}

}





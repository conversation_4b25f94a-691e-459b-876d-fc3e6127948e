<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('questioner_models') && !Schema::hasColumn('questioner_models', 'Selected_Option')) {
            Schema::table('questioner_models', function (Blueprint $table) {
                $table->string('Selected_Option')->nullable()->after('D');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('questioner_models') && Schema::hasColumn('questioner_models', 'Selected_Option')) {
            Schema::table('questioner_models', function (Blueprint $table) {
                $table->dropColumn('Selected_Option');
            });
        }
    }
};
<?php

namespace App\Http\Controllers;

use App\Models\FeedbackModel;
use App\Models\PatientModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;

class FeedbackController extends Controller
{
    /**
     * Display a listing of the feedbacks.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // د فلټر کولو لپاره د سرچ پارامیټر واخلئ
        $search = $request->input('search');
        $date = $request->input('date');
        
        // د فیډبیک کیوري جوړ کړئ
        $query = FeedbackModel::query();
        
        // که سرچ پارامیټر موجود وي، نو د ایمیل په اساس فلټر کړئ
        if ($search) {
            $query->where('Recever_email', 'like', '%' . $search . '%');
        }
        
        // که د نیټې پارامیټر موجود وي، نو د نیټې په اساس فلټر کړئ
        if ($date) {
            $query->whereDate('created_at', $date);
        }
        
        // د فیډبیک لیست واخلئ او پیجینیشن اضافه کړئ
        $feedbacks = $query->orderBy('created_at', 'desc')->paginate(10);
        
        // د پیجینیشن لینکونو کې د سرچ پارامیټر ساتل
        $feedbacks->appends($request->all());
        
        // د ناروغانو ایمیلونه د ډیټابیس څخه واخلئ
        try {
            $patients = DB::table('patient_models')
                ->whereNotNull('Patient_email')
                ->where('Patient_email', '!=', '')
                ->select('Patient_id', 'Patiet_Name', 'Patient_email')
                ->get();
        } catch (\Exception $e) {
            \Log::error('Error fetching patients: ' . $e->getMessage());
            $patients = [];
        }
        
        return view('dashboardofproject.Feedbackcopy', compact('feedbacks', 'patients'));
    }

    /**
     * Show the form for creating a new feedback.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $feedbacks = FeedbackModel::orderBy('created_at', 'desc')->get();
        $patients = PatientModel::whereNotNull('Patient_email')->get();
        
        return view('dashboardofproject.feedbackcopy', compact('feedbacks', 'patients'));
    }

    /**
     * Store a newly created feedback in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        try {
            // Validate the request
            $request->validate([
                'Contents' => 'required|string'
            ]);

            // Get email from either dropdown or custom input
            $email = $request->recipient_email;
            if (empty($email) && !empty($request->custom_email)) {
                $email = $request->custom_email;
                // Validate the custom email
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    return redirect()->route('feedback.index')->with('error', 'لطفۍ یو صحیح ایمیل ادرس ولیکئ.');
                }
            }

            // Check if email is provided
            if (empty($email)) {
                return redirect()->route('feedback.index')->with('error', 'لطفۍ د ترلاسه کوونکي ایمیل ادرس وټاکئ یا ولیکئ.');
            }

            // Create new feedback
            $feedback = new FeedbackModel();
            
            $feedback->Recever_email = $email;
            $feedback->Contents = $request->Contents;
            
            if (Auth::check()) {
                $feedback->User_Id = Auth::id();
            } else {
                $feedback->User_Id = $this->createDefaultUserIfNoneExists();
            }
            
            $feedback->save();

            // Try to send email
            try {
                $emailSent = $this->sendFeedbackEmail($feedback);

                if ($emailSent) {
                    \Log::info('Email sent successfully to: ' . $feedback->Recever_email);
                    return redirect()->route('feedback.index')->with('success', 'نظر په بریالیتوب سره ثبت شو او ایمیل ولیږل شو.');
                } else {
                    \Log::warning('Email failed to send to: ' . $feedback->Recever_email);
                    return redirect()->route('feedback.index')->with('warning', 'نظر ثبت شو مګر ایمیل ونه لیږل شو. د ایمیل تنظیمات وګورئ.');
                }
            } catch (\Exception $e) {
                \Log::error('د ایمیل لېږلو کې تېروتنه: ' . $e->getMessage());
                \Log::error('Email error details: ' . $e->getTraceAsString());
                return redirect()->route('feedback.index')->with('error', 'نظر ثبت شو مګر ایمیل ونه لیږل شو. تېروتنه: ' . $e->getMessage());
            }
        } catch (\Exception $e) {
            return redirect()->route('feedback.index')->with('error', 'د نظر په ثبتولو کې تېروتنه: ' . $e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View|\Illuminate\Http\RedirectResponse
     */
    public function edit($id)
    {
        try {
            $feedback = FeedbackModel::findOrFail($id);
            return view('dashboardofproject.feedback.edit', compact('feedback'));
        } catch (\Exception $e) {
            return redirect()->route('feedback.index')->with('error', 'Feedback not found: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified feedback in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        try {
            // Validate the request
            $request->validate([
                'recipient_email' => 'required|email',
                'Contents' => 'required|string'
            ]);

            // Find the feedback
            $feedback = FeedbackModel::findOrFail($id);
            
            // Update feedback
            $feedback->Recever_email = $request->recipient_email;
            $feedback->Contents = $request->Contents;
            
            // Update User_Id if empty
            if (empty($feedback->User_Id)) {
                if (Auth::check()) {
                    $feedback->User_Id = Auth::id();
                } else {
                    $user = DB::table('users')->first();
                    if ($user) {
                        $feedback->User_Id = $user->user_id;
                    } else {
                        throw new \Exception('هیڅ کارن موجود نه دی. لطفۍ لومړی یو کارن جوړ کړئ.');
                    }
                }
            }
            
            $feedback->save();

            // Try to send email but don't let it break the flow
            try {
                $this->sendFeedbackEmail($feedback);
            } catch (\Exception $e) {
                \Log::error('د ایمیل لېږلو کې تېروتنه: ' . $e->getMessage());
                // Continue execution even if email fails
            }

            return redirect()->route('feedback.index')->with('success', 'نظر په بریالیتوب سره تازه شو.');
        } catch (\Exception $e) {
            return redirect()->route('feedback.index')->with('error', 'د نظر په تازه کولو کې تېروتنه: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified feedback from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            $feedback = FeedbackModel::findOrFail($id);
            $feedback->delete();

            return redirect()->route('feedback.index')->with('success', 'فیډبیک په بریالیتوب سره حذف شو!');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'تیروتنه: ' . $e->getMessage());
        }
    }

    /**
     * Send feedback email.
     *
     * @param  \App\Models\FeedbackModel  $feedback
     * @return bool
     */
    private function sendFeedbackEmail(FeedbackModel $feedback)
    {
        try {
            \Log::info('Attempting to send email to: ' . $feedback->Recever_email);
            \Log::info('Email configuration - MAIL_MAILER: ' . config('mail.default'));
            \Log::info('Email configuration - MAIL_HOST: ' . config('mail.mailers.smtp.host'));

            // Use the email template instead of raw text
            Mail::send('emails.feedback', [
                'content' => $feedback->Contents,
                'created_at' => $feedback->created_at->format('Y-m-d H:i:s')
            ], function ($message) use ($feedback) {
                $message->to($feedback->Recever_email)
                        ->subject('نوی نظر له KU Mental Health څخه')
                        ->from(config('mail.from.address'), config('mail.from.name'));
            });

            \Log::info('Email sent successfully to: ' . $feedback->Recever_email);
            return true;
        } catch (\Exception $e) {
            \Log::error('د ایمیل لېږلو کې تېروتنه: ' . $e->getMessage());
            \Log::error('Email error trace: ' . $e->getTraceAsString());
            \Log::error('Email configuration check:');
            \Log::error('- MAIL_MAILER: ' . config('mail.default'));
            \Log::error('- MAIL_HOST: ' . config('mail.mailers.smtp.host'));
            \Log::error('- MAIL_PORT: ' . config('mail.mailers.smtp.port'));
            \Log::error('- MAIL_FROM_ADDRESS: ' . config('mail.from.address'));
            return false;
        }
    }

    /**
     * Create a default user if none exists.
     *
     * @return int
     */
    private function createDefaultUserIfNoneExists()
    {
        $user = DB::table('users')->first();
        
        if (!$user) {
            // Create a default user
            $userId = time(); // Using timestamp as user_id
            DB::table('users')->insert([
                'user_id' => $userId,
                'name' => 'Default User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'user',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
            
            return $userId;
        }
        
        return $user->user_id;
    }
}








































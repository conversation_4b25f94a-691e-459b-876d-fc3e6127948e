<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('doctor_models', function (Blueprint $table) {
            $table->string('Dr_specialty')->nullable()->after('Dr_image');
            $table->text('Dr_description')->nullable()->after('Dr_specialty');
            $table->string('Dr_facebook')->nullable()->after('Dr_description');
            $table->string('Dr_youtube')->nullable()->after('Dr_facebook');
            $table->string('Dr_telegram')->nullable()->after('Dr_youtube');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('doctor_models', function (Blueprint $table) {
            $table->dropColumn(['Dr_specialty', 'Dr_description', 'Dr_facebook', 'Dr_youtube', 'Dr_telegram']);
        });
    }
};

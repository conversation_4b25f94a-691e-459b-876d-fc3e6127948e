@extends('layouts/Main')

@section('title','doctor')

@section('contents')

<div id="doctor">
  <h1>{{ trans_static('زموږ متخصص ډاکتران') }}</h1>

  <form action="{{ route('doctor.search') }}" method="GET">
    <div id="search_sec">
      <button type="submit" id="serch_btn">
        <i class="fas fa-search"></i>
      </button>
      <input type="search" name="search" placeholder="{{ trans_static('پلټنه وکړئ....') }}" value="{{ request('search') }}">
    </div>
  </form>

<div class="card-container" id="cardContainer">
  @if($doctors->count() > 0)
    @foreach($doctors as $doctor)
      <!-- کارت {{ $loop->iteration }} -->
      <div class="card {{ $loop->iteration > 4 ? 'hidden-card' : '' }}" style="display: {{ $loop->iteration > 4 ? 'none' : 'flex' }};">
        <div>
          <h3>{{ $doctor->Dr_Name }}</h3>
          @if($doctor->Dr_description)
            <p>{{ $doctor->Dr_description }}</p>
          @else
            <p>{{ trans_static('د ذهني روغتیا متخصص ډاکټر چې د ناروغانو سره د کار تجربه لري.') }}</p>
          @endif
          <div class="icons">
            @if($doctor->Dr_facebook)
              <a href="{{ $doctor->Dr_facebook }}" target="_blank"><i class="fab fa-facebook"></i></a>
            @else
              <i class="fab fa-facebook"></i>
            @endif
            @if($doctor->Dr_youtube)
              <a href="{{ $doctor->Dr_youtube }}" target="_blank"><i class="fab fa-youtube"></i></a>
            @else
              <i class="fab fa-youtube"></i>
            @endif
            @if($doctor->Dr_telegram)
              <a href="{{ $doctor->Dr_telegram }}" target="_blank"><i class="fab fa-telegram"></i></a>
            @else
              <i class="fab fa-telegram"></i>
            @endif
          </div>
        </div>
        @if($doctor->Dr_image && file_exists(public_path($doctor->Dr_image)))
          <img src="{{ asset($doctor->Dr_image) }}" alt="{{ $doctor->Dr_Name }}">
        @else
          <img src="{{ asset('imagese/doctor.jpg') }}" alt="{{ trans_static('ډاکټر') }}">
        @endif
      </div>
    @endforeach

  @else
    <div class="no-results">
      <p>{{ trans_static('هیڅ ډاکټر ونه موندل شو') }}</p>
    </div>
  @endif
</div>

@if($doctors->count() > 4)
  <div class="show-more-container">
    <button id="showMoreDoctors" onclick="showMoreDoctors()">
      {{ trans_static('نور ډاکتران ښکاره کړئ') }}
    </button>
  </div>
@endif

<script>
function showMoreDoctors() {
    const hiddenCards = document.querySelectorAll('.hidden-card');
    const button = document.getElementById('showMoreDoctors');

    if (button.textContent.includes('ښکاره کړئ')) {
        // Show hidden cards
        hiddenCards.forEach(card => {
            card.style.display = 'flex';
        });
        button.textContent = '{{ trans_static("لږ ډاکتران ښکاره کړئ") }}';
    } else {
        // Hide cards again
        hiddenCards.forEach(card => {
            card.style.display = 'none';
        });
        button.textContent = '{{ trans_static("نور ډاکتران ښکاره کړئ") }}';
    }
}
</script>

  

  <div class="pagination-container">
    {{ $doctors->links() }}
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const showMoreBtn = document.getElementById('showMoreDoctors');
    const extraDoctors = document.querySelectorAll('.extra-doctor');
    
    if (showMoreBtn && extraDoctors.length > 0) {
      showMoreBtn.addEventListener('click', function() {
        extraDoctors.forEach(doctor => {
          doctor.style.display = doctor.style.display === 'none' ? 'block' : 'none';
        });
        
        showMoreBtn.textContent = showMoreBtn.textContent === '{{ trans_static("نور ډاکتران ښکاره کړئ") }}' ?
          '{{ trans_static("لږ ډاکتران ښکاره کړئ") }}' : '{{ trans_static("نور ډاکتران ښکاره کړئ") }}';
      });
    }
  });
</script>

@endsection



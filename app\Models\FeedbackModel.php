<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FeedbackModel extends Model
{
    use HasFactory;

    protected $table = 'feedback_models';
    protected $primaryKey = 'Feedback_Id';
    
    protected $fillable = [
        'User_Id',
        'Contents',
        'Recever_email'  // د ستون نوم سم کړئ - Recever_email نه recipient_email
    ];

    /**
     * Get the user that owns the feedback.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'User_Id', 'id');
    }
}



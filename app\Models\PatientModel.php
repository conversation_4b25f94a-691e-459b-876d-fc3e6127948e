<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class PatientModel extends Model
{
    use HasFactory;

    // Update the table name to match what's in your database
    protected $table = 'patient_models';
    protected $primaryKey = 'Patient_id';

    protected $fillable = [
        'Patiet_Name',
        'Patient_Age',
        'Patient_Gender',
        'Patient_phone',
        'Patient_email',
        'Dr_id',
    ];

    protected $dates = [
        'created_at',
        'updated_at'
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        // Add any attributes you want to append here
        // 'full_name',
    ];

    /**
     * Get the address associated with the patient.
     */
    public function address()
    {
        return $this->hasOne(PatientAddModel::class, 'Patient_Id', 'Patient_id');
    }

    /**
     * Get the doctor associated with the patient.
     */
    public function doctor()
    {
        return $this->belongsTo(DoctorModel::class, 'Dr_id', 'Dr_id');
    }

    /**
     * Get the questions associated with the patient.
     */
    public function questions()
    {
        return $this->hasMany(QuestionerModel::class, 'Patient_Id', 'Patient_id');
    }

    /**
     * Get the answers associated with the patient.
     */
    public function answers()
    {
        return $this->belongsTo(DoctorModel::class, 'Dr_id', 'Dr_Id');
    }

    /**
     * Get the full name attribute.
     * This is an example of an accessor that could be appended.
     */
    // public function getFullNameAttribute()
    // {
    //     return $this->Patiet_Name . ' (' . $this->Patient_Age . ' کلن)';
    // }
}






















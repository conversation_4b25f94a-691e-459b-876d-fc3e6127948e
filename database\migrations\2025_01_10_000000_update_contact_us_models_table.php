<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contact_us_models', function (Blueprint $table) {
            // Drop old columns if they exist
            if (Schema::hasColumn('contact_us_models', 'contact_name')) {
                $table->dropColumn('contact_name');
            }
            if (Schema::hasColumn('contact_us_models', 'comment')) {
                $table->dropColumn('comment');
            }
            
            // Add new columns if they don't exist
            if (!Schema::hasColumn('contact_us_models', 'fname')) {
                $table->string('fname')->after('contact_id');
            }
            if (!Schema::hasColumn('contact_us_models', 'lname')) {
                $table->string('lname')->after('fname');
            }
            if (!Schema::hasColumn('contact_us_models', 'email')) {
                $table->string('email')->after('lname');
            }
            if (!Schema::hasColumn('contact_us_models', 'message')) {
                $table->text('message')->after('phone');
            }
            
            // Make User_id nullable if it isn't already
            $table->unsignedBigInteger('User_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contact_us_models', function (Blueprint $table) {
            // Restore old structure
            if (!Schema::hasColumn('contact_us_models', 'contact_name')) {
                $table->string('contact_name')->after('contact_id');
            }
            if (!Schema::hasColumn('contact_us_models', 'comment')) {
                $table->text('comment')->nullable()->after('phone');
            }
            
            // Drop new columns
            if (Schema::hasColumn('contact_us_models', 'fname')) {
                $table->dropColumn('fname');
            }
            if (Schema::hasColumn('contact_us_models', 'lname')) {
                $table->dropColumn('lname');
            }
            if (Schema::hasColumn('contact_us_models', 'email')) {
                $table->dropColumn('email');
            }
            if (Schema::hasColumn('contact_us_models', 'message')) {
                $table->dropColumn('message');
            }
        });
    }
};

/**
 * Session timeout handler
 */
(function() {
    // Set timeout to check session status every 5 minutes
    const SESSION_CHECK_INTERVAL = 5 * 60 * 1000; // 5 minutes

    function checkSessionStatus() {
        fetch('/session-status', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (response.status === 401 || response.status === 419) {
                // Session expired, show message and refresh
                alert('ستاسو غونډه پای ته رسېدلې ده. مهرباني وکړئ بیا ننوتل وکړئ.');
                window.location.reload();
            }
        })
        .catch(error => {
            console.error('Error checking session status:', error);
        });
    }

    // Set interval to check session status
    setInterval(checkSessionStatus, SESSION_CHECK_INTERVAL);
})();

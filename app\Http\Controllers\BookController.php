<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ArticalModel;
use Illuminate\View\View;
use Illuminate\Http\Response;
use Illuminate\Http\RedirectResponse;
use Illuminate\Contracts\View\View as ViewContract;
use Illuminate\Support\Facades\DB;

class BookController extends Controller
{
    /**
     * Display a listing of the books.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        try {
            // Get all books, papers, and policies
            $books = ArticalModel::where('A_Type', 'کتاب')->get();
            $papers = ArticalModel::where('A_Type', 'مقالي')->get();
            $policy = ArticalModel::where('A_Type', 'پالیسي')->get();

            // Get unique years for filter dropdown
            $years = ArticalModel::selectRaw('YEAR(A_Publication_date) as year')
                ->distinct()
                ->orderBy('year', 'desc')
                ->pluck('year')
                ->filter();

            // Get unique categories for filter dropdown
            $categories = ArticalModel::select('A_catagory')
                ->distinct()
                ->pluck('A_catagory')
                ->filter();

            // Get unique languages for filter dropdown
            $languages = ArticalModel::select('A_Language')
                ->distinct()
                ->pluck('A_Language')
                ->filter();

            return view('dashboardofproject.bookcopy', compact('books', 'papers', 'policy', 'years', 'categories', 'languages'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', translateText('تېروتنه: ') . $e->getMessage());
        }
    }

    /**
     * Filter books based on criteria.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Contracts\View\View
     */
    public function filter(Request $request)
    {
        try {
            // Start with a base query
            $booksQuery = ArticalModel::query()->where('A_Type', 'کتاب');
            $papersQuery = ArticalModel::query()->where('A_Type', 'مقالي');
            $policyQuery = ArticalModel::query()->where('A_Type', 'پالیسي');

            // Apply filters
            if ($request->filled('language')) {
                $booksQuery->where('A_Language', $request->language);
                $papersQuery->where('A_Language', $request->language);
                $policyQuery->where('A_Language', $request->language);
            }

            if ($request->filled('year')) {
                $booksQuery->whereYear('A_Publication_date', $request->year);
                $papersQuery->whereYear('A_Publication_date', $request->year);
                $policyQuery->whereYear('A_Publication_date', $request->year);
            }

            if ($request->filled('category')) {
                $booksQuery->where('A_catagory', $request->category);
                $papersQuery->where('A_catagory', $request->category);
                $policyQuery->where('A_catagory', $request->category);
            }

            // Get the filtered results
            $books = $booksQuery->get();
            $papers = $papersQuery->get();
            $policy = $policyQuery->get();

            // Get unique years, categories, and languages for filter dropdowns
            $years = ArticalModel::selectRaw('YEAR(A_Publication_date) as year')
                ->distinct()
                ->orderBy('year', 'desc')
                ->pluck('year')
                ->filter();

            $categories = ArticalModel::select('A_catagory')
                ->distinct()
                ->pluck('A_catagory')
                ->filter();

            $languages = ArticalModel::select('A_Language')
                ->distinct()
                ->pluck('A_Language')
                ->filter();

            // Flash a success message
            session()->flash('success', translateText('فلټر په بریالیتوب سره تطبیق شو!'));

            return view('dashboardofproject.bookcopy', compact('books', 'papers', 'policy', 'years', 'categories', 'languages'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', translateText('تېروتنه: ') . $e->getMessage());
        }
    }

    /**
     * Store a newly created book in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'author' => 'required|string|max:255',
            'publishdate' => 'required|date',
            'language' => 'required|string|max:255',
            'type' => 'required|string|in:کتاب,مقالي,پالیسي',
            'catagory' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'author_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'file' => 'nullable|mimes:pdf,doc,docx|max:100000',
        ]);

        try {
            $book = new ArticalModel();
            $book->A_Title = $request->title;
            $book->A_Author = $request->author;
            $book->A_Publication_date = $request->publishdate;
            $book->A_Language = $request->language;
            $book->A_Type = $request->type;
            $book->A_catagory = $request->catagory;
            $book->A_Description = $request->description;
            $book->user_id = 2024;

            // Handle book cover image upload
            if ($request->hasFile('image')) {
                $imageName = time() . '_image.' . $request->image->extension();
                $request->image->move(public_path('imagese'), $imageName);
                $book->A_Image = $imageName;
            } else {
                // Set a default image if none provided
                $book->A_Image = 'default.jpg';
            }

            // Handle author image upload
            if ($request->hasFile('author_image')) {
                $authorImageName = time() . '_author.' . $request->author_image->extension();
                $request->author_image->move(public_path('imagese'), $authorImageName);
                $book->A_Author_Image = $authorImageName;
            } else {
                // Set a default author image if none provided
                $book->A_Author_Image = 'author-default.jpg';
            }

            // Handle file upload
            if ($request->hasFile('file')) {
                $fileName = time() . '_file.' . $request->file->extension();
                $request->file->move(public_path('imagese'), $fileName);
                $book->A_File = $fileName;
            } else {
                // Set a default file if none provided
                $book->A_File = 'default.pdf';
            }

            $book->save();

            $successMessage = '';
            if ($request->type === 'کتاب') {
                $successMessage = translateText('کتاب په بریالیتوب سره ثبت شو او د ویب سایت په کتابونو صفحه کې به ښکاره شي!');
            } elseif ($request->type === 'مقالي') {
                $successMessage = translateText('مقاله په بریالیتوب سره ثبت شوه!');
            } elseif ($request->type === 'پالیسي') {
                $successMessage = translateText('پالیسي په بریالیتوب سره ثبت شوه!');
            }

            return redirect()->route('bookd.index')->with('success', $successMessage);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', translateText('تېروتنه: ') . $e->getMessage())->withInput();
        }
    }

    /**
     * Display a listing of books for the public view.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function Book()
    {
        try {
            // Get all books with type 'کتاب'
            $books = ArticalModel::where('A_Type', 'کتاب')
                ->latest()
                ->paginate(10);

            return view('bookpage', compact('books'));
        } catch (\Exception $e) {
            return redirect()->back()->with('error', translateText('تېروتنه: ') . $e->getMessage());
        }
    }

    /**
     * Search for books/ketabs.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function searchKetabs(Request $request)
    {
        $search = $request->input('search');

        // Search in book title, author, category, and description
        $books = ArticalModel::where('A_Type', 'کتاب')
            ->where(function($query) use ($search) {
                $query->where('A_Title', 'LIKE', "%{$search}%")
                      ->orWhere('A_Author', 'LIKE', "%{$search}%")
                      ->orWhere('A_catagory', 'LIKE', "%{$search}%")
                      ->orWhere('A_Description', 'LIKE', "%{$search}%");
            })
            ->latest()
            ->paginate(10);

        // Preserve search parameter in pagination links
        $books->appends(['search' => $search]);

        return view('bookpage', compact('books'));
    }

    /**
     * Show the form for editing the specified book.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\RedirectResponse
     */
    public function edit($id)
    {
        try {
            // Find the book to edit
            $editBook = ArticalModel::findOrFail($id);

            // Get all books for the table
            $books = ArticalModel::latest()->paginate(10);

            return view('dashboardofproject.bookcopy', compact('editBook', 'books'));
        } catch (\Exception $e) {
            return redirect()->route('bookd.index')
                ->with('error', translateText('کتاب ونه موندل شو: ') . $e->getMessage());
        }
    }

    /**
     * Update the specified book in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'author' => 'required|string|max:255',
            'publishdate' => 'required|date',
            'language' => 'required|string|max:255',
            'type' => 'required|string|max:255',
            'catagory' => 'required|string|max:255',
            'description' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'author_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'file' => 'nullable|mimes:pdf,doc,docx|max:10240',
        ]);

        try {
            $book = ArticalModel::findOrFail($id);
            $book->A_Title = $request->title;
            $book->A_Author = $request->author;
            $book->A_Publication_date = $request->publishdate;
            $book->A_Language = $request->language;
            $book->A_Type = $request->type;
            $book->A_catagory = $request->catagory;
            $book->A_Description = $request->description;

            // Handle book cover image upload if a new image is provided
            if ($request->hasFile('image')) {
                // Delete old image if it exists
                if ($book->A_Image && file_exists(public_path('imagese/' . $book->A_Image))) {
                    unlink(public_path('imagese/' . $book->A_Image));
                }

                $imageName = time() . '_image.' . $request->image->extension();
                $request->image->move(public_path('imagese'), $imageName);
                $book->A_Image = $imageName;
            }

            // Handle author image upload if a new author image is provided
            if ($request->hasFile('author_image')) {
                // Delete old author image if it exists
                if ($book->A_Author_Image && file_exists(public_path('imagese/' . $book->A_Author_Image))) {
                    unlink(public_path('imagese/' . $book->A_Author_Image));
                }

                $authorImageName = time() . '_author.' . $request->author_image->extension();
                $request->author_image->move(public_path('imagese'), $authorImageName);
                $book->A_Author_Image = $authorImageName;
            }

            // Handle file upload if a new file is provided
            if ($request->hasFile('file')) {
                // Delete old file if it exists
                if ($book->A_File && file_exists(public_path('imagese/' . $book->A_File))) {
                    unlink(public_path('imagese/' . $book->A_File));
                }

                $fileName = time() . '_file.' . $request->file->extension();
                $request->file->move(public_path('imagese'), $fileName);
                $book->A_File = $fileName;
            }

            $book->save();

            return redirect()->route('bookd.index')
                ->with('success', translateText('کتاب په بریالیتوب سره تازه شو!'));
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', translateText('تېروتنه: ') . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified book from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            $book = ArticalModel::findOrFail($id);

            // Delete image file if it exists
            if ($book->A_Image && file_exists(public_path('imagese/' . $book->A_Image))) {
                unlink(public_path('imagese/' . $book->A_Image));
            }

            // Delete document file if it exists
            if ($book->A_File && file_exists(public_path('imagese/' . $book->A_File))) {
                unlink(public_path('imagese/' . $book->A_File));
            }

            $book->delete();

            return redirect()->route('bookd.index')
                ->with('success', translateText('کتاب په بریالیتوب سره لرې شو!'));
        } catch (\Exception $e) {
            return redirect()->route('bookd.index')
                ->with('error', translateText('تېروتنه: ') . $e->getMessage());
        }
    }
}





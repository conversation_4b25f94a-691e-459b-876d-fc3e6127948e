
// Use questions from database if available, otherwise fallback to hardcoded
const questions = window.databaseQuestions || [
window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟") : "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟",
window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي د تقویت درمل ته اړتیا لري؟") : "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي د تقویت درمل ته اړتیا لري؟",
window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دي د سستي او کمزوري احساس کړی؟") : "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دي د سستي او کمزوري احساس کړی؟",
window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې د ناروغۍ احساس کړی؟") : "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې د ناروغۍ احساس کړی؟",
window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه د سر درد لري؟") : "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه د سر درد لري؟",
window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي سر دي په دستمال وتړم تر څو د درد کم شي؟") : "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي سر دي په دستمال وتړم تر څو د درد کم شي؟",
window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي ګرم یا یخ کیږي؟") : "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي ګرم یا یخ کیږي؟",
window.trans_static ? window.trans_static("آیا له دیرشو(۳۰) ورځو راهیسي د تشویش له امله بې خوبه شوی یې؟") : "آیا له دیرشو(۳۰) ورځو راهیسي د تشویش له امله بې خوبه شوی یې؟",
window.trans_static ? window.trans_static("آیا د خوب په منځ کې راویښیږې او خوب دې ګډوډیږي؟") : "آیا د خوب په منځ کې راویښیږې او خوب دې ګډوډیږي؟",
window.trans_static ? window.trans_static("آیا همېشه د فشار لاندې یې؟") : "آیا همېشه د فشار لاندې یې؟",
// window.trans_static ? window.trans_static("آیا د کورنۍ غړو سره ښه اړیکه لرئ؟") : "آیا د کورنۍ غړو سره ښه اړیکه لرئ؟",
// window.trans_static ? window.trans_static("آیا د ملګرو سره ښه اړیکه لرئ؟") : "آیا د ملګرو سره ښه اړیکه لرئ؟",
// window.trans_static ? window.trans_static("آیا د کار ځای کې ستونزې لرئ؟") : "آیا د کار ځای کې ستونزې لرئ؟",
// window.trans_static ? window.trans_static("آیا د پیسو ستونزې لرئ؟") : "آیا د پیسو ستونزې لرئ؟",
// window.trans_static ? window.trans_static("آیا د راتلونکي په اړه اندېښنه لرئ؟") : "آیا د راتلونکي په اړه اندېښنه لرئ؟"
];

// Function to create the question form
function createQuestionForm() {
    const form = document.getElementById("mentalForm");
    if (!form) return;

    // Always prioritize database questions, fallback to hardcoded only if no database questions
    const questionsToUse = (window.databaseQuestions && window.databaseQuestions.length > 0) ? window.databaseQuestions : questions;

    console.log('Creating form with questions:', questionsToUse.length, 'questions');
    console.log('Database questions available:', window.databaseQuestions ? window.databaseQuestions.length : 0);

    // Clear any existing question steps (keep step-0 which is personal info)
    const existingSteps = form.querySelectorAll('.form-step:not(#step-0)');
    existingSteps.forEach(step => step.remove());

    questionsToUse.forEach((question, index) => {
const stepDiv = document.createElement("div");
stepDiv.className = "form-step";
stepDiv.id = `step-${index + 1}`;

const q = document.createElement("p");
q.textContent = `${index + 1} - ${question}`;
stepDiv.appendChild(q);

// Use dynamic options if available, otherwise use default
const options = (window.questionOptions && window.questionOptions[index]) || [
  { value: "A", text: window.trans_static ? window.trans_static("هیڅ نه") : "هیڅ نه" },
  { value: "B", text: window.trans_static ? window.trans_static("لږه اندازه") : "لږه اندازه" },
  { value: "C", text: window.trans_static ? window.trans_static("لږه ډېره اندازه") : "لږه ډېره اندازه" },
  { value: "D", text: window.trans_static ? window.trans_static("ډېره اندازه") : "ډېره اندازه" }
];
options.forEach(opt => {
const label = document.createElement("label");
label.className = "radio-group";
label.innerHTML = `<input type="radio" name="q${index}" value="${opt.value}"> ${opt.text}`;
stepDiv.appendChild(label);
});

const button = document.createElement("button");
if (index < questionsToUse.length - 1) {
button.type = "button";
button.textContent = window.trans_static ? window.trans_static("بله پوښتنه") : "بله پوښتنه";
button.onclick = () => nextStep();
} else {
button.type = "submit";
button.textContent = window.trans_static ? window.trans_static("لیږل") : "لیږل";
}

stepDiv.appendChild(button);
form.appendChild(stepDiv);
    });
}

// Call the function to create the form
// Wait a bit for database questions to load
setTimeout(function() {
    createQuestionForm();
    console.log('Question form created with', (window.databaseQuestions && window.databaseQuestions.length > 0) ? 'database questions' : 'fallback questions');
}, 100);

let currentStep = 0;

// Function to refresh questions before starting questionnaire
window.refreshQuestionsAndStart = function() {
    console.log('Refreshing questions before starting questionnaire...');

    // Show loading state on button
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> د پوښتنو راوړل...';
    button.disabled = true;

    fetch('/questions-for-questionnaire')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.questions) {
                // Update questions arrays
                window.databaseQuestions = data.questions.map(q => q.question_text);
                window.questionOptions = data.questions.map(q => [
                    {value: 'A', text: q.A},
                    {value: 'B', text: q.B},
                    {value: 'C', text: q.C},
                    {value: 'D', text: q.D}
                ]);

                console.log('Questions refreshed successfully:', window.databaseQuestions.length, 'questions');

                // Regenerate the form with new questions
                createQuestionForm();

                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;

                // Now proceed with validation and next step
                window.nextStep();
            } else {
                console.error('Failed to refresh questions:', data.message);
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
                // Proceed with existing questions
                window.nextStep();
            }
        })
        .catch(error => {
            console.error('Error refreshing questions:', error);
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
            // Proceed with existing questions
            window.nextStep();
        });
}

// Make nextStep function global so it can be called from onclick
window.nextStep = function() {
    window.clearErrors();
    const currentDiv = document.getElementById(`step-${currentStep}`);
    const inputs = currentDiv.querySelectorAll("input[type='text']");
    const radios = currentDiv.querySelectorAll("input[type='radio']");
    let valid = true;

    if (currentStep === 0) {
        inputs.forEach(input => {
            const name = input.name;
            const value = input.value.trim();

            if (name === "name") {
                if (!/^[\u0600-\u06FF\s]{1,25}$/.test(value)) {
                    window.showError(input, "نوم باید تر ۲۵ حروفو کم وي او هیڅ عدد یا سمبول پکې نه وي");
                    valid = false;
                }
            }

            if (name === "phone") {
                if (!/^07\d{8}$/.test(value)) {
                    window.showError(input, "تلیفون باید له ۱۰ عدده جوړ وي او له 07 پیل شي");
                    valid = false;
                }
            }

            if (name === "age") {
                const age = parseInt(value);
                if (isNaN(age) || age <= 0 || age > 60) {
                    window.showError(input, "عمر باید عدد وي او تر ۶۰ کم وي");
                    valid = false;
                }
            }

            if (["province", "district", "village"].includes(name)) {
                if (!/^[\u0600-\u06FF\s]{1,10}$/.test(value)) {
                    window.showError(input, "یواځې حروف وکاروئ (تر ۱۰ کم)، عدد یا سمبول مه داخلوئ");
                    valid = false;
                }
            }
        });

        const genderContainer = currentDiv.querySelector('label[for="gender"]') || currentDiv;
        const genderInputs = currentDiv.querySelectorAll('input[name="gender"]');
        const genderChecked = Array.from(genderInputs).some(r => r.checked);

        if (!genderChecked) {
            const lastRadio = genderInputs[genderInputs.length - 1];
            window.showError(lastRadio, "مهرباني وکړئ خپل جنسیت وټاکئ");
            valid = false;
        }

    } else {
        // د پوښتنو مرحله
        valid = Array.from(radios).some(r => r.checked);
        if (!valid) {
            const q = currentDiv.querySelector("p");
            const error = document.createElement("div");
            error.classList.add("error-msg");
            error.textContent = "مهرباني وکړئ دا پوښتنه ځواب کړئ";
            error.style.color = "red";
            currentDiv.appendChild(error);
        }
    }

    if (!valid) return;

    currentDiv.classList.remove("active");
    currentStep++;
    const nextDiv = document.getElementById(`step-${currentStep}`);
    if (nextDiv) nextDiv.classList.add("active");
}

// Make showError function global
window.showError = function(input, message) {
    let error = input.nextElementSibling;
    if (!error || !error.classList.contains('error-msg')) {
        error = document.createElement('div');
        error.classList.add('error-msg');
        error.style.color = 'red';
        error.style.fontSize = '14px';
        input.insertAdjacentElement('afterend', error);
    }
    error.textContent = message;
}

// Make clearErrors function global
window.clearErrors = function() {
    document.querySelectorAll('.error-msg').forEach(e => e.remove());
}

// Add form submit event listener
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById("mentalForm");
    if (form) {
        form.addEventListener("submit", function(e) {
            // Allow the form to submit normally to the server
            // The server will handle the data storage and redirect
            console.log("Form is being submitted to server...");
        });
    }
});
@extends('layouts.Admin')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-2xl shadow-xl p-6 border border-blue-100 overflow-hidden mb-8">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-800 border-b-2 border-blue-500 pb-2">
                {{ translateText('د ناروغ معلومات') }}
            </h3>

            <a href="{{ route('patient.index') }}" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all duration-200 flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                {{ translateText('بیرته') }}
            </a>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="text-lg font-semibold text-gray-700 mb-3">{{ translateText('شخصي معلومات') }}</h4>
                <div class="space-y-2">
                    <p><strong>{{ translateText('نوم:') }}</strong> {{ $patient->Patiet_Name }}</p>
                    <p><strong>{{ translateText('عمر:') }}</strong> {{ $patient->Patient_Age }}</p>
                    <p><strong>{{ translateText('جنسیت:') }}</strong> {{ $patient->Patient_Gender }}</p>
                    <p><strong>{{ translateText('تلیفون:') }}</strong> {{ $patient->Patient_phone ?? translateText('نشته') }}</p>
                    <p><strong>{{ translateText('بریښنالیک:') }}</strong> {{ $patient->Patient_email ?? translateText('نشته') }}</p>
                    <p><strong>{{ translateText('د ثبت نیټه:') }}</strong> {{ $patient->created_at->format('Y-m-d') }}</p>
                </div>
            </div>

            <div>
                <h4 class="text-lg font-semibold text-gray-700 mb-3">{{ translateText('د استوګنې ځای') }}</h4>
                <div class="space-y-2">
                    <p><strong>{{ translateText('ولایت:') }}</strong> {{ $patient->address->P_Province ?? translateText('نشته') }}</p>
                    <p><strong>{{ translateText('ولسوالۍ:') }}</strong> {{ $patient->address->P_Distract ?? translateText('نشته') }}</p>
                    <p><strong>{{ translateText('کلی:') }}</strong> {{ $patient->address->P_Village ?? translateText('نشته') }}</p>
                </div>
            </div>
        </div>

        <div class="mt-8">
            <h4 class="text-lg font-semibold text-gray-700 mb-3">{{ translateText('د ډاکټر معلومات') }}</h4>
            @if($patient->doctor)
                <div class="space-y-2">
                    <p><strong>{{ translateText('د ډاکټر نوم:') }}</strong> {{ $patient->doctor->Dr_Name }}</p>
                    <p><strong>{{ translateText('تخصص:') }}</strong> {{ $patient->doctor->Dr_Specialization }}</p>
                    <p><strong>{{ translateText('تلیفون:') }}</strong> {{ $patient->doctor->Dr_Personal_phone }}</p>
                </div>
            @else
                <p>{{ translateText('د ډاکټر معلومات نشته') }}</p>
            @endif
        </div>

        @if(isset($patient->questions) && count($patient->questions) > 0)
            <div class="mt-8">
                <h4 class="text-lg font-semibold text-gray-700 mb-3">{{ translateText('د پوښتنو ځوابونه') }}</h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white border border-gray-200">
                        <thead>
                            <tr>
                                <th class="py-2 px-4 border-b text-right">{{ translateText('پوښتنه') }}</th>
                                <th class="py-2 px-4 border-b text-right">{{ translateText('ځواب') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($patient->questions as $question)
                                <tr>
                                    <td class="py-2 px-4 border-b">{{ $question->question->Question_Text ?? $question->Question_Id }}</td>
                                    <td class="py-3 px-3 border-b">{{ $question->Answer }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        @endif
    </div>
</div>
@endsection

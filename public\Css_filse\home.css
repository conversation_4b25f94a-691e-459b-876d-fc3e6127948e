/* عمومی ریسټایل */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: #1d2a3a;
  padding-top: 70px; /* د navbar د ارتفاع لپاره */
}

/* د Navbar اصلی ډیزاین */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(33, 45, 64, 0.95);
  padding: 0 20px;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  height: 55px;
  z-index: 1000;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
}

/* لوګو سټایل */
.logo {
  display: flex;
  align-items: center;
}

.logo img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 10px;
}

/* د مینو تڼۍ */
.menu-toggle {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 28px;
  cursor: pointer;
  padding: 0.1px 10px;
}

/* نویگیشن لینکونه */
.nav-links {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-links li {
  position: relative;
}

.nav-links a {
  color: white;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  border-radius: 5px;
}

.nav-links a:hover {
  background-color: rgba(151, 13, 151, 0.7);
  color: #fff;
}

.nav-links a i {
  font-size: 18px;
}

/* ډراپ ډاون مینو */
.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  min-width: 160px;
  box-shadow: 0 8px 16px rgba(0,0,0,0.2);
  border-radius: 5px;
  z-index: 1;
  display: none;
}

.dropdown-menu a {
  color: #333;
  padding: 12px 16px;
  display: block;
  text-align: right;
}

.dropdown-menu a:hover {
  background-color: #f1f1f1;
}

li:hover .dropdown-menu {
  display: block;
}

/* ریسپانسیو ډیزاین */
@media (max-width: 992px) {
  .nav-links a {
    font-size: 15px;
    padding: 5px 12px;
  }
}

@media (max-width: 768px) {
  .navbar {
    padding: 0 15px;
  }
  
  .menu-toggle {
    display: block;
  }
  
  .nav-links {
    position: fixed;
    top: 65px;
    right: -100%;
    width: 200px;
    height: calc(100vh - 70px);
    background-color: #212d40;
    flex-direction: column;
    align-items: flex-end;
    padding: 20px;
    transition: right 0.3s ease;
  }
  
  .nav-links.active {
    right: 0;
  }
  
  .nav-links li {
    width: 100%;
    margin: 5px 0;
  }
  
  .nav-links a {
    padding: 8px 20px;
    justify-content: flex-end;
  }
  
  .dropdown-menu {
    position: static;
    width: 100%;
    box-shadow: none;
    display: none;
    margin-top: 5px;
  }
  
  .dropdown-menu a {
    color: white;
    background-color: rgba(0,0,0,0.2);
  }
  
  .dropdown-menu a:hover {
    background-color: rgba(0,0,0,0.3);
  }
  
  li:hover .dropdown-menu {
    display: none;
  }
  
  li.active .dropdown-menu {
    display: block;
  }
}





/* =====Home page============================  */

  .image-container{
      background-image: url(../imagese/fullscreen_background_image.jpg);
    background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 88vh;
  width: 100%;
  }
.text-section{
 
    text-align: right;
}

.text-section h1 {
  font-weight: bold;
  font-size: 40px;
  color: black;
  direction: rtl;
  padding-top: 4em;
  padding-right: 2em;
  font-size: 3em;
  text-align: right;
}

.text-section h1 strong {
  color: orange;
  font-size: 40px;
   direction: rtl;
}

.text-section p {
     direction: rtl;
  margin: 1rem 0;
  line-height: 1.6;
  color: #523d3d;
  padding-right: 5em;
}

 .text-section #see-more {
  padding: 5px 20px;
  background: rgb(151, 13, 151);
  color: white;
  border: none;
  text-decoration: none;
  border-radius: 5px;
  cursor: pointer;
margin-right: 5em;
margin-top: 5em;
z-index: 3;
}
#see-more:hover{
  background-color: #ed4a4a;
}
.social-iconss {
  display: flex;
  gap: 20px;
  margin-top: 6em;
  margin-left: 4em;
}

.social-iconss a {
  font-size: 24px;
  color: orange; /* آیکونونه سپین شول */
  text-decoration: none;
  transition: color 0.3s ease;
}

.social-iconss a:hover {
  color: #9f892f; /* د hover پر وخت ژیړ رنګ، اختیاري */
}

    /* ====populor-section=========================  */
 
    #populor-section {
      padding: 1px 20px 10px 20px;
      background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(15, 15, 15, 0.8)), url('../imagese/book2.jpg') center/cover no-repeat;
      text-align: center;
    }

    #populor-section h1 {
      font-size: 2rem;
      margin-bottom: 2px;
      color: white;
      padding-top: 8px;
      padding-bottom: 8px;
    }

    #populor-section p {
      font-size: 1.1rem;
      max-width: 800px;
      margin: 0 auto 15px auto;
      line-height: 1.8;
      color: white;
    }

    #wrapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 30px;
    }

    .ererer {
      position: relative;
      width: 300px;
      height: 410px;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
      background: url('../imagese/book.jpg') center/cover no-repeat;
      transition: transform 0.3s ease;
    }

    .ererer::before {
      content: '';
      position: absolute;
      inset: 0;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.3), rgba(0,0,0,0.3));
      z-index: 1;
    }

    .ererer:hover {
      transform: scale(1.05);
    }

    .ererer-content {
      position: relative;
      z-index: 2;
      height: 97%;
      padding: 20px;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      color: #fff;
      text-align: right;
    }

    .ererer-content h3 {
      font-size: 1.8rem;
      margin-bottom: 45px;
    }

    .ererer-content p {
      background-color: rgba(0, 0, 0, 0.2);
     box-shadow: 5px 4px rgba(0, 0, 0, 0.6);
      border-radius: 15px;
      font-size: 1rem;
      line-height: 1.6;
      margin-top: 1em;
    }

    .btns {
      margin-top: 5px;
      padding: 10px 18px;
      background-color: royalblue;
      color: white;
      border: none;
      border-radius: 25px;
      text-decoration: none;
      font-size: 0.95rem;
      cursor: pointer;
      transition: background-color 0.3s;
      text-align: center;
      display: inline-block;
         gap: 10px;
    }

    .btns:hover {
      background-color: rgb(94, 119, 194);
    }

    @media (max-width: 768px) {
      #wrapper {
        flex-direction: column;
        align-items: center;
      }
    }
          
   /* =====video_section==================  */
/* ======================================== */

    #video_section {
       direction: rtl;
      padding: 30px 30px;
      text-align: center;
    }

    #video_section h1 {
      font-size: 28px;
      color: white;
      margin-bottom: 20px;
    }

    .teachers-section {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 25px;
    }

    @media (max-width: 1200px) {
      .teachers-section {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .teachers-section {
        grid-template-columns: 1fr;
      }
    }

    .teacher-card {
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 6px 18px rgba(0,0,0,0.08);
      padding: 20px;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .teacher-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 24px rgba(0,0,0,0.12);
    }

    .teacher-card img {
      width: 90px;
      height: 90px;
      border-radius: 50%;
      border: 3px solid #0ea5e9;
      margin-bottom: 15px;
      object-fit: cover;
    }

    .teacher-name {
      font-size: 17px;
      font-weight: bold;
      color: #0f172a;
      margin-bottom: 10px;
    }

    .teacher-info {
      font-size: 14px;
      color: #475569;
      text-align: right;
      line-height: 2;
    }

    .teacher-info i {
      margin-left: 8px;
      color: #0ea5e9;
    }

    .youtube-btn {
      background-color: #dc2626;
      color: #fff;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 13px;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      margin-top: 15px;
      transition: all 0.3s ease;
    }

    .youtube-btn:hover {
      background-color: #b91c1c;
      transform: scale(1.05);
    }

    .extra-links {
      margin-top: 10px;
      font-size: 13px;
    }

    .extra-links a {
      color: #2563eb;
      text-decoration: none;
      margin-left: 10px;
    }

    .extra-links a i {
      margin-left: 4px;
    }
   /* ======testing section==================  */

#addmission-section{
  background-image: linear-gradient(rgba(0,0,0,0.6),rgba(0,0,0,0.6)),url('../imagese/sd.jpg');
  background-color: rgb(194, 187, 187);
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  width: 100%;
  height: auto;
  padding: 100px 0;
  text-align: center;
}
#addmission-section #addmission-content{
  background-color: rgba(255, 255, 255, 0.8);
  width: 70%;
  height: auto;
  margin: 0 auto;
  padding: 30px 30px;
  border-radius: 12px;
  border: 1px solid black;
}
#addmission-section #addmission-content h1{
  color: black;
  font-size: 32px;

}
#addmission-section #addmission-content p{
  color: black;
  font-weight: bold;
 padding: 20px 0;
 direction: rtl;
}
#addmission-section #addmission-content a{
  padding: 10px 30px;
  background-color: black;
  color: white;
  margin-top: 15px;
  text-decoration: none;
  font-size: 17px;
  display: inline-block;
  border-radius: 0.4em;

}
#addmission-section #addmission-content a:hover{
  background-color:#0d6b6b;
  color: white;
}
/*=======testemonial section==========  */
#boody {
  font-family: sans-serif;
  /* background: #375376; */
  color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  direction: rtl;
}
.testimonial-container {
  background-color: #243247;;
  border-radius: 20px;
  padding: 30px 20px;
  max-width: 500px;
  text-align: center;
  box-shadow: 0 10px 20px rgba(0,0,0,0.3);
  position: relative;
  display: none;
  height: 23em;
}
.testimonial-container.active {
  display: block;
}
.profile-pic {
  width: 100px;
  height: 100px;
  background-size: cover;
  background-position: center;
  border-radius: 50%;
  margin: 0 auto 20px;
  border: 4px solid orange;
}
.name {
  font-weight: bold;
  font-size: 20px;
  margin-bottom: 5px;
}
.designation {
  font-size: 14px;
  color: #bbb;
  margin-bottom: 5px;
}
.quote {
  font-size: 16px;
  font-style: italic;
  line-height: 1.6;
  position: relative;
  padding: 10px 20px;
}
.quote::before, .quote::after {
  content: '"';
  font-size: 30px;
  color: orange;
}
.stars {
  color: gold;
  margin-top: 15px;
}
.buttons {
  margin-top: 30px;
  display: flex;
  gap: 10px;
}
.buttons button {
  background: orange;
  border: none;
  color: #fff;
  padding: 10px 16px;
  border-radius: 10px;
  cursor: pointer;
  transition: 0.3s;
}
.buttons button:hover {
  background: #e67e22;
}
#kjkj{
  width: 100%;
  height: 2px;
  background-color: white;
}
/*   ====== contact_us  section ===========    */

/* د اړیکې سیکشن اصلی ډیزاین */
#contact_us {

  direction: rtl;
  width: 100%;
  min-height: 100vh;
  padding: 7px 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

#contact_us h1 {
  text-align: center;
  color: white;
  font-size: 2rem;
  margin: 1.5rem auto;
}

.container {
  display: flex;
  justify-content: space-between;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  /* background: #3a387b; */
  background-color: whitesmoke;
  border-radius: 15px;
  color: black;
  box-shadow: 0 0 25px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  flex-wrap: wrap;
}

.form-box, .info-box {
  flex: flex;
  min-width: 300px;
  padding: 1rem 4rem;
  margin-right: 2em;
}

.form-box h2 {
  /* color: #ff4081; */
  color: #211ca4;
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.form-box p {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.form-group {
  margin-bottom: 0.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.form-group input, 
.form-group textarea {
  width: 11em;
  padding: 0.7rem 1.5rem;
  border: none;
  border-radius: 6px;
  background:  rgb(217, 222, 238);
  color: black;
  font-size: 1rem;
  transition: all 0.3s ease;
}
.form-group textarea{
  width: 23em;
}
.form-group input:focus, 
.form-group textarea:focus {
  outline: none;
  box-shadow: 0 0 0 2px #ff4081;
}

.error-message {
  color: #ef5454;
  font-size: 0.8rem;
  margin-top: 0.1rem;
  width: 11em;
  height: 0.5rem;
}

button[type="submit"] {
  /* background: #ff4081; */
  background-color:  royalblue;
  color: white;
  border: none;
  padding: 0.3rem 2.5rem;
  border-radius: 6px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: auto;

}

button[type="submit"]:hover {
  background: #e03a6d;
  transform: translateY(-2px);
}

.info-box h3 {
  /* color: #ff4081; */
  color: #211ca4;
  font-size: 1.2rem;
  width: 13em;
  margin: 1.5rem 7em 1.5rem 1em ;
}

.info-box p {
  font-size: 1.1rem;
  line-height: 1.6;
  width: 13em;

  margin-bottom: 1.5rem;
  margin-right: 8em;
}

.social-links {
  display: flex;
  gap: 1.5rem;
  width: 13em;
  margin-right: 8.5em;
  margin-top: 1.5rem;

}

.social-links a {
  font-size: 1.5rem;
  color: royalblue;
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-block;
}

.social-links a:hover {
  color: #ff4081;
  transform: scale(1.2);
}

/* د ګروپ شوي انپوټونو لپاره */
.input-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.input-group .form-group {
  flex: 1;
  margin-bottom: 0;
}

/* ریسپانسیو ډیزاین */
@media (max-width: 992px) {
  .container {
    flex-direction: column;
  }
  
  .form-box, .info-box {
    width: 100%;
    padding: 1.5rem;
  }
  
  #contact_us h1 {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .input-group {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .form-box, .info-box {
    padding: 1.2rem;
  }
  
  button[type="submit"] {
    width: 100%;
  }
}

   /* =====footer-section============== */
#footer-section {
  background-color: #243247;
  width: 100%;
  height: auto;
  display: flex;
  justify-content: space-between;
}
#footer-section .aboutUs-section {
  padding: 2em 5%;
  /* width: 40%; */
  display: flex;
  flex-direction: column;
}
.headings {
  color: white;
  margin-bottom: 10px;
  font-size: 1.3em;
  font-weight: bold;
  direction: rtl;
 
}
.mylinks {
  text-decoration: none;
  color: white;
  margin-bottom: 4px;
  direction: rtl;
  padding-top: 1em;

}
.mylinks:hover {
  text-decoration: underline;
  color: orange;
}

.hgfd a {
  display: inline-block;
  margin: 10px;
  font-size: 17px;
  color: white;
  transition: transform 0.3s ease;
}

.hgfd a:hover {
  transform: scale(1.2);
  color: #ff4081;
}















@media (max-width: 768px) {
  .text-section h1 {
    font-size: 2em;
    padding-top: 4.5em;
    padding-right: 0.5em;
  }

  .text-section p {
    padding-right: 1em;
    font-size: 14px;
    padding-top: 1.5em;
  }

  .text-section #see-more {
    margin-right: 1em;
    margin-top: 2em;
  }

  .social-icons {
margin-right: 15em;
   margin-top: 6em;

  }
}





@media (max-width: 768px) {
  .testimonial-container {
    width: 90%;
    margin: 0 auto;
    height: auto;
    padding: 20px;
  }
}





@media (max-width: 768px) {
    #contact_us{
        width: 100%;
        height: auto;
    }

}





@media (max-width: 768px) {
  #footer-section {
    flex-direction: column;
    align-items: center;
    padding: 1em;
  }

  #footer-section .aboutUs-section {
    padding: 1em;
    width: 100%;
    text-align: center;
  }
}




@media (max-width: 768px) {
  .dddd {
    flex-direction: column;
  }

  .form-group {
    margin-right: 0;
  }
}

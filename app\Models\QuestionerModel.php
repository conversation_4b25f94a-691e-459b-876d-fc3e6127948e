<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuestionerModel extends Model
{
    use HasFactory;

    // د جدول نوم مشخص کول
    protected $table = 'questioner_models';
    
    // د اصلي کلید مشخص کول
    protected $primaryKey = 'Q_Id';
    
    // د ډکولو وړ ستونونه مشخص کول
    protected $fillable = [
        'Q_Discription',
        'Question_No',
        'A',
        'B',
        'C',
        'D',
        'Selected_Option',
        'Patient_Id'
    ];

    /**
     * Get the patient that owns the questioner.
     */
    public function patient()
    {
        return $this->belongsTo(PatientModel::class, 'Patient_Id', 'Patient_id');
    }
}























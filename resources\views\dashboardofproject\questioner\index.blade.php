@extends('layouts.dashboard')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">{{ translateText('د پوښتنو مدیریت') }}</h1>
        <a href="{{ route('questioner.create') }}" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
            {{ translateText('نوې پوښتنه اضافه کړئ') }}
        </a>
    </div>

    @if(session('success'))
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline">{{ session('success') }}</span>
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline">{{ session('error') }}</span>
    </div>
    @endif

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full bg-white">
            <thead class="bg-gray-800 text-white">
                <tr>
                    <th class="py-3 px-4 text-right">{{ translateText('شمېره') }}</th>
                    <th class="py-3 px-4 text-right">{{ translateText('د پوښتنې ډول') }}</th>
                    <th class="py-3 px-4 text-right">{{ translateText('پوښتنه') }}</th>
                    <th class="py-3 px-4 text-right">{{ translateText('الف') }}</th>
                    <th class="py-3 px-4 text-right">{{ translateText('ب') }}</th>
                    <th class="py-3 px-4 text-right">{{ translateText('ج') }}</th>
                    <th class="py-3 px-4 text-right">{{ translateText('د') }}</th>
                    <th class="py-3 px-4 text-right">{{ translateText('عملیات') }}</th>
                </tr>
            </thead>
            <tbody class="text-gray-700">
                @forelse($questions as $question)
                <tr class="border-b hover:bg-gray-100">
                    <td class="py-3 px-4">{{ $question->Question_No }}</td>
                    <td class="py-3 px-4">
                        @if($question->Q_Type == 'mental')
                            {{ translateText('رواني') }}
                        @elseif($question->Q_Type == 'physical')
                            {{ translateText('فزیکي') }}
                        @elseif($question->Q_Type == 'social')
                            {{ translateText('ټولنیز') }}
                        @endif
                    </td>
                    <td class="py-3 px-4">{{ $question->Q_Discription }}</td>
                    <td class="py-3 px-4">{{ $question->A }}</td>
                    <td class="py-3 px-4">{{ $question->B }}</td>
                    <td class="py-3 px-4">{{ $question->C }}</td>
                    <td class="py-3 px-4">{{ $question->D }}</td>
                    <td class="py-3 px-4">
                        <div class="flex space-x-2">
                            <a href="{{ route('questioner.edit', $question->Q_Id) }}" class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-2 rounded text-sm">
                                {{ translateText('تعدیل') }}
                            </a>
                            <form action="{{ route('questioner.destroy', $question->Q_Id) }}" method="POST" onsubmit="return confirm('{{ translateText('آیا تاسو ډاډه یاست چې دا پوښتنه حذف کړئ؟') }}');">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-2 rounded text-sm">
                                    {{ translateText('حذف') }}
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="8" class="py-3 px-4 text-center">{{ translateText('هیڅ پوښتنه شتون نلري') }}</td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>

    <div class="mt-4">
        {{ $questions->links() }}
    </div>
</div>
@endsection
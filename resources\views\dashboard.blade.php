<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ translateText('ډشبورډ') }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        h1 {
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>{{ trans_static('ښه راغلاست') }}</h1>
        <p>{{ trans_static('تاسو اوس په ډشبورډ کې یاست') }}</p>

        @if(session('success'))
            <div class="alert alert-success">
                {{ trans_static(session('success')) }}
            </div>
        @endif

        <h2>{{ trans_static('لنډیز') }}</h2>
        <p>{{ trans_static('ټول ناروغان') }}: {{ Auth::user()->name }}</p>
        <p>{{ trans_static('ستاسو بریښنالیک') }}: {{ Auth::user()->email }}</p>
        <p>{{ trans_static('ستاسو د زیږیدو نیټه') }}: {{ Auth::user()->date_of_birth }}</p>

        <form action="{{ route('logout') }}" method="POST">
            @csrf
            <button type="submit" class="logout-button">{{ trans_static('وتل') }}</button>
        </form>

        <div class="dashboard-action-buttons">
            <a href="{{ route('register') }}" class="btn btn-primary">
                {{ translateText('نوی کارن ثبت کړئ') }}
            </a>
        </div>
    </div>
</body>
</html>









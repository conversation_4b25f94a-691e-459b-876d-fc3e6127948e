@extends('layouts.admin')

@section('title', 'د اړیکې پیغام')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">د اړیکې پیغام</h1>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.contacts.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> بیرته
            </a>
            <form method="POST" action="{{ route('admin.contacts.destroy', $contact->contact_id) }}" 
                  style="display: inline;"
                  onsubmit="return confirm('ایا تاسو ډاډه یاست چې غواړئ دا پیغام ړنګ کړئ؟')">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> ړنګول
                </button>
            </form>
        </div>
    </div>

    <!-- Contact Message Details -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">د پیغام تفصیلات</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>نوم:</strong>
                        </div>
                        <div class="col-sm-9">
                            {{ $contact->fname }}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>تخلص:</strong>
                        </div>
                        <div class="col-sm-9">
                            {{ $contact->lname }}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>ایمیل:</strong>
                        </div>
                        <div class="col-sm-9">
                            <a href="mailto:{{ $contact->email }}">{{ $contact->email }}</a>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>شمیره:</strong>
                        </div>
                        <div class="col-sm-9">
                            <a href="tel:{{ $contact->phone }}">{{ $contact->phone }}</a>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-sm-3">
                            <strong>پیغام:</strong>
                        </div>
                        <div class="col-sm-9">
                            <div class="border p-3 bg-light rounded">
                                {{ $contact->message }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Contact Info Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">اضافي معلومات</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>د یوزر نوم:</strong><br>
                        {{ $contact->user ? $contact->user->name : 'میلمه یوزر' }}
                    </div>
                    
                    <div class="mb-3">
                        <strong>د لیږلو نیټه:</strong><br>
                        {{ $contact->created_at->format('Y-m-d H:i:s') }}
                    </div>
                    
                    <div class="mb-3">
                        <strong>د تازه کولو نیټه:</strong><br>
                        {{ $contact->updated_at->format('Y-m-d H:i:s') }}
                    </div>
                    
                    <div class="mb-3">
                        <strong>پیغام ID:</strong><br>
                        {{ $contact->contact_id }}
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">ګړندۍ عملیات</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="mailto:{{ $contact->email }}" class="btn btn-primary">
                            <i class="fas fa-envelope"></i> ایمیل ولیږئ
                        </a>
                        
                        <a href="tel:{{ $contact->phone }}" class="btn btn-success">
                            <i class="fas fa-phone"></i> زنګ ووهئ
                        </a>
                        
                        <button class="btn btn-info" onclick="copyToClipboard('{{ $contact->email }}')">
                            <i class="fas fa-copy"></i> ایمیل کاپي کړئ
                        </button>
                        
                        <button class="btn btn-warning" onclick="copyToClipboard('{{ $contact->phone }}')">
                            <i class="fas fa-copy"></i> شمیره کاپي کړئ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card-body {
    direction: rtl;
    text-align: right;
}
.row {
    margin-bottom: 0.5rem;
}
.d-grid .btn {
    margin-bottom: 0.5rem;
}
</style>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('کاپي شو: ' + text);
    }, function(err) {
        console.error('د کاپي کولو کې تېروتنه: ', err);
    });
}
</script>
@endsection

<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class PatientsExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize
{
    protected $patients;

    public function __construct($patients)
    {
        $this->patients = $patients;
    }

    public function collection()
    {
        return $this->patients;
    }

    public function headings(): array
    {
        return [
            'شمېره',
            'نوم',
            'عمر',
            'جنسیت',
            'اړیکه',
            'ولایت',
            'ولسوالي',
            'کلی',
            'نېټه',
        ];
    }

    public function map($patient): array
    {
        return [
            $patient->Patient_id,
            $patient->Patiet_Name,
            $patient->Patient_Age,
            $patient->Patient_Gender,
            $patient->Patient_phone ?? 'نشته',
            $patient->address->P_Province ?? 'نشته',
            $patient->address->P_Distract ?? 'نشته',
            $patient->address->P_Village ?? 'نشته',
            $patient->created_at ? $patient->created_at->format('Y-m-d') : 'نشته',
        ];
    }
}


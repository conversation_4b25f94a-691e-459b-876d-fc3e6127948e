/**
 * Function to refresh the CSRF token
 */
function refreshCsrfToken() {
    // Make an AJAX request to get a new token
    fetch('/csrf-token-refresh', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.token) {
            // Update the meta tag
            let metaTag = document.querySelector('meta[name="csrf-token"]');
            if (metaTag) {
                metaTag.setAttribute('content', data.token);
            }

            // Update axios header
            if (window.axios) {
                window.axios.defaults.headers.common['X-CSRF-TOKEN'] = data.token;
            }

            // Update all forms with the new token
            document.querySelectorAll('input[name="_token"]').forEach(input => {
                input.value = data.token;
            });

            console.log('CSRF token refreshed successfully');
        }
    })
    .catch(error => {
        console.error('Error refreshing CSRF token:', error);
    });
}

// Add event listener for token mismatch errors
document.addEventListener('DOMContentLoaded', function() {
    // Listen for CSRF token mismatch errors
    document.addEventListener('csrf-token-mismatch', function() {
        refreshCsrfToken();
    });
});

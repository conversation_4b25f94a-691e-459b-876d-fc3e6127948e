<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('artical_models', function (Blueprint $table) {
            $table->bigIncrements('A_Id');
            $table->string('A_Title');
            $table->string('A_Author');
            $table->date('A_Publication_date');
            $table->string('A_Language');
            $table->string('A_Type');
            $table->string('A_catagory');
            $table->string('A_File');
            $table->string('A_Image');
            $table->text('A_Description')->nullable(); // Add description field
            $table->string('A_Author_Image')->nullable(); // Add author image field
            $table->unsignedBigInteger('user_id');
            $table->foreign('user_id')->references('user_id')->on('users')->onDelete('cascade');
            $table->timestamps();
         

        });
    }
    public function down(): void
    {
        Schema::dropIfExists('artical_models');
    }
};
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('feedback_models', function (Blueprint $table) {
            $table->bigIncrements('Feedback_Id');
             $table->string('Recever_email');
            $table->unsignedBigInteger('User_Id');
            $table->longText('Contents');
            $table->foreign('User_id')->references('user_id')->on('users')->onDelete('cascade');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('feedback_models');
    }
};


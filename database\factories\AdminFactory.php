<?php

namespace Database\Factories;

use Illuminate\Support\Str;

use App\Models\Admin;

use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Model>
 */
class AdminFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
              
            'name' =>'<PERSON>',
            'email' => 'mna<PERSON><PERSON><PERSON>@gmail.com',
        


    
        ];
    }
}

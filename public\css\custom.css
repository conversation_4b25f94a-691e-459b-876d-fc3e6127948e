.search-results-container {
    position: absolute;
    width: 100%;
    max-width: 600px;
    z-index: 1000;
    margin-top: 5px;
}

#search {
    position: relative;
}

#searchResults {
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Notification styles */
#notification {
    animation: slideIn 0.3s ease-out forwards;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

#notification.opacity-0 {
    animation: slideOut 0.3s ease-in forwards;
}

@keyframes slideIn {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Notification badge animation */
.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Notification list item hover effect */
.notification-item {
    transition: all 0.3s ease;
}

.notification-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Unread notification indicator */
.unread-indicator {
    position: relative;
}

.unread-indicator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -10px;
    transform: translateY(-50%);
    width: 8px;
    height: 8px;
    background-color: #ef4444;
    border-radius: 50%;
}


<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PatientAnswerModel extends Model
{
    use HasFactory;

    protected $table = 'patient_answer_models';
    protected $primaryKey = 'Answer_Id';
    
    protected $fillable = [
        'Patient_Id',
        'Question_Id',
        'Selected_Answer',
        'Score'
    ];

    public function patient()
    {
        return $this->belongsTo(PatientModel::class, 'Patient_Id', 'Patient_id');
    }

    public function question()
    {
        return $this->belongsTo(QuestionerModel::class, 'Question_Id', 'Q_Id');
    }
}


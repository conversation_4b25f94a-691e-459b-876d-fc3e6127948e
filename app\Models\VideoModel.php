<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VideoModel extends Model
{
    use HasFactory;

    protected $table = 'video_models';
    protected $primaryKey = 'Video_Id';

    protected $fillable = [
        'Video_URL',
        'A_id',
    ];

    // Relationship with ArticalModel
    public function article()
    {
        return $this->belongsTo(ArticalModel::class, 'A_id', 'A_Id');
    }

    // Get YouTube video ID from URL
    public function getYoutubeIdAttribute()
    {
        $videoId = null;

        if (strpos($this->Video_URL, 'youtube.com/watch') !== false) {
            parse_str(parse_url($this->Video_URL, PHP_URL_QUERY), $params);
            $videoId = $params['v'] ?? null;
        } elseif (strpos($this->Video_URL, 'youtu.be/') !== false) {
            $videoId = substr(parse_url($this->Video_URL, PHP_URL_PATH), 1);
        }

        return $videoId;
    }

    // Get YouTube thumbnail URL
    public function getYoutubeThumbnailAttribute()
    {
        $videoId = $this->youtube_id;

        if ($videoId) {
            // Return high quality thumbnail
            return "https://img.youtube.com/vi/{$videoId}/hqdefault.jpg";
        }

        return null;
    }

    // Get embedded YouTube URL
    public function getEmbedUrlAttribute()
    {
        $videoId = $this->youtube_id;

        if ($videoId) {
            return "https://www.youtube.com/embed/{$videoId}";
        }

        return $this->Video_URL;
    }
}


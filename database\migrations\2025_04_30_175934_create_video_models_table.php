<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('video_models', function (Blueprint $table) {
            $table->bigIncrements('Video_Id');
    
        
            $table->text('Video_URL');
            $table->unsignedBigInteger('A_id');
            $table->foreign('A_id')->references('A_Id')->on('artical_models')->onDelete('cascade');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('video_models');
    }
};

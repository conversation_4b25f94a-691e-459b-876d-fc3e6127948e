<?php

namespace App\Http\Controllers;

use App\Models\Question;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class QuestionBankController extends Controller
{
    /**
     * Display a listing of the questions.
     */
    public function index()
    {
        $questions = Question::orderBy('created_at', 'desc')->get();
        return view('dashboardofproject.questionercopy', compact('questions'));
    }

    /**
     * Store a newly created question in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'question' => 'required|string|max:500',
            'question_type' => 'required|string|in:mental,physical,social',
            'difficulty_level' => 'required|string|in:easy,medium,hard',
            'is_active' => 'boolean',
            'options' => 'required|array|min:2',
            'options.*' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        $question = Question::create([
            'question_text' => $request->question,
            'question_type' => $request->question_type,
            'difficulty_level' => $request->difficulty_level,
            'is_active' => $request->has('is_active') ? true : false,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'پوښتنه په بریالیتوب سره اضافه شوه!',
            'question' => $question
        ]);
    }

    /**
     * Update the specified question in storage.
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'question' => 'required|string|max:500',
            'question_type' => 'required|string|in:mental,physical,social',
            'difficulty_level' => 'required|string|in:easy,medium,hard',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        $question = Question::findOrFail($id);
        
        $question->update([
            'question_text' => $request->question,
            'question_type' => $request->question_type,
            'difficulty_level' => $request->difficulty_level,
            'is_active' => $request->has('is_active') ? true : false,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'پوښتنه په بریالیتوب سره تازه شوه!',
            'question' => $question
        ]);
    }

    /**
     * Remove the specified question from storage.
     */
    public function destroy($id)
    {
        $question = Question::findOrFail($id);
        $question->delete();

        return response()->json([
            'success' => true,
            'message' => 'پوښتنه په بریالیتوب سره حذف شوه!'
        ]);
    }

    /**
     * Seed initial questions from JS file.
     */
    public function seedQuestions()
    {
        // Get questions from the JS file
        $jsQuestions = [
            "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دې دا احساس کړی چي ښه یم؟",
            "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي د تقویت درمل ته اړتیا لري؟",
            "آیا له دیرشو(۳۰) ورځو راهیسي، تر اوسه دي د سستي او کمزوري احساس کړی؟",
            "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې د ناروغۍ احساس کړی؟",
            "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه د سر درد لري؟",
            "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي سر دي په دستمال وتړم تر څو د درد کم شي؟",
            "آیا له دیرشو(۳۰) ورځو راهیسي تر اوسه دې دا احساس کړی چي بدن مي ګرم یا یخ کیږي؟",
            "آیا له دیرشو(۳۰) ورځو راهیسي د تشویش له امله بې خوبه شوی یې؟",
            "آیا د خوب په منځ کې راویښیږې او خوب دې ګډوډیږي؟",
            "آیا همېشه د فشار لاندې یې؟"
        ];

        // Check if questions already exist
        if (Question::count() > 0) {
            return redirect()->route('questions.index')->with('warning', 'پوښتنې مخکې له مخکې موجودې دي!');
        }

        // Insert questions
        foreach ($jsQuestions as $questionText) {
            Question::create([
                'question_text' => $questionText,
                'question_type' => 'mental',
                'difficulty_level' => 'medium',
                'is_active' => true
            ]);
        }

        return redirect()->route('questions.index')->with('success', 'پوښتنې په بریالیتوب سره اضافه شوې!');
    }
}
<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\PatientModel;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class NotificationController extends Controller
{
    /**
     * Display notifications page
     */
    public function index(): View
    {
        // Simple approach - get all notifications
        $notifications = Notification::orderBy('created_at', 'desc')->paginate(15);
        $unreadCount = Notification::where('is_read', false)->count();
        $totalCount = Notification::count();

        // Debug logging
        \Log::info('Notification Controller Debug', [
            'total_count' => $totalCount,
            'unread_count' => $unreadCount,
            'notifications_count' => $notifications->count(),
            'notifications_total' => $notifications->total()
        ]);

        return view('dashboardofproject.notification', [
            'notifications' => $notifications,
            'unreadCount' => $unreadCount,
            'totalCount' => $totalCount
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Request $request, $id): JsonResponse
    {
        try {
            $notification = Notification::findOrFail($id);
            $notification->markAsRead();

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as read'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error marking notification as read'
            ], 500);
        }
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead(): JsonResponse
    {
        try {
            Notification::unread()->update([
                'is_read' => true,
                'read_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'All notifications marked as read'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error marking notifications as read'
            ], 500);
        }
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadCount(): JsonResponse
    {
        $count = Notification::unread()->count();
        
        return response()->json([
            'count' => $count
        ]);
    }

    /**
     * Get notifications for API/AJAX
     */
    public function getNotifications(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);
        
        $notifications = Notification::with('patient')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'patient_name' => $notification->patient_name,
                    'patient_id' => $notification->patient_id,
                    'is_read' => $notification->is_read,
                    'time_ago' => $notification->time_ago,
                    'icon' => $notification->icon,
                    'color' => $notification->color,
                    'created_at' => $notification->created_at->format('Y-m-d H:i:s')
                ];
            });

        return response()->json($notifications);
    }

    /**
     * View patient details from notification
     */
    public function viewPatient($notificationId, $patientId): View
    {
        // Mark notification as read
        $notification = Notification::findOrFail($notificationId);
        if ($notification->isUnread()) {
            $notification->markAsRead();
        }

        // Get patient details
        $patient = PatientModel::findOrFail($patientId);

        // Get patient's questionnaire responses if available
        $questionnaireData = $notification->questionnaire_data ?? [];

        // Always fetch fresh data from database to ensure accuracy
        $shouldFetchFromDB = true;

        if ($shouldFetchFromDB) {
            $questionsFromDB = \App\Models\QuestionerModel::where('Patient_Id', $patientId)
                ->orderBy('Question_No')
                ->get();

            if ($questionsFromDB->count() > 0) {
                $detailedQuestions = [];
                foreach ($questionsFromDB as $q) {
                    // Enhanced scoring and option handling
                    $selectedOption = trim($q->Selected_Option);
                    $score = $this->calculateQuestionScore($selectedOption);
                    $selectedText = $this->getSelectedOptionText($selectedOption, $q);
                    $options = $this->getQuestionOptions($q);



                    $detailedQuestions[] = [
                        'question_number' => $q->Question_No,
                        'question' => $q->Q_Discription,
                        'options' => $options,
                        'selected_answer' => $selectedOption,
                        'selected_text' => $selectedText,
                        'score' => $score,
                        'max_score' => 3,
                        'score_percentage' => round(($score / 3) * 100, 1),
                        'score_color' => $this->getScoreColor($score),
                        'score_level' => $this->getScoreLevel($score)
                    ];
                }

                // Update questionnaire data with fetched questions
                $questionnaireData['questions_data'] = $detailedQuestions;
                $questionnaireData = $this->calculateSummaryStatistics($questionnaireData, $detailedQuestions);


            }
        }

        return view('dashboardofproject.patient-details', compact(
            'patient',
            'notification',
            'questionnaireData'
        ));
    }

    /**
     * Delete notification
     */
    public function destroy($id): JsonResponse
    {
        try {
            $notification = Notification::findOrFail($id);
            $notification->delete();

            return response()->json([
                'success' => true,
                'message' => 'Notification deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting notification'
            ], 500);
        }
    }

    /**
     * Calculate score for a question based on selected option
     */
    private function calculateQuestionScore($selectedOption)
    {
        // Clean the selected option
        $selectedOption = trim(strtoupper($selectedOption));

        // Handle both letter format and text format
        $score = match($selectedOption) {
            'A' => 0,
            'B' => 1,
            'C' => 2,
            'D' => 3,
            // Handle text format responses
            'هیڅ نه' => 0,
            'لږه اندازه' => 1,
            'لږه ډېره اندازه' => 2,
            'ډېره اندازه' => 3,
            default => 0
        };



        return $score;
    }

    /**
     * Get selected option text with proper formatting
     */
    private function getSelectedOptionText($selectedOption, $question)
    {
        // Try to get from question record first
        $optionText = match($selectedOption) {
            'A' => $question->A ?? 'هیڅ نه',
            'B' => $question->B ?? 'لږه اندازه',
            'C' => $question->C ?? 'لږه ډېره اندازه',
            'D' => $question->D ?? 'ډېره اندازه',
            default => 'نامعلوم'
        };

        return $optionText;
    }

    /**
     * Get all question options
     */
    private function getQuestionOptions($question)
    {
        return [
            'A' => $question->A ?? 'هیڅ نه',
            'B' => $question->B ?? 'لږه اندازه',
            'C' => $question->C ?? 'لږه ډېره اندازه',
            'D' => $question->D ?? 'ډېره اندازه'
        ];
    }

    /**
     * Get color based on score
     */
    private function getScoreColor($score)
    {
        return match($score) {
            0 => 'green',    // No symptoms
            1 => 'yellow',   // Mild
            2 => 'orange',   // Moderate
            3 => 'red',      // Severe
            default => 'gray'
        };
    }

    /**
     * Get score level description
     */
    private function getScoreLevel($score)
    {
        return match($score) {
            0 => 'عادي',
            1 => 'لږ',
            2 => 'منځنی',
            3 => 'لوړ',
            default => 'نامعلوم'
        };
    }

    /**
     * Calculate summary statistics for questionnaire
     */
    private function calculateSummaryStatistics($questionnaireData, $detailedQuestions)
    {
        $totalQuestions = count($detailedQuestions);
        $totalScore = array_sum(array_column($detailedQuestions, 'score'));
        $maxScore = $totalQuestions * 3;
        $percentage = $maxScore > 0 ? round(($totalScore / $maxScore) * 100, 2) : 0;

        // Determine assessment level
        $assessmentLevel = 'عادي';
        if ($percentage >= 75) {
            $assessmentLevel = 'لوړ خطر';
        } elseif ($percentage >= 50) {
            $assessmentLevel = 'منځنی خطر';
        } elseif ($percentage >= 25) {
            $assessmentLevel = 'لږ خطر';
        }

        return array_merge($questionnaireData, [
            'total_questions' => $totalQuestions,
            'total_score' => $totalScore,
            'max_score' => $maxScore,
            'percentage' => $percentage,
            'assessment_level' => $assessmentLevel,
            'questions_answered' => $totalQuestions,
            'average_score' => $totalQuestions > 0 ? round($totalScore / $totalQuestions, 2) : 0
        ]);
    }
}
